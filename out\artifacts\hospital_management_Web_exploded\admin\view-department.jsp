<%@ page contentType="text/html;charset=UTF-8" language="java" %> <%@ page
import="com.hospital.entity.Admin" %> <% Admin admin = (Admin)
session.getAttribute("admin"); if (admin == null) {
response.sendRedirect(request.getContextPath() + "/login.jsp"); return; } String
deptId = request.getParameter("id"); if (deptId == null) { deptId = "1"; } %>
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>科室详情 - 医院管理系统</title>
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 0;
        padding: 0;
        background: #f5f5f5;
      }
      .header {
        background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
        color: white;
        padding: 20px 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .logo {
        font-size: 1.5em;
        font-weight: bold;
      }
      .nav-links a {
        color: white;
        text-decoration: none;
        margin-left: 20px;
        padding: 8px 16px;
        border-radius: 5px;
        transition: background 0.3s ease;
      }
      .nav-links a:hover {
        background: rgba(255, 255, 255, 0.2);
      }
      .container {
        max-width: 1000px;
        margin: 30px auto;
        padding: 0 20px;
      }
      .card {
        background: white;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      .card h2 {
        color: #333;
        margin: 0 0 20px 0;
        border-bottom: 2px solid #667eea;
        padding-bottom: 10px;
      }
      .dept-header {
        background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
      }
      .dept-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5em;
        margin: 0 auto 20px auto;
      }
      .dept-header h3 {
        margin: 0 0 10px 0;
        font-size: 2em;
      }
      .dept-header p {
        margin: 5px 0;
        opacity: 0.9;
        font-size: 1.1em;
      }
      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }
      .info-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
      }
      .info-section h4 {
        margin: 0 0 15px 0;
        color: #333;
        font-size: 1.2em;
      }
      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e1e5e9;
      }
      .info-item:last-child {
        margin-bottom: 0;
        border-bottom: none;
      }
      .info-label {
        font-weight: 500;
        color: #666;
        min-width: 100px;
      }
      .info-value {
        color: #333;
        flex: 1;
        text-align: right;
        font-weight: 500;
      }
      .description-section {
        grid-column: 1 / -1;
        background: #fff3e0;
      }
      .description-text {
        background: white;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e1e5e9;
        line-height: 1.6;
        color: #555;
      }
      .stats-section {
        grid-column: 1 / -1;
        background: #e8f5e8;
      }
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }
      .stat-item {
        background: white;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid #e1e5e9;
      }
      .stat-number {
        font-size: 1.8em;
        font-weight: bold;
        color: #4caf50;
        margin-bottom: 5px;
      }
      .stat-label {
        color: #666;
        font-size: 0.9em;
      }
      .btn {
        padding: 12px 30px;
        border: none;
        border-radius: 8px;
        font-size: 1.1em;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        margin-right: 15px;
      }
      .btn-secondary {
        background: #6c757d;
        color: white;
      }
      .btn-secondary:hover {
        background: #5a6268;
      }
      .btn-warning {
        background: #ffc107;
        color: #212529;
      }
      .btn-warning:hover {
        background: #e0a800;
      }
      .btn-info {
        background: #17a2b8;
        color: white;
      }
      .btn-info:hover {
        background: #138496;
      }
      .loading {
        text-align: center;
        padding: 40px;
        color: #666;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="header-content">
        <div class="logo">🏥 医院管理系统</div>
        <div class="nav-links">
          <a href="${pageContext.request.contextPath}/admin/departments.jsp"
            >返回科室管理</a
          >
          <a href="${pageContext.request.contextPath}/admin/index.jsp"
            >返回首页</a
          >
          <a href="${pageContext.request.contextPath}/LogoutServlet"
            >退出登录</a
          >
        </div>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <h2>科室详情</h2>

        <div id="loadingDiv" class="loading">正在加载科室信息...</div>

        <div id="deptContent" style="display: none">
          <div class="dept-header">
            <div class="dept-icon">🏢</div>
            <h3 id="deptName">-</h3>
            <p id="deptBasicInfo">-</p>
            <p id="deptLocation">位置：-</p>
            <p id="createTime">创建时间：-</p>
          </div>

          <div class="info-grid">
            <div class="info-section">
              <h4>基本信息</h4>
              <div class="info-item">
                <span class="info-label">科室名称</span>
                <span class="info-value" id="name">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">科室编号</span>
                <span class="info-value" id="deptId">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">科室位置</span>
                <span class="info-value" id="location">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">联系电话</span>
                <span class="info-value" id="phone">-</span>
              </div>
            </div>

            <div class="info-section">
              <h4>时间信息</h4>
              <div class="info-item">
                <span class="info-label">创建时间</span>
                <span class="info-value" id="createDate">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">最后更新</span>
                <span class="info-value" id="updateTime">-</span>
              </div>
              <div class="info-item">
                <span class="info-label">科室状态</span>
                <span class="info-value" id="status">正常运营</span>
              </div>
            </div>

            <div class="info-section stats-section">
              <h4>科室统计</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-number" id="doctorCount">-</div>
                  <div class="stat-label">在职医生</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number" id="patientCount">-</div>
                  <div class="stat-label">本月接诊</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number" id="registrationCount">-</div>
                  <div class="stat-label">总挂号数</div>
                </div>
              </div>
            </div>

            <div
              class="info-section description-section"
              id="descriptionSection"
            >
              <h4>科室介绍</h4>
              <div class="description-text" id="descriptionText">-</div>
            </div>
          </div>

          <div style="text-align: center; margin-top: 30px">
            <a
              href="${pageContext.request.contextPath}/admin/dept-doctors.jsp?deptId=<%= deptId %>"
              class="btn btn-info"
              >查看科室医生</a
            >
            <a
              href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=<%= deptId %>"
              class="btn btn-warning"
              >编辑科室信息</a
            >
            <a
              href="${pageContext.request.contextPath}/admin/departments.jsp"
              class="btn btn-secondary"
              >返回科室列表</a
            >
          </div>
        </div>

        <div
          id="errorDiv"
          style="
            display: none;
            text-align: center;
            padding: 40px;
            color: #dc3545;
          "
        >
          <h3>加载失败</h3>
          <p>无法加载科室信息，请检查科室ID是否正确。</p>
          <a
            href="${pageContext.request.contextPath}/admin/departments.jsp"
            class="btn btn-secondary"
            >返回科室列表</a
          >
        </div>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const deptId = "<%= deptId %>";
        loadDeptData(deptId);
      });

      function loadDeptData(deptId) {
        // 调用后端API获取真实科室数据
        fetch(
          "${pageContext.request.contextPath}/DepartmentDetailServlet?id=" +
            deptId
        )
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              displayDeptData(data);
              // 加载真实的统计数据
              loadDeptStats(deptId);
            } else {
              console.error("获取科室信息失败:", data.error);
              showError();
            }
          })
          .catch((error) => {
            console.error("请求失败:", error);
            showError();
          });
      }

      function loadDeptStats(deptId) {
        fetch(
          "${pageContext.request.contextPath}/DepartmentStatsServlet?deptId=" +
            deptId
        )
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              // 更新统计数据
              document.getElementById("doctorCount").textContent =
                data.doctorCount;
              document.getElementById("patientCount").textContent =
                data.monthlyRegistrations;
              document.getElementById("registrationCount").textContent =
                data.totalRegistrations;
            } else {
              console.error("获取统计数据失败:", data.error);
              // 显示错误状态
              document.getElementById("doctorCount").textContent = "错误";
              document.getElementById("patientCount").textContent = "错误";
              document.getElementById("registrationCount").textContent = "错误";
            }
          })
          .catch((error) => {
            console.error("请求失败:", error);
            // 显示错误状态
            document.getElementById("doctorCount").textContent = "错误";
            document.getElementById("patientCount").textContent = "错误";
            document.getElementById("registrationCount").textContent = "错误";
          });
      }

      function displayDeptData(dept) {
        document.getElementById("loadingDiv").style.display = "none";
        document.getElementById("deptContent").style.display = "block";

        // 更新科室信息
        document.getElementById("deptName").textContent = dept.deptName;
        document.getElementById("deptBasicInfo").textContent =
          "科室编号：" + dept.id;
        document.getElementById("deptLocation").textContent =
          "位置：" + dept.location;
        document.getElementById("createTime").textContent =
          "创建时间：" + dept.createTime;

        // 更新详细信息
        document.getElementById("name").textContent = dept.deptName;
        document.getElementById("deptId").textContent = dept.id;
        document.getElementById("location").textContent = dept.location;
        document.getElementById("phone").textContent = dept.phone;
        document.getElementById("createDate").textContent = dept.createTime;
        document.getElementById("updateTime").textContent =
          dept.updateTime || "未更新";

        // 统计数据将通过loadDeptStats函数从后端API获取

        // 显示科室介绍
        if (dept.deptDesc && dept.deptDesc.trim()) {
          document.getElementById("descriptionText").textContent =
            dept.deptDesc;
        } else {
          document.getElementById("descriptionText").textContent =
            "暂无科室介绍";
        }
      }

      function showError() {
        document.getElementById("loadingDiv").style.display = "none";
        document.getElementById("errorDiv").style.display = "block";
      }
    </script>
  </body>
</html>
