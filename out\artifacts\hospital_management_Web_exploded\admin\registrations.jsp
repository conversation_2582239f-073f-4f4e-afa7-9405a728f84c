<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.Registration" %>
<%@ page import="com.hospital.service.RegistrationService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    // 获取服务
    RegistrationService registrationService = JspServiceUtil.getRegistrationService(application);
    
    List<Registration> registrations = registrationService.findAllWithDetails();
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>挂号管理 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .stats-bar {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stat-item h4 {
            margin: 0 0 5px 0;
            font-size: 1.8em;
        }
        
        .stat-item p {
            margin: 0;
            opacity: 0.9;
        }
        
        .filter-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-bar select,
        .filter-bar input {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1em;
        }
        
        .filter-bar select:focus,
        .filter-bar input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            font-size: 1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .status-registered {
            background: #d4edda;
            color: #155724;
        }
        
        .status-visited {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .reg-no {
            font-family: monospace;
            color: #667eea;
            font-weight: bold;
        }
        
        .patient-info {
            font-weight: bold;
            color: #333;
        }
        
        .doctor-info {
            color: #666;
            font-size: 0.9em;
        }
        
        .symptoms {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            color: #ccc;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .table-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>挂号管理</h2>
            
            <%
                String success = request.getParameter("success");
                if (success != null) {
                    String message = "";
                    switch (success) {
                        case "1":
                            message = "✅ 挂号记录创建成功！";
                            break;
                        case "2":
                            message = "✅ 挂号记录修改成功！";
                            break;
                        case "3":
                            message = "✅ 挂号记录删除成功！";
                            break;
                        default:
                            message = "✅ 操作成功！";
                            break;
                    }
            %>
                <div class="alert alert-success">
                    <%= message %>
                </div>
            <% } %>
            
            <% if (request.getParameter("error") != null) { %>
                <div class="alert alert-error">
                    <%= request.getParameter("error") %>
                </div>
            <% } %>
            
            <% 
                int totalCount = registrations.size();
                int registeredCount = 0;
                int visitedCount = 0;
                int cancelledCount = 0;
                
                for (Registration reg : registrations) {
                    String status = reg.getStatus();
                    if ("已挂号".equals(status)) registeredCount++;
                    else if ("已就诊".equals(status)) visitedCount++;
                    else if ("已取消".equals(status)) cancelledCount++;
                }
            %>
            
            <div class="stats-bar">
                <div class="stat-item">
                    <h4><%= totalCount %></h4>
                    <p>挂号总数</p>
                </div>
                <div class="stat-item">
                    <h4><%= registeredCount %></h4>
                    <p>待就诊</p>
                </div>
                <div class="stat-item">
                    <h4><%= visitedCount %></h4>
                    <p>已就诊</p>
                </div>
                <div class="stat-item">
                    <h4><%= cancelledCount %></h4>
                    <p>已取消</p>
                </div>
            </div>
            
            <div class="filter-bar">
                <label>状态筛选：</label>
                <select id="statusFilter" onchange="filterTable()">
                    <option value="">全部状态</option>
                    <option value="已挂号">已挂号</option>
                    <option value="已就诊">已就诊</option>
                    <option value="已取消">已取消</option>
                </select>
                
                <label>挂号日期：</label>
                <input type="date" id="dateFilter" onchange="filterTable()">
                
                <label>患者姓名：</label>
                <input type="text" id="nameFilter" placeholder="输入患者姓名" onkeyup="filterTable()">
                
                <label>医生姓名：</label>
                <input type="text" id="doctorFilter" placeholder="输入医生姓名" onkeyup="filterTable()">
            </div>
            
            <% if (registrations != null && !registrations.isEmpty()) { %>
                <div class="table-container">
                    <table class="table" id="registrationTable">
                        <thead>
                            <tr>
                                <th>挂号单号</th>
                                <th>患者信息</th>
                                <th>医生信息</th>
                                <th>科室</th>
                                <th>挂号日期</th>
                                <th>挂号时间</th>
                                <th>挂号费</th>
                                <th>状态</th>
                                <th>主要症状</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% for (Registration reg : registrations) { %>
                                <tr data-status="<%= reg.getStatus() %>" 
                                    data-date="<%= dateFormat.format(reg.getRegDate()) %>" 
                                    data-patient="<%= reg.getPatientName() != null ? reg.getPatientName().toLowerCase() : "" %>"
                                    data-doctor="<%= reg.getDoctorName() != null ? reg.getDoctorName().toLowerCase() : "" %>">
                                    <td class="reg-no"><%= reg.getRegNo() %></td>
                                    <td class="patient-info"><%= reg.getPatientName() != null ? reg.getPatientName() : "未知" %></td>
                                    <td>
                                        <div class="patient-info"><%= reg.getDoctorName() != null ? reg.getDoctorName() : "未知" %></div>
                                    </td>
                                    <td><%= reg.getDeptName() != null ? reg.getDeptName() : "未知" %></td>
                                    <td><%= dateFormat.format(reg.getRegDate()) %></td>
                                    <td><%= timeFormat.format(reg.getRegTime()) %></td>
                                    <td>¥<%= reg.getRegFee() %></td>
                                    <td>
                                        <% 
                                            String status = reg.getStatus();
                                            String statusClass = "";
                                            if ("已挂号".equals(status)) {
                                                statusClass = "status-registered";
                                            } else if ("已就诊".equals(status)) {
                                                statusClass = "status-visited";
                                            } else if ("已取消".equals(status)) {
                                                statusClass = "status-cancelled";
                                            }
                                        %>
                                        <span class="status <%= statusClass %>"><%= status %></span>
                                    </td>
                                    <td class="symptoms" title="<%= reg.getSymptoms() != null ? reg.getSymptoms() : "" %>">
                                        <%= reg.getSymptoms() != null ? reg.getSymptoms() : "-" %>
                                    </td>
                                    <td><%= reg.getCreateTime() != null ? dateFormat.format(reg.getCreateTime()) : "-" %></td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <div class="empty-state">
                    <div style="font-size: 4em; margin-bottom: 20px;">📋</div>
                    <h3>暂无挂号记录</h3>
                    <p>系统中还没有挂号记录。</p>
                </div>
            <% } %>
        </div>
    </div>
    
    <script>
        function filterTable() {
            const statusFilter = document.getElementById('statusFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;
            const nameFilter = document.getElementById('nameFilter').value.toLowerCase();
            const doctorFilter = document.getElementById('doctorFilter').value.toLowerCase();
            const table = document.getElementById('registrationTable');
            
            if (!table) return;
            
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const status = row.getAttribute('data-status');
                const date = row.getAttribute('data-date');
                const patient = row.getAttribute('data-patient');
                const doctor = row.getAttribute('data-doctor');
                
                let showRow = true;
                
                // 状态筛选
                if (statusFilter && status !== statusFilter) {
                    showRow = false;
                }
                
                // 日期筛选
                if (dateFilter && date !== dateFilter) {
                    showRow = false;
                }
                
                // 患者姓名筛选
                if (nameFilter && !patient.includes(nameFilter)) {
                    showRow = false;
                }
                
                // 医生姓名筛选
                if (doctorFilter && !doctor.includes(doctorFilter)) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
            }
        }
        

    </script>
</body>
</html>
