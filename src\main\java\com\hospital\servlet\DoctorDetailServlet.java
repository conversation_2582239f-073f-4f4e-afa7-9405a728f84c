package com.hospital.servlet;

import com.hospital.entity.Doctor;
import com.hospital.entity.Department;
import com.hospital.service.DoctorService;
import com.hospital.service.DepartmentService;
import com.hospital.ioc.BeanFactory;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

/**
 医生详情API
 */
@WebServlet("/DoctorDetailServlet")
public class DoctorDetailServlet extends HttpServlet {
    
    private DoctorService doctorService;
    private DepartmentService departmentService;
    
    @Override
    public void init() throws ServletException {
        super.init();
        BeanFactory beanFactory = BeanFactory.getInstance();
        doctorService = beanFactory.getApplicationContext().getBean(DoctorService.class);
        departmentService = beanFactory.getApplicationContext().getBean(DepartmentService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        String doctorIdStr = request.getParameter("id");
        if (doctorIdStr == null || doctorIdStr.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "医生ID不能为空");
            return;
        }
        
        try {
            Integer doctorId = Integer.parseInt(doctorIdStr);
            Doctor doctor = doctorService.findById(doctorId);
            
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> result = new HashMap<>();
            
            if (doctor != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                
                // 获取科室信息
                String deptName = "未分配";
                if (doctor.getDeptId() != null) {
                    Department department = departmentService.findById(doctor.getDeptId());
                    if (department != null) {
                        deptName = department.getDeptName();
                    }
                }
                
                result.put("success", true);
                result.put("id", doctor.getId());
                result.put("name", doctor.getName());
                result.put("gender", doctor.getGender());
                result.put("age", doctor.getAge());
                result.put("phone", doctor.getPhone());
                result.put("email", doctor.getEmail());
                result.put("doctorNo", doctor.getDoctorNo());
                result.put("deptId", doctor.getDeptId());
                result.put("deptName", deptName);
                result.put("title", doctor.getTitle());
                result.put("status", doctor.getStatus());
                result.put("speciality", doctor.getSpeciality());
                result.put("createTime", doctor.getCreateTime() != null ? dateFormat.format(doctor.getCreateTime()) : null);
                result.put("updateTime", doctor.getUpdateTime() != null ? dateFormat.format(doctor.getUpdateTime()) : null);
            } else {
                result.put("success", false);
                result.put("error", "医生不存在");
            }
            
            String jsonResponse = mapper.writeValueAsString(result);
            response.getWriter().write(jsonResponse);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "无效的医生ID");
        } catch (Exception e) {
            e.printStackTrace();
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "获取医生信息失败: " + e.getMessage());
            
            ObjectMapper mapper = new ObjectMapper();
            String jsonResponse = mapper.writeValueAsString(errorResponse);
            response.getWriter().write(jsonResponse);
        }
    }
}
