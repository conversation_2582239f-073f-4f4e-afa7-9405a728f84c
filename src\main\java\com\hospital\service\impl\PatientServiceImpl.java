package com.hospital.service.impl;

import com.hospital.dao.PatientDao;
import com.hospital.entity.Patient;
import com.hospital.service.PatientService;
import com.hospital.util.TransactionManager;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 患者服务实现类
 * 实现PatientService接口，提供患者相关业务逻辑的具体实现。
 * 该类负责处理患者注册、登录、信息管理等核心业务功能。
 * 主要职责：
 * 1. 患者注册业务逻辑：数据验证、唯一性检查、编号生成
 * 2. 患者登录验证：凭据验证、密码匹配
 * 3. 患者信息管理：查询、更新、删除操作
 * 4. 业务规则实施：确保数据完整性和业务约束
 * 5. 事务管理：保证数据操作的一致性
 */
public class PatientServiceImpl implements PatientService {

    /**
     患者数据访问对象
     */
    private PatientDao patientDao;

    /**
     设置患者DAO
     依赖注入方法，由IoC容器调用设置PatientDao实例
     */
    public void setPatientDao(PatientDao patientDao) {
        this.patientDao = patientDao;
    }
    
    /**
     * 患者注册
     * 实现患者注册的完整业务流程，包括数据验证、唯一性检查和数据持久化。
     * 使用事务管理确保注册过程的数据一致性。
     * 业务流程：
     * 1. 验证患者编号的唯一性
     * 2. 验证身份证号的唯一性（如果提供）
     * 3. 将患者信息插入数据库
     * 4. 返回注册结果
     */
    @Override
    public boolean register(Patient patient) {
        return TransactionManager.executeInTransaction(() -> {
            // 检查患者编号是否已存在，确保编号唯一性
            if (isPatientNoExists(patient.getPatientNo())) {
                return false;
            }

            // 检查身份证号是否已存在，确保身份证号唯一性
            if (patient.getIdCard() != null && isIdCardExists(patient.getIdCard())) {
                return false;
            }

            // 插入患者数据到数据库
            return patientDao.insert(patient) > 0;
        });
    }

    /**
     患者登录
     验证患者的登录凭据，通过患者编号和密码进行身份验证。
     */
    @Override
    public Patient login(String patientNo, String password) {
        return patientDao.login(patientNo, password);
    }

    /**
     * 根据ID查询患者
     * 通过患者主键ID查询患者的详细信息。
     */
    @Override
    public Patient findById(Integer id) {
        return patientDao.findById(id);
    }
    
    @Override
    public Patient findByPatientNo(String patientNo) {
        return patientDao.findByPatientNo(patientNo);
    }
    
    @Override
    public boolean updatePatient(Patient patient) {
        return patientDao.update(patient) > 0;
    }
    
    @Override
    public boolean changePassword(Integer id, String oldPassword, String newPassword) {
        Patient patient = patientDao.findById(id);
        if (patient == null || !patient.getPassword().equals(oldPassword)) {
            return false;
        }
        return patientDao.updatePassword(id, newPassword) > 0;
    }
    
    @Override
    public List<Patient> findAll() {
        return patientDao.findAll();
    }
    
    @Override
    public List<Patient> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return patientDao.findByPage(offset, size);
    }
    
    @Override
    public List<Patient> findByNameLike(String name) {
        return patientDao.findByNameLike(name);
    }
    
    @Override
    public int count() {
        return patientDao.count();
    }

    @Override
    public String generatePatientNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());

        // 查询当天已有的患者数量
        int count = patientDao.count();

        // 生成患者编号：P + 日期 + 4位序号
        return String.format("P%s%04d", dateStr, count + 1);
    }

    @Override
    public boolean isPatientNoExists(String patientNo) {
        return patientDao.findByPatientNo(patientNo) != null;
    }
    
    @Override
    public boolean isIdCardExists(String idCard) {
        return patientDao.findByIdCard(idCard) != null;
    }
}
