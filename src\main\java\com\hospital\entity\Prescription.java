package com.hospital.entity;

import java.util.Date;
import java.util.List;

/**
 * 处方实体类
 * 用于管理医生开具的处方信息
 * 数据库对应表：prescription
 */
public class Prescription {
    
    private Integer id;
    private String prescriptionNo;
    private Integer medicalRecordId;
    private Integer patientId;
    private Integer doctorId;
    private String prescriptionType; // 中药处方、西药处方、中西药混合
    private String diagnosis;
    private Date prescriptionDate;
    private String status; // 已开具、已发药、已取消
    private String notes;
    private Date createTime;
    private Date updateTime;
    
    // 非数据库字段，用于显示
    private String patientName;
    private String doctorName;
    private String departmentName;
    private List<PrescriptionDetail> prescriptionDetails; // 处方明细
    private ChinesePrescriptionAttr chinesePrescriptionAttr; // 中药处方特殊属性
    
    public Prescription() {}
    
    public Prescription(String prescriptionNo, Integer medicalRecordId, Integer patientId, 
                       Integer doctorId, String prescriptionType, String diagnosis) {
        this.prescriptionNo = prescriptionNo;
        this.medicalRecordId = medicalRecordId;
        this.patientId = patientId;
        this.doctorId = doctorId;
        this.prescriptionType = prescriptionType;
        this.diagnosis = diagnosis;
        this.prescriptionDate = new Date();
        this.status = "已开具";
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getPrescriptionNo() {
        return prescriptionNo;
    }
    
    public void setPrescriptionNo(String prescriptionNo) {
        this.prescriptionNo = prescriptionNo;
    }
    
    public Integer getMedicalRecordId() {
        return medicalRecordId;
    }
    
    public void setMedicalRecordId(Integer medicalRecordId) {
        this.medicalRecordId = medicalRecordId;
    }
    
    public Integer getPatientId() {
        return patientId;
    }
    
    public void setPatientId(Integer patientId) {
        this.patientId = patientId;
    }
    
    public Integer getDoctorId() {
        return doctorId;
    }
    
    public void setDoctorId(Integer doctorId) {
        this.doctorId = doctorId;
    }
    
    public String getPrescriptionType() {
        return prescriptionType;
    }
    
    public void setPrescriptionType(String prescriptionType) {
        this.prescriptionType = prescriptionType;
    }
    
    public String getDiagnosis() {
        return diagnosis;
    }
    
    public void setDiagnosis(String diagnosis) {
        this.diagnosis = diagnosis;
    }
    
    public Date getPrescriptionDate() {
        return prescriptionDate;
    }
    
    public void setPrescriptionDate(Date prescriptionDate) {
        this.prescriptionDate = prescriptionDate;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getPatientName() {
        return patientName;
    }
    
    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }
    
    public String getDoctorName() {
        return doctorName;
    }
    
    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }
    
    public String getDepartmentName() {
        return departmentName;
    }
    
    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }
    
    public List<PrescriptionDetail> getPrescriptionDetails() {
        return prescriptionDetails;
    }
    
    public void setPrescriptionDetails(List<PrescriptionDetail> prescriptionDetails) {
        this.prescriptionDetails = prescriptionDetails;
    }
    
    public ChinesePrescriptionAttr getChinesePrescriptionAttr() {
        return chinesePrescriptionAttr;
    }
    
    public void setChinesePrescriptionAttr(ChinesePrescriptionAttr chinesePrescriptionAttr) {
        this.chinesePrescriptionAttr = chinesePrescriptionAttr;
    }
    
    @Override
    public String toString() {
        return "Prescription{" +
                "id=" + id +
                ", prescriptionNo='" + prescriptionNo + '\'' +
                ", medicalRecordId=" + medicalRecordId +
                ", patientId=" + patientId +
                ", doctorId=" + doctorId +
                ", prescriptionType='" + prescriptionType + '\'' +
                ", diagnosis='" + diagnosis + '\'' +
                ", prescriptionDate=" + prescriptionDate +
                ", status='" + status + '\'' +
                ", patientName='" + patientName + '\'' +
                ", doctorName='" + doctorName + '\'' +
                '}';
    }
}
