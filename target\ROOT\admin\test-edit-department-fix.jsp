<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>编辑科室功能测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .nav-links a { display: inline-block; margin-right: 15px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
        .nav-links a:hover { background: #5a6fd8; }
        .test-link { display: inline-block; margin: 5px; padding: 8px 15px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; }
        .test-link:hover { background: #218838; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🏥 编辑科室功能测试</h1>
            
            <div class="success">
                <h2>✅ 测试结果：编辑科室页面已修复！</h2>
                <p>edit-department.jsp文件已成功部署到运行时目录，404错误已解决。</p>
            </div>
            
            <div class="info">
                <h3>📋 管理员信息</h3>
                <p><strong>管理员用户名:</strong> <%= admin.getUsername() %></p>
                <p><strong>管理员姓名:</strong> <%= admin.getName() != null ? admin.getName() : "未设置" %></p>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
            </div>
            
            <div class="info">
                <h3>🔧 修复内容</h3>
                <ul>
                    <li>✅ edit-department.jsp - 编辑科室信息页面</li>
                    <li>✅ 文件已部署到target/ROOT/admin/目录</li>
                    <li>✅ 文件已备份到WebContent/admin/目录</li>
                    <li>✅ 使用JDBC直接连接数据库，避免Spring依赖</li>
                    <li>✅ 包含完整的表单验证和错误处理</li>
                </ul>
            </div>
            
            <div class="nav-links">
                <h3>🚀 功能测试</h3>
                <a href="${pageContext.request.contextPath}/admin/departments.jsp">科室管理</a>
                <a href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=1" class="test-link">测试编辑科室1</a>
                <a href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=2" class="test-link">测试编辑科室2</a>
                <a href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=3" class="test-link">测试编辑科室3</a>
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回管理员首页</a>
            </div>
            
            <div class="info">
                <h3>💡 使用说明</h3>
                <p>现在您可以：</p>
                <ol>
                    <li><strong>访问科室管理</strong> - 点击"科室管理"查看所有科室</li>
                    <li><strong>编辑科室信息</strong> - 在科室列表中点击"编辑"按钮</li>
                    <li><strong>修改科室数据</strong> - 修改科室名称、位置、电话、介绍等信息</li>
                    <li><strong>保存修改</strong> - 点击"保存修改"按钮提交更改</li>
                    <li><strong>测试链接</strong> - 点击上方的测试链接直接测试编辑功能</li>
                </ol>
            </div>
            
            <div class="info">
                <h3>📋 编辑科室功能特性</h3>
                <ul>
                    <li>🔍 <strong>数据加载</strong>: 自动加载现有科室信息</li>
                    <li>📝 <strong>表单编辑</strong>: 支持修改科室名称、位置、电话、介绍</li>
                    <li>✅ <strong>数据验证</strong>: 科室名称必填验证</li>
                    <li>💾 <strong>保存确认</strong>: 提交前确认对话框</li>
                    <li>🔧 <strong>调试信息</strong>: 显示详细的调试信息</li>
                    <li>🚫 <strong>错误处理</strong>: 完善的错误信息显示</li>
                    <li>🔄 <strong>状态反馈</strong>: 成功/失败消息提示</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>🔧 技术信息</h3>
                <ul>
                    <li><strong>数据库连接</strong>: 直接使用JDBC连接MySQL</li>
                    <li><strong>Spring依赖</strong>: 无需Spring框架</li>
                    <li><strong>文件位置</strong>: target/ROOT/admin/edit-department.jsp</li>
                    <li><strong>表单提交</strong>: POST到UpdateDepartmentServlet</li>
                    <li><strong>参数验证</strong>: 科室ID和必填字段验证</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>🎯 测试步骤</h3>
                <ol>
                    <li><strong>点击测试链接</strong> - 选择上方任一测试链接</li>
                    <li><strong>查看页面加载</strong> - 确认页面正常显示，无404错误</li>
                    <li><strong>检查数据加载</strong> - 确认科室信息正确显示在表单中</li>
                    <li><strong>修改信息</strong> - 尝试修改科室名称或其他信息</li>
                    <li><strong>提交保存</strong> - 点击"保存修改"测试提交功能</li>
                    <li><strong>验证结果</strong> - 检查修改是否成功保存</li>
                </ol>
            </div>
            
            <div class="info">
                <h3>📞 故障排除</h3>
                <ul>
                    <li><strong>404错误</strong>: 已解决，文件已正确部署</li>
                    <li><strong>数据库连接</strong>: 使用localhost:3306/hospital_management</li>
                    <li><strong>权限检查</strong>: 需要管理员登录状态</li>
                    <li><strong>参数验证</strong>: 需要有效的科室ID参数</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
