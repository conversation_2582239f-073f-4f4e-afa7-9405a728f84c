<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="FacetManager">
    <facet type="web" name="Web3">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/WebContent/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/WebContent" relative="/" />
        </webroots>
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
          <root url="file://$MODULE_DIR$/target/generated-sources/annotations" />
        </sourceRoots>
      </configuration>
    </facet>
    <facet type="web" name="Web2">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/out/artifacts/hospital_management_Web_exploded/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/out/artifacts/hospital_management_Web_exploded" relative="/" />
        </webroots>
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
          <root url="file://$MODULE_DIR$/target/generated-sources/annotations" />
        </sourceRoots>
      </configuration>
    </facet>
    <facet type="web" name="Web5">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/docs/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/docs" relative="/" />
        </webroots>
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
          <root url="file://$MODULE_DIR$/target/generated-sources/annotations" />
        </sourceRoots>
      </configuration>
    </facet>
    <facet type="web" name="Web4">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/manager/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/manager" relative="/" />
        </webroots>
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
          <root url="file://$MODULE_DIR$/target/generated-sources/annotations" />
        </sourceRoots>
      </configuration>
    </facet>
    <facet type="web" name="Web7">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/ROOT/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/ROOT" relative="/" />
        </webroots>
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
          <root url="file://$MODULE_DIR$/target/generated-sources/annotations" />
        </sourceRoots>
      </configuration>
    </facet>
    <facet type="web" name="Web10">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/conf/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/conf" relative="/WEB-INF" />
        </webroots>
      </configuration>
    </facet>
    <facet type="web" name="Web6">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/docs/appdev/sample/web/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/docs/appdev/sample/web" relative="/" />
        </webroots>
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
          <root url="file://$MODULE_DIR$/target/generated-sources/annotations" />
        </sourceRoots>
      </configuration>
    </facet>
    <facet type="web" name="Web9">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/examples/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/examples" relative="/" />
        </webroots>
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
          <root url="file://$MODULE_DIR$/target/generated-sources/annotations" />
        </sourceRoots>
      </configuration>
    </facet>
    <facet type="web" name="Web8">
      <configuration>
        <descriptors>
          <deploymentDescriptor name="web.xml" url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/host-manager/WEB-INF/web.xml" />
        </descriptors>
        <webroots>
          <root url="file://$MODULE_DIR$/apache-tomcat-9.0.100-windows-x64/webapps/host-manager" relative="/" />
        </webroots>
        <sourceRoots>
          <root url="file://$MODULE_DIR$/src/main/java" />
          <root url="file://$MODULE_DIR$/src/main/resources" />
          <root url="file://$MODULE_DIR$/target/generated-sources/annotations" />
        </sourceRoots>
      </configuration>
    </facet>
  </component>
</module>