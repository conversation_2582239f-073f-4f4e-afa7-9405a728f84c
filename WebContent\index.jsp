<%@ page contentType="text/html;charset=UTF-8" language="java" %> <% // 调试信息
System.out.println("访问首页 - Context Path: " + request.getContextPath());
System.out.println("访问首页 - Request URI: " + request.getRequestURI());
System.out.println("访问首页 - Request URL: " + request.getRequestURL()); %>
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>医院挂号就诊管理系统</title>
<%--    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css"/>--%>
    <link rel="stylesheet" href="css/style.css"/>
    <style>
      body {
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow-x: hidden;
      }

      /* 背景装饰 */
      body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        pointer-events: none;
      }

      .main-container {
        background: var(--surface-color);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        padding: var(--spacing-2xl);
        text-align: center;
        max-width: 700px;
        width: 90%;
        position: relative;
        z-index: 1;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .logo-section {
        margin-bottom: var(--spacing-xl);
      }

      .logo {
        font-size: 3rem;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: var(--spacing-sm);
        font-weight: 800;
        letter-spacing: -1px;
      }

      .subtitle {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-md);
        font-size: 1.125rem;
        font-weight: 500;
      }

      .tagline {
        color: var(--text-muted);
        font-size: 0.875rem;
        margin-bottom: var(--spacing-xl);
      }

      .btn-group {
        display: flex;
        justify-content: center;
        gap: var(--spacing-md);
        flex-wrap: wrap;
        margin-bottom: var(--spacing-2xl);
      }

      .btn-hero {
        padding: var(--spacing-md) var(--spacing-xl);
        border: none;
        border-radius: var(--radius-lg);
        font-size: 1.125rem;
        font-weight: 600;
        cursor: pointer;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        transition: all 0.3s ease;
        min-width: 160px;
        justify-content: center;
        position: relative;
        overflow: hidden;
      }

      .btn-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
      }

      .btn-hero:hover::before {
        left: 100%;
      }

      .btn-primary-hero {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        color: white;
        box-shadow: 0 4px 15px rgba(13, 115, 119, 0.3);
      }

      .btn-primary-hero:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(13, 115, 119, 0.4);
      }

      .btn-secondary-hero {
        background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
        color: white;
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
      }

      .btn-secondary-hero:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
      }

      .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
        margin-top: var(--spacing-xl);
      }

      .feature {
        padding: var(--spacing-lg);
        background: linear-gradient(135deg, var(--surface-color), var(--border-light));
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .feature::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      }

      .feature:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
      }

      .feature-icon {
        font-size: 2.5rem;
        margin-bottom: var(--spacing-md);
        display: block;
      }

      .feature h3 {
        margin: 0 0 var(--spacing-sm) 0;
        color: var(--text-primary);
        font-size: 1.125rem;
        font-weight: 600;
      }

      .feature p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 0.875rem;
        line-height: 1.5;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .main-container {
          padding: var(--spacing-lg);
          margin: var(--spacing-md);
        }

        .logo {
          font-size: 2.5rem;
        }

        .btn-group {
          flex-direction: column;
          align-items: center;
        }

        .btn-hero {
          width: 100%;
          max-width: 300px;
        }

        .features {
          grid-template-columns: 1fr;
          gap: var(--spacing-md);
        }
      }
    </style>

  </head>
  <body>
    <div class="main-container">
      <div class="logo-section">
        <div class="logo">🏥 智慧医疗</div>
        <div class="subtitle">Hospital Management System</div>
        <div class="tagline">现代化医院管理解决方案</div>
      </div>

      <div class="btn-group">
        <a
          href="${pageContext.request.contextPath}/login.jsp"
          class="btn-hero btn-primary-hero"
        >
          <span>🔐</span>
          用户登录
        </a>
        <a
          href="${pageContext.request.contextPath}/register.jsp"
          class="btn-hero btn-secondary-hero"
        >
          <span>📝</span>
          患者注册
        </a>
      </div>

      <div class="features">
        <div class="feature">
          <span class="feature-icon">📅</span>
          <h3>在线挂号</h3>
          <p>便捷的在线挂号服务，支持多科室预约，实时查看医生排班信息</p>
        </div>
        <div class="feature">
          <span class="feature-icon">📋</span>
          <h3>就诊管理</h3>
          <p>完整的就诊记录管理和查询功能，电子病历一站式服务</p>
        </div>
        <div class="feature">
          <span class="feature-icon">👨‍⚕️</span>
          <h3>医生排班</h3>
          <p>实时查看医生排班信息和专业特长，智能匹配最适合的医生</p>
        </div>
        <div class="feature">
          <span class="feature-icon">🔒</span>
          <h3>系统安全</h3>
          <p>多角色权限管理，保障信息安全，符合医疗数据保护标准</p>
        </div>
      </div>
    </div>
  </body>
</html>
