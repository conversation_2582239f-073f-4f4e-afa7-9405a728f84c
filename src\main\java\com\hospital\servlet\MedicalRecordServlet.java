package com.hospital.servlet;

import com.hospital.entity.Doctor;
import com.hospital.entity.MedicalRecord;
import com.hospital.entity.Registration;
import com.hospital.service.MedicalRecordService;
import com.hospital.service.RegistrationService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 就诊记录控制器
 */
@WebServlet("/MedicalRecordServlet")
public class MedicalRecordServlet extends HttpServlet {
    
    private MedicalRecordService medicalRecordService;
    private RegistrationService registrationService;
    
    @Override
    public void init() throws ServletException {
        BeanFactory beanFactory = BeanFactory.getInstance();
        medicalRecordService = beanFactory.getApplicationContext().getBean(MedicalRecordService.class);
        registrationService = beanFactory.getApplicationContext().getBean(RegistrationService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            handleAdd(request, response);
        } else if ("update".equals(action)) {
            handleUpdate(request, response);
        } else {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "无效的操作");
        }
    }
    
    /**
     * 处理添加就诊记录请求
     */
    private void handleAdd(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession();
        Doctor doctor = (Doctor) session.getAttribute("doctor");
        
        if (doctor == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取表单参数
        String regIdStr = request.getParameter("regId");
        String chiefComplaint = request.getParameter("chiefComplaint");
        String presentIllness = request.getParameter("presentIllness");
        String physicalExam = request.getParameter("physicalExam");
        String diagnosis = request.getParameter("diagnosis");
        String treatmentPlan = request.getParameter("treatmentPlan");
        String prescription = request.getParameter("prescription");
        String advice = request.getParameter("advice");
        String nextVisitDateStr = request.getParameter("nextVisitDate");
        
        // 参数验证
        if (regIdStr == null || chiefComplaint == null || diagnosis == null) {
            request.setAttribute("error", "请填写必要的就诊信息");
            request.getRequestDispatcher("/doctor/add-medical-record.jsp").forward(request, response);
            return;
        }
        
        try {
            Integer regId = Integer.parseInt(regIdStr);
            
            // 验证挂号记录是否存在且属于当前医生
            Registration registration = registrationService.findById(regId);
            if (registration == null || !registration.getDoctorId().equals(doctor.getId())) {
                request.setAttribute("error", "无效的挂号记录");
                request.getRequestDispatcher("/doctor/add-medical-record.jsp").forward(request, response);
                return;
            }
            
            // 检查是否已有就诊记录
            MedicalRecord existingRecord = medicalRecordService.findByRegId(regId);
            if (existingRecord != null) {
                request.setAttribute("error", "该挂号记录已有就诊记录");
                request.getRequestDispatcher("/doctor/add-medical-record.jsp").forward(request, response);
                return;
            }
            
            // 创建就诊记录
            MedicalRecord medicalRecord = new MedicalRecord();
            medicalRecord.setRegId(regId);
            medicalRecord.setPatientId(registration.getPatientId());
            medicalRecord.setDoctorId(doctor.getId());
            medicalRecord.setChiefComplaint(chiefComplaint);
            medicalRecord.setPresentIllness(presentIllness);
            medicalRecord.setPhysicalExam(physicalExam);
            medicalRecord.setDiagnosis(diagnosis);
            medicalRecord.setTreatmentPlan(treatmentPlan);
            medicalRecord.setPrescription(prescription);
            medicalRecord.setAdvice(advice);
            
            // 处理下次复诊日期
            if (nextVisitDateStr != null && !nextVisitDateStr.trim().isEmpty()) {
                try {
                    Date nextVisitDate = new SimpleDateFormat("yyyy-MM-dd").parse(nextVisitDateStr);
                    medicalRecord.setNextVisitDate(nextVisitDate);
                } catch (Exception e) {
                    // 忽略日期解析错误
                }
            }
            
            // 保存就诊记录
            boolean success = medicalRecordService.addMedicalRecord(medicalRecord);
            
            if (success) {
                response.sendRedirect(request.getContextPath() + "/doctor/registrations.jsp?success=1");
            } else {
                request.setAttribute("error", "就诊记录添加失败");
                request.getRequestDispatcher("/doctor/add-medical-record.jsp").forward(request, response);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "系统错误：" + e.getMessage());
            request.getRequestDispatcher("/doctor/add-medical-record.jsp").forward(request, response);
        }
    }
    
    /**
     * 处理更新就诊记录请求
     */
    private void handleUpdate(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession();
        Doctor doctor = (Doctor) session.getAttribute("doctor");
        
        if (doctor == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取表单参数
        String idStr = request.getParameter("id");
        String chiefComplaint = request.getParameter("chiefComplaint");
        String presentIllness = request.getParameter("presentIllness");
        String physicalExam = request.getParameter("physicalExam");
        String diagnosis = request.getParameter("diagnosis");
        String treatmentPlan = request.getParameter("treatmentPlan");
        String prescription = request.getParameter("prescription");
        String advice = request.getParameter("advice");
        String nextVisitDateStr = request.getParameter("nextVisitDate");
        
        if (idStr == null) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "缺少就诊记录ID");
            return;
        }
        
        try {
            Integer id = Integer.parseInt(idStr);
            
            // 验证就诊记录是否存在且属于当前医生
            MedicalRecord medicalRecord = medicalRecordService.findById(id);
            if (medicalRecord == null || !medicalRecord.getDoctorId().equals(doctor.getId())) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "无权限操作此就诊记录");
                return;
            }
            
            // 更新就诊记录
            medicalRecord.setChiefComplaint(chiefComplaint);
            medicalRecord.setPresentIllness(presentIllness);
            medicalRecord.setPhysicalExam(physicalExam);
            medicalRecord.setDiagnosis(diagnosis);
            medicalRecord.setTreatmentPlan(treatmentPlan);
            medicalRecord.setPrescription(prescription);
            medicalRecord.setAdvice(advice);
            
            // 处理下次复诊日期
            if (nextVisitDateStr != null && !nextVisitDateStr.trim().isEmpty()) {
                try {
                    Date nextVisitDate = new SimpleDateFormat("yyyy-MM-dd").parse(nextVisitDateStr);
                    medicalRecord.setNextVisitDate(nextVisitDate);
                } catch (Exception e) {
                    medicalRecord.setNextVisitDate(null);
                }
            } else {
                medicalRecord.setNextVisitDate(null);
            }
            
            // 保存更新
            boolean success = medicalRecordService.updateMedicalRecord(medicalRecord);
            
            if (success) {
                response.sendRedirect(request.getContextPath() + "/doctor/medical-records.jsp?success=2");
            } else {
                request.setAttribute("error", "就诊记录更新失败");
                request.getRequestDispatcher("/doctor/edit-medical-record.jsp?id=" + id).forward(request, response);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "系统错误");
        }
    }
}
