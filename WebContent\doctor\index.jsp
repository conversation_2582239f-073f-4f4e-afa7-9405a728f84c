<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%
    Doctor doctor = (Doctor) session.getAttribute("doctor");
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>医生工作台 - 智慧医疗管理系统</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: var(--background-color);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
            color: white;
            padding: var(--spacing-lg) 0;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .logo {
            font-size: 1.75rem;
            font-weight: 800;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            background: rgba(255, 255, 255, 0.1);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.875rem;
        }

        .user-title {
            font-size: 0.75rem;
            opacity: 0.8;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .container {
            max-width: 1200px;
            margin: var(--spacing-xl) auto;
            padding: 0 var(--spacing-lg);
        }

        .welcome-card {
            background: linear-gradient(135deg, var(--surface-color), var(--border-light));
            border-radius: var(--radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .welcome-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .welcome-card h2 {
            color: var(--text-primary);
            margin: 0 0 var(--spacing-sm) 0;
            font-size: 1.875rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome-card p {
            color: var(--text-secondary);
            margin: 0;
            font-size: 1rem;
            line-height: 1.6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-md);
            font-size: 1.5rem;
            color: white;
            box-shadow: var(--shadow-md);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
            color: var(--text-primary);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 500;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-lg);
        }

        .menu-item {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            box-shadow: var(--shadow-md);
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .menu-item:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .menu-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-md);
            font-size: 2rem;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .menu-item h3 {
            margin: 0 0 var(--spacing-sm) 0;
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 600;
        }

        .menu-item p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: var(--spacing-md);
                text-align: center;
            }

            .user-info {
                flex-direction: column;
                gap: var(--spacing-sm);
            }

            .container {
                padding: 0 var(--spacing-md);
            }

            .welcome-card {
                padding: var(--spacing-lg);
            }

            .stats-grid,
            .menu-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">🏥</div>
                <span>智慧医疗工作台</span>
            </div>
            <div class="user-info">
                <div class="user-avatar">👨‍⚕️</div>
                <div class="user-details">
                    <div class="user-name"><%= doctor.getName() %> 医生</div>
                    <div class="user-title"><%= doctor.getTitle() != null ? doctor.getTitle() : "医师" %></div>
                </div>
                <a href="${pageContext.request.contextPath}/LogoutServlet" class="logout-btn">退出登录</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="welcome-card">
            <h2>医生工作台</h2>
            <p>
                欢迎使用智慧医疗管理系统，您可以在这里查看患者挂号、录入就诊记录、管理医疗档案等专业医疗服务操作。
            </p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">🏢</div>
                <div class="stat-number">
                    <%= doctor.getDeptName() != null ? doctor.getDeptName() : "未知科室" %>
                </div>
                <div class="stat-label">所属科室</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⭐</div>
                <div class="stat-number">
                    <%= doctor.getTitle() != null ? doctor.getTitle() : "医师" %>
                </div>
                <div class="stat-label">职业职称</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🩺</div>
                <div class="stat-number">专业</div>
                <div class="stat-label">医疗服务</div>
            </div>
        </div>

        <div class="menu-grid">
            <a href="${pageContext.request.contextPath}/doctor/registrations.jsp" class="menu-item">
                <div class="menu-icon">📋</div>
                <h3>患者挂号</h3>
                <p>查看和管理患者挂号信息，处理预约就诊安排</p>
            </a>

            <a href="${pageContext.request.contextPath}/doctor/medical-records.jsp" class="menu-item">
                <div class="menu-icon">📑</div>
                <h3>就诊记录</h3>
                <p>录入和查看患者就诊记录，管理医疗档案</p>
            </a>

            <a href="${pageContext.request.contextPath}/doctor/profile.jsp" class="menu-item">
                <div class="menu-icon">👤</div>
                <h3>个人信息</h3>
                <p>查看和修改个人基本信息，更新专业资料</p>
            </a>
        </div>
    </div>
</body>
</html>
