<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>编辑科室调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; }
        .nav-links a { display: inline-block; margin-right: 15px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🔧 编辑科室调试页面</h1>
            
            <div class="success">
                <h2>✅ 页面访问成功！</h2>
                <p>如果您能看到这个页面，说明JSP文件路径是正确的。</p>
            </div>
            
            <div class="debug-info">
                <h3>📋 系统调试信息</h3>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
                <p><strong>请求URI:</strong> <%= request.getRequestURI() %></p>
                <p><strong>Context Path:</strong> <%= request.getContextPath() %></p>
                <p><strong>Servlet Path:</strong> <%= request.getServletPath() %></p>
                <p><strong>Query String:</strong> <%= request.getQueryString() %></p>
                <p><strong>Server Name:</strong> <%= request.getServerName() %></p>
                <p><strong>Server Port:</strong> <%= request.getServerPort() %></p>
                <p><strong>Real Path:</strong> <%= application.getRealPath("/") %></p>
                <p><strong>Server Info:</strong> <%= application.getServerInfo() %></p>
            </div>
            
            <div class="info">
                <h3>🔍 文件存在性检查</h3>
                <%
                    String editDeptPath = application.getRealPath("/admin/edit-department.jsp");
                    java.io.File editDeptFile = new java.io.File(editDeptPath);
                %>
                <p><strong>edit-department.jsp 路径:</strong> <%= editDeptPath %></p>
                <p><strong>文件存在:</strong> <%= editDeptFile.exists() ? "✅ 是" : "❌ 否" %></p>
                <p><strong>文件可读:</strong> <%= editDeptFile.canRead() ? "✅ 是" : "❌ 否" %></p>
                <p><strong>文件大小:</strong> <%= editDeptFile.length() %> 字节</p>
                <p><strong>最后修改:</strong> <%= new java.util.Date(editDeptFile.lastModified()) %></p>
            </div>
            
            <div class="info">
                <h3>🌐 URL构建测试</h3>
                <%
                    String contextPath = request.getContextPath();
                    String editUrl = contextPath + "/admin/edit-department.jsp?id=7";
                    String fullUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + editUrl;
                %>
                <p><strong>相对URL:</strong> <%= editUrl %></p>
                <p><strong>完整URL:</strong> <%= fullUrl %></p>
                <p><strong>当前访问URL:</strong> <%= request.getRequestURL() %></p>
            </div>
            
            <div class="nav-links">
                <h3>🚀 测试链接</h3>
                <a href="<%= contextPath %>/admin/edit-department.jsp?id=1">测试编辑科室(ID=1)</a>
                <a href="<%= contextPath %>/admin/edit-department.jsp?id=7">测试编辑科室(ID=7)</a>
                <a href="<%= contextPath %>/admin/departments.jsp">返回科室管理</a>
            </div>
            
            <div class="info">
                <h3>💡 问题诊断</h3>
                <p>如果编辑科室页面仍然出现404错误，可能的原因：</p>
                <ul>
                    <li><strong>缓存问题:</strong> 浏览器或服务器缓存了旧的错误页面</li>
                    <li><strong>部署问题:</strong> 文件没有正确部署到运行目录</li>
                    <li><strong>路径问题:</strong> IDE配置的部署路径不正确</li>
                    <li><strong>权限问题:</strong> 文件没有正确的访问权限</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>🔧 解决建议</h3>
                <ol>
                    <li><strong>清理缓存:</strong> 按Ctrl+F5强制刷新，或使用无痕模式</li>
                    <li><strong>重启服务:</strong> 重启Tomcat服务器</li>
                    <li><strong>检查部署:</strong> 确认文件在正确的部署目录</li>
                    <li><strong>检查权限:</strong> 确认文件有读取权限</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
