<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Patient" %>
<%@ page import="com.hospital.entity.Registration" %>
<%@ page import="com.hospital.service.RegistrationService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Patient patient = (Patient) session.getAttribute("patient");
    if (patient == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }

    RegistrationService registrationService = JspServiceUtil.getRegistrationService(application);
    List<Registration> registrations = null;

    // 查询患者的挂号记录
    registrations = registrationService.findByPatientIdWithDetails(patient.getId());

    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的挂号记录 - 医院管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .stats-bar {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .stats-bar h3 {
            margin: 0 0 10px 0;
            font-size: 2em;
        }
        .stats-bar p {
            margin: 0;
            opacity: 0.9;
        }
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        .table th,
        .table td {
            padding: 15px 12px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #e1e5e9;
        }
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        .table tbody tr:last-child td {
            border-bottom: none;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
            display: inline-block;
        }
        .status-已挂号 {
            background: #d4edda;
            color: #155724;
        }
        .status-已就诊 {
            background: #cce5ff;
            color: #004085;
        }
        .status-已取消 {
            background: #f8d7da;
            color: #721c24;
        }
        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            font-size: 0.9em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background: #138496;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .empty-state h3 {
            margin-bottom: 10px;
            color: #999;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/patient/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/patient/registration.jsp">预约挂号</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>我的挂号记录</h2>
            
            <!-- 显示成功或错误消息 -->
            <% String success = request.getParameter("success"); %>
            <% if (success != null) { %>
                <div class="alert alert-success">
                    <%= success %>
                </div>
            <% } %>
            
            <% String error = request.getParameter("error"); %>
            <% if (error != null) { %>
                <div class="alert alert-danger">
                    <%= error %>
                </div>
            <% } %>
            
            <div class="stats-bar">
                <h3><%= registrations.size() %></h3>
                <p>挂号记录总数</p>
            </div>

            <% if (registrations != null && !registrations.isEmpty()) { %>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>挂号编号</th>
                                <th>科室</th>
                                <th>医生</th>
                                <th>就诊日期</th>
                                <th>就诊时间</th>
                                <th>挂号费</th>
                                <th>状态</th>
                                <th>症状描述</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% for (Registration reg : registrations) { %>
                                <tr>
                                    <td><%= reg.getRegNo() != null ? reg.getRegNo() : "R" + reg.getId() %></td>
                                    <td><%= reg.getDeptName() != null ? reg.getDeptName() : "未知科室" %></td>
                                    <td><%= reg.getDoctorName() != null ? reg.getDoctorName() : "待分配" %></td>
                                    <td><%= reg.getRegDate() != null ? dateFormat.format(reg.getRegDate()) : "未安排" %></td>
                                    <td><%= reg.getRegTime() != null ? timeFormat.format(reg.getRegTime()) : "未安排" %></td>
                                    <td>¥<%= reg.getRegFee() != null ? reg.getRegFee() : "0.00" %></td>
                                    <td>
                                        <span class="status-badge status-<%= reg.getStatus() != null ? reg.getStatus() : "已挂号" %>">
                                            <%= reg.getStatus() != null ? reg.getStatus() : "已挂号" %>
                                        </span>
                                    </td>
                                    <td>
                                        <% if (reg.getSymptoms() != null && !reg.getSymptoms().trim().isEmpty()) { %>
                                            <span title="<%= reg.getSymptoms() %>">
                                                <%= reg.getSymptoms().length() > 20 ? reg.getSymptoms().substring(0, 20) + "..." : reg.getSymptoms() %>
                                            </span>
                                        <% } else { %>
                                            <span style="color: #999;">无</span>
                                        <% } %>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <%
                                                String status = reg.getStatus();
                                                if ("已就诊".equals(status)) {
                                            %>
                                                <span class="btn btn-info" style="cursor: default;">已就诊</span>
                                            <% } else if ("已取消".equals(status)) { %>
                                                <span style="color: #999;">已取消</span>
                                            <% } else { %>
                                                <span class="btn btn-primary" style="cursor: default;">待就诊</span>
                                            <% } %>
                                        </div>
                                    </td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <div class="empty-state">
                    <h3>📋 暂无挂号记录</h3>
                    <p>您还没有任何挂号记录，<a href="${pageContext.request.contextPath}/patient/registration.jsp">立即预约挂号</a></p>
                </div>
            <% } %>
        </div>
    </div>
    

</body>
</html>
