package com.hospital.entity;

import java.util.Date;

/**
 * 药品分类实体类
 * 用于管理药品的分类信息，支持中药和西药的分类管理
 * 数据库对应表：medicine_category
 */
public class MedicineCategory {
    
    private Integer id;
    private String categoryName;
    private String categoryType; // 中药、西药
    private String description;
    private Date createTime;
    private Date updateTime;
    
    public MedicineCategory() {}
    
    public MedicineCategory(String categoryName, String categoryType, String description) {
        this.categoryName = categoryName;
        this.categoryType = categoryType;
        this.description = description;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getCategoryName() {
        return categoryName;
    }
    
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
    
    public String getCategoryType() {
        return categoryType;
    }
    
    public void setCategoryType(String categoryType) {
        this.categoryType = categoryType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    @Override
    public String toString() {
        return "MedicineCategory{" +
                "id=" + id +
                ", categoryName='" + categoryName + '\'' +
                ", categoryType='" + categoryType + '\'' +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
