package com.hospital.servlet;

import com.hospital.service.*;
import com.hospital.ioc.BeanFactory;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 科室统计数据Servlet
 */
@WebServlet("/DepartmentStatsServlet")
public class DepartmentStatsServlet extends HttpServlet {
    
    private DoctorService doctorService;
    private RegistrationService registrationService;
    
    @Override
    public void init() throws ServletException {
        super.init();
        // 获取原生IoC容器中的服务
        BeanFactory beanFactory = BeanFactory.getInstance();
        doctorService = beanFactory.getApplicationContext().getBean(DoctorService.class);
        registrationService = beanFactory.getApplicationContext().getBean(RegistrationService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // 设置响应类型为JSON
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        String deptIdStr = request.getParameter("deptId");
        if (deptIdStr == null || deptIdStr.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "科室ID不能为空");
            return;
        }
        
        try {
            Integer deptId = Integer.parseInt(deptIdStr);
            
            // 获取统计数据
            Map<String, Object> stats = new HashMap<>();
            
            // 获取科室医生数量
            int doctorCount = doctorService.countByDeptId(deptId);
            
            // 获取科室挂号数量（总数和本月数）
            int totalRegistrations = registrationService.countByDeptId(deptId);
            int monthlyRegistrations = registrationService.countByDeptIdAndMonth(deptId);
            
            // 将统计数据放入Map
            stats.put("doctorCount", doctorCount);
            stats.put("totalRegistrations", totalRegistrations);
            stats.put("monthlyRegistrations", monthlyRegistrations);
            stats.put("success", true);
            
            // 转换为JSON并返回
            ObjectMapper mapper = new ObjectMapper();
            String jsonResponse = mapper.writeValueAsString(stats);
            
            response.getWriter().write(jsonResponse);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "无效的科室ID");
        } catch (Exception e) {
            e.printStackTrace();
            
            // 如果出错，返回错误信息
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "获取统计数据失败: " + e.getMessage());
            errorResponse.put("doctorCount", 0);
            errorResponse.put("totalRegistrations", 0);
            errorResponse.put("monthlyRegistrations", 0);
            
            ObjectMapper mapper = new ObjectMapper();
            String jsonResponse = mapper.writeValueAsString(errorResponse);
            
            response.getWriter().write(jsonResponse);
        }
    }
}
