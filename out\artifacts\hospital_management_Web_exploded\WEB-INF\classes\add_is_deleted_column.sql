-- 为registration表添加is_deleted字段以支持软删除功能
-- 执行此脚本前请备份数据库

-- 检查字段是否已存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'registration' 
AND COLUMN_NAME = 'is_deleted';

-- 如果字段不存在，则添加字段
ALTER TABLE registration 
ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE COMMENT '软删除标记，TRUE表示已删除';

-- 为现有数据设置默认值
UPDATE registration 
SET is_deleted = FALSE 
WHERE is_deleted IS NULL;

-- 验证字段添加成功
DESCRIBE registration;

-- 查看表结构
SHOW CREATE TABLE registration;
