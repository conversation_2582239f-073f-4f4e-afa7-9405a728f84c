package com.hospital.service;

import com.hospital.entity.Department;
import java.util.List;

/**
 * 科室服务接口
 */
public interface DepartmentService {
    
    /**
     根据ID查询科室
     */
    Department findById(Integer id);
    
    /**
     根据科室名称查询科室
     */
    Department findByDeptName(String deptName);
    
    /**
     查询所有科室
     */
    List<Department> findAll();
    
    /**
     根据科室名称模糊查询
     */
    List<Department> findByDeptNameLike(String deptName);
    
    /**
     添加科室
     */
    boolean addDepartment(Department department);
    
    /**
     更新科室信息
     */
    boolean updateDepartment(Department department);
    
    /**
     删除科室
     */
    boolean deleteDepartment(Integer id);
    
    /**
     分页查询科室
     */
    List<Department> findByPage(int page, int size);
    
    /**
     统计科室总数
     */
    int count();
    
    /**
     验证科室名称是否存在
     */
    boolean isDeptNameExists(String deptName);
}
