package com.hospital.service;

import com.hospital.entity.Prescription;
import com.hospital.entity.PrescriptionDetail;
import com.hospital.entity.ChinesePrescriptionAttr;
import java.util.Date;
import java.util.List;

/**
 * 处方服务接口
 * 提供处方的业务逻辑处理
 */
public interface PrescriptionService {
    
    /**
     * 添加处方（包含处方明细）
     */
    boolean addPrescription(Prescription prescription, List<PrescriptionDetail> details);
    
    /**
     * 添加中药处方（包含处方明细和中药特殊属性）
     */
    boolean addChinesePrescription(Prescription prescription, List<PrescriptionDetail> details, 
                                  ChinesePrescriptionAttr attr);
    
    /**
     * 更新处方
     */
    boolean updatePrescription(Prescription prescription);
    
    /**
     * 更新处方明细
     */
    boolean updatePrescriptionDetails(Integer prescriptionId, List<PrescriptionDetail> details);
    
    /**
     * 更新中药处方特殊属性
     */
    boolean updateChinesePrescriptionAttr(ChinesePrescriptionAttr attr);
    
    /**
     * 删除处方
     */
    boolean deletePrescription(Integer id);
    
    /**
     * 根据ID查询处方
     */
    Prescription findById(Integer id);
    
    /**
     * 根据处方编号查询处方
     */
    Prescription findByPrescriptionNo(String prescriptionNo);
    
    /**
     * 根据就诊记录ID查询处方
     */
    List<Prescription> findByMedicalRecordId(Integer medicalRecordId);
    
    /**
     * 根据患者ID查询处方列表
     */
    List<Prescription> findByPatientId(Integer patientId);
    
    /**
     * 根据医生ID查询处方列表
     */
    List<Prescription> findByDoctorId(Integer doctorId);
    
    /**
     * 根据处方类型查询处方列表
     */
    List<Prescription> findByPrescriptionType(String prescriptionType);
    
    /**
     * 根据状态查询处方列表
     */
    List<Prescription> findByStatus(String status);
    
    /**
     * 根据日期范围查询处方列表
     */
    List<Prescription> findByDateRange(Date startDate, Date endDate);
    
    /**
     * 查询所有处方（包含关联信息）
     */
    List<Prescription> findAllWithDetails();
    
    /**
     * 根据患者ID查询处方（包含关联信息）
     */
    List<Prescription> findByPatientIdWithDetails(Integer patientId);
    
    /**
     * 根据医生ID查询处方（包含关联信息）
     */
    List<Prescription> findByDoctorIdWithDetails(Integer doctorId);
    
    /**
     * 根据处方编号查询处方（包含关联信息）
     */
    Prescription findByPrescriptionNoWithDetails(String prescriptionNo);
    
    /**
     * 根据处方ID查询处方明细
     */
    List<PrescriptionDetail> findDetailsByPrescriptionId(Integer prescriptionId);
    
    /**
     * 根据处方ID查询中药处方特殊属性
     */
    ChinesePrescriptionAttr findChineseAttrByPrescriptionId(Integer prescriptionId);
    
    /**
     * 更新处方状态
     */
    boolean updateStatus(Integer prescriptionId, String status);
    
    /**
     * 生成处方编号
     */
    String generatePrescriptionNo();
    
    /**
     * 检查处方编号是否已存在
     */
    boolean isPrescriptionNoExists(String prescriptionNo);
    
    /**
     * 分页查询处方
     */
    List<Prescription> findByPage(int page, int size);
    
    /**
     * 统计处方总数
     */
    int count();
}
