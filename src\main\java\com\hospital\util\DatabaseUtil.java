package com.hospital.util;

import com.alibaba.druid.pool.DruidDataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Properties;
import java.io.InputStream;
import java.io.IOException;

/**
 数据库工具类
 该类负责管理数据库连接池，提供数据库连接的获取和释放功能。
 主要功能：
 1. 初始化和配置Druid数据源
 2. 提供数据库连接的获取和释放
 3. 管理连接池的生命周期
 4. 从配置文件加载数据库连接参数
 配置文件：db.properties
 */
public class DatabaseUtil {

    /**
     Druid数据源实例
     使用静态变量确保全局唯一，在类加载时初始化
     */
    private static DruidDataSource dataSource;

    /**
     静态初始化块
     在类加载时自动执行，初始化数据源配置
     */
    static {
        initDataSource();
    }
    
    /**
     初始化数据源
     从配置文件加载数据库连接参数，创建并配置Druid数据源。
     如果配置文件不存在或读取失败，将使用默认配置。
     配置项说明：
     jdbc.driver: 数据库驱动类名
     jdbc.url: 数据库连接URL
     jdbc.username: 数据库用户名
     jdbc.password: 数据库密码
     druid.initialSize: 初始连接数
     druid.minIdle: 最小空闲连接数
     druid.maxActive: 最大活跃连接数
     druid.maxWait: 获取连接最大等待时间(毫秒)
     druid.timeBetweenEvictionRunsMillis: 连接回收器运行间隔时间
     druid.minEvictableIdleTimeMillis: 连接最小空闲时间
     druid.validationQuery: 连接有效性检测SQL
     druid.testWhileIdle: 空闲时检测连接有效性
     druid.testOnBorrow: 获取连接时检测有效性
     druid.testOnReturn: 归还连接时检测有效性
     druid.poolPreparedStatements: 是否缓存PreparedStatement
     ruid.maxPoolPreparedStatementPerConnectionSize: 每个连接最大缓存的PreparedStatement数
     */
    private static void initDataSource() {
        try {
            // 加载配置文件
            Properties props = new Properties();
            InputStream is = DatabaseUtil.class.getClassLoader().getResourceAsStream("db.properties");
            if (is != null) {
                props.load(is);
                is.close();
            }

            // 创建并配置数据源
            dataSource = new DruidDataSource();

            // 基本连接配置
            dataSource.setDriverClassName(props.getProperty("jdbc.driver", "com.mysql.cj.jdbc.Driver"));
            dataSource.setUrl(props.getProperty("jdbc.url", "********************************************************************************************************************************"));
            dataSource.setUsername(props.getProperty("jdbc.username", "root"));
            dataSource.setPassword(props.getProperty("jdbc.password", "123456"));

            // 连接池配置
            dataSource.setInitialSize(Integer.parseInt(props.getProperty("druid.initialSize", "5")));
            dataSource.setMinIdle(Integer.parseInt(props.getProperty("druid.minIdle", "5")));
            dataSource.setMaxActive(Integer.parseInt(props.getProperty("druid.maxActive", "20")));
            dataSource.setMaxWait(Long.parseLong(props.getProperty("druid.maxWait", "60000")));
            dataSource.setTimeBetweenEvictionRunsMillis(Long.parseLong(props.getProperty("druid.timeBetweenEvictionRunsMillis", "60000")));
            dataSource.setMinEvictableIdleTimeMillis(Long.parseLong(props.getProperty("druid.minEvictableIdleTimeMillis", "300000")));
            dataSource.setValidationQuery(props.getProperty("druid.validationQuery", "SELECT 1 FROM DUAL"));
            dataSource.setTestWhileIdle(Boolean.parseBoolean(props.getProperty("druid.testWhileIdle", "true")));
            dataSource.setTestOnBorrow(Boolean.parseBoolean(props.getProperty("druid.testOnBorrow", "false")));
            dataSource.setTestOnReturn(Boolean.parseBoolean(props.getProperty("druid.testOnReturn", "false")));
            dataSource.setPoolPreparedStatements(Boolean.parseBoolean(props.getProperty("druid.poolPreparedStatements", "true")));
            dataSource.setMaxPoolPreparedStatementPerConnectionSize(Integer.parseInt(props.getProperty("druid.maxPoolPreparedStatementPerConnectionSize", "20")));

            // 初始化数据源
            dataSource.init();

        } catch (IOException | SQLException e) {
            throw new RuntimeException("Failed to initialize data source", e);
        }
    }
    
    /**
     获取数据库连接
     从连接池中获取一个可用的数据库连接。
     连接使用完毕后必须调用close()方法归还给连接池。
     */
    public static Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }

    /**
     关闭连接
     安全地关闭数据库连接，将连接归还给连接池。
     */
    public static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                // 记录异常但不抛出，避免影响主业务流程
                e.printStackTrace();
            }
        }
    }

    /**
     获取数据源
     返回当前使用的Druid数据源实例，主要用于：
     1. 监控连接池状态
     2. 获取连接池统计信息
     3. 与其他框架集成
     */
    public static DruidDataSource getDataSource() {
        return dataSource;
    }

    /**
     销毁数据源
     关闭数据源并释放所有资源，包括：
     1. 关闭所有活跃连接
     2. 停止连接池的后台线程
     3. 释放相关资源
     */
    public static void destroy() {
        if (dataSource != null) {
            dataSource.close();
        }
    }
}
