package com.hospital.dao;

import com.hospital.entity.PrescriptionDetail;
import java.util.List;

/**
 * 处方明细DAO接口
 * 提供处方明细的数据访问方法
 */
public interface PrescriptionDetailDao extends BaseDao<PrescriptionDetail> {
    
    /**
     * 根据处方ID查询处方明细列表
     */
    List<PrescriptionDetail> findByPrescriptionId(Integer prescriptionId);
    
    /**
     * 根据药品ID查询处方明细列表
     */
    List<PrescriptionDetail> findByMedicineId(Integer medicineId);
    
    /**
     * 根据药品类型查询处方明细列表
     * @param medicineType 药品类型（中药、西药）
     */
    List<PrescriptionDetail> findByMedicineType(String medicineType);
    
    /**
     * 根据处方ID查询处方明细（包含药品信息）
     */
    List<PrescriptionDetail> findByPrescriptionIdWithMedicine(Integer prescriptionId);
    
    /**
     * 批量插入处方明细
     */
    int batchInsert(List<PrescriptionDetail> details);
    
    /**
     * 删除处方明细
     */
    int deleteByPrescriptionId(Integer prescriptionId);
}
