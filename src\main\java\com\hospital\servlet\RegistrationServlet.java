package com.hospital.servlet;

import com.hospital.entity.Patient;
import com.hospital.entity.Registration;
import com.hospital.service.RegistrationService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Time;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 挂号控制器
 处理患者挂号相关的HTTP请求，包括挂号预约、挂号查询、挂号取消等功能。
 支持的操作：
 register: 患者挂号预约
 query: 查询挂号记录
 cancel: 取消挂号
 list: 获取挂号列表
 请求参数：
action: 操作类型（register/query/cancel/list）
doctorId: 医生ID（挂号时必需）
deptId: 科室ID（挂号时必需）
regDate: 挂号日期（挂号时必需）
regTime: 挂号时间（挂号时必需）
symptoms: 症状描述（挂号时可选）
 */
@WebServlet("/RegistrationServlet")
public class RegistrationServlet extends HttpServlet {

    /**
     挂号服务接口
     用于处理挂号相关的业务逻辑
     */
    private RegistrationService registrationService;

    /**
     Servlet初始化方法
     在Servlet实例创建时调用，用于初始化依赖的服务对象
     */
    @Override
    public void init() throws ServletException {
        BeanFactory beanFactory = BeanFactory.getInstance();
        registrationService = beanFactory.getApplicationContext().getBean(RegistrationService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        
        String action = request.getParameter("action");
        
        if ("register".equals(action)) {
            handleRegister(request, response);
        } else if ("cancel".equals(action)) {
            handleCancel(request, response);
        } else {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "无效的操作");
        }
    }
    
    /**
     处理挂号请求
     */
    private void handleRegister(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession();
        Patient patient = (Patient) session.getAttribute("patient");
        
        if (patient == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取表单参数
        String deptIdStr = request.getParameter("deptId");
        String doctorIdStr = request.getParameter("doctorId");
        String regDateStr = request.getParameter("regDate");
        String regTimeStr = request.getParameter("regTime");
        String regFeeStr = request.getParameter("regFee");
        String symptoms = request.getParameter("symptoms");
        
        // 参数验证
        if (deptIdStr == null || doctorIdStr == null || regDateStr == null || regTimeStr == null) {
            request.setAttribute("error", "请填写完整的挂号信息");
            request.getRequestDispatcher("/patient/registration.jsp").forward(request, response);
            return;
        }
        
        try {
            Integer deptId = Integer.parseInt(deptIdStr);
            Integer doctorId = Integer.parseInt(doctorIdStr);
            Date regDate = new SimpleDateFormat("yyyy-MM-dd").parse(regDateStr);
            Time regTime = Time.valueOf(regTimeStr + ":00");
            BigDecimal regFee = new BigDecimal(regFeeStr != null ? regFeeStr : "10.00");
            
            // 创建挂号记录
            Registration registration = new Registration();
            registration.setPatientId(patient.getId());
            registration.setDoctorId(doctorId);
            registration.setDeptId(deptId);
            registration.setRegDate(regDate);
            registration.setRegTime(regTime);
            registration.setRegFee(regFee);
            registration.setSymptoms(symptoms);
            
            // 保存挂号记录
            boolean success = registrationService.register(registration);
            
            if (success) {
                request.setAttribute("success", "挂号成功！挂号单号：" + registration.getRegNo());
                request.getRequestDispatcher("/patient/registration.jsp").forward(request, response);
            } else {
                request.setAttribute("error", "挂号失败，请重试");
                request.getRequestDispatcher("/patient/registration.jsp").forward(request, response);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "系统错误：" + e.getMessage());
            request.getRequestDispatcher("/patient/registration.jsp").forward(request, response);
        }
    }
    
    /**
     处理取消挂号请求
     */
    private void handleCancel(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession();
        Patient patient = (Patient) session.getAttribute("patient");
        
        if (patient == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        String idStr = request.getParameter("id");
        
        if (idStr == null) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "缺少挂号ID");
            return;
        }
        
        try {
            Integer id = Integer.parseInt(idStr);
            
            boolean success = registrationService.cancelRegistration(id, patient.getId());
            
            if (success) {
                request.setAttribute("success", "取消挂号成功");
            } else {
                request.setAttribute("error", "取消挂号失败，可能该挂号记录不存在或已就诊");
            }
            
            request.getRequestDispatcher("/patient/my-registrations.jsp").forward(request, response);
            
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "系统错误：" + e.getMessage());
            request.getRequestDispatcher("/patient/my-registrations.jsp").forward(request, response);
        }
    }
}
