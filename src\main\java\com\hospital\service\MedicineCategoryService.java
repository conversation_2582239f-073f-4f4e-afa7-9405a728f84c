package com.hospital.service;

import com.hospital.entity.MedicineCategory;
import java.util.List;

/**
 * 药品分类服务接口
 * 提供药品分类的业务逻辑处理
 */
public interface MedicineCategoryService {
    
    /**
     * 添加药品分类
     */
    boolean addMedicineCategory(MedicineCategory category);
    
    /**
     * 更新药品分类
     */
    boolean updateMedicineCategory(MedicineCategory category);
    
    /**
     * 删除药品分类
     */
    boolean deleteMedicineCategory(Integer id);
    
    /**
     * 根据ID查询药品分类
     */
    MedicineCategory findById(Integer id);
    
    /**
     * 查询所有药品分类
     */
    List<MedicineCategory> findAll();
    
    /**
     * 根据分类名称查询药品分类
     */
    MedicineCategory findByCategoryName(String categoryName);
    
    /**
     * 根据药品类型查询分类列表
     */
    List<MedicineCategory> findByCategoryType(String categoryType);
    
    /**
     * 检查分类名称是否已存在
     */
    boolean isCategoryNameExists(String categoryName);
    
    /**
     * 分页查询药品分类
     */
    List<MedicineCategory> findByPage(int page, int size);
    
    /**
     * 统计药品分类总数
     */
    int count();
}
