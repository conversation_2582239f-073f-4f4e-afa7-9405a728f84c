package com.hospital.entity;

import java.util.Date;

/**
 * 中药特殊属性实体类
 * 用于存储中药的特殊属性信息，如药性、药味、归经等
 * 数据库对应表：chinese_medicine_attr
 */
public class ChineseMedicineAttr {
    
    private Integer id;
    private Integer medicineId;
    private String nature; // 药性：寒、热、温、凉、平
    private String taste; // 药味：酸、苦、甘、辛、咸
    private String meridianTropism; // 归经
    private String processingMethod; // 炮制方法
    private Date createTime;
    private Date updateTime;
    
    public ChineseMedicineAttr() {}
    
    public ChineseMedicineAttr(Integer medicineId, String nature, String taste, 
                              String meridianTropism, String processingMethod) {
        this.medicineId = medicineId;
        this.nature = nature;
        this.taste = taste;
        this.meridianTropism = meridianTropism;
        this.processingMethod = processingMethod;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public Integer getMedicineId() {
        return medicineId;
    }
    
    public void setMedicineId(Integer medicineId) {
        this.medicineId = medicineId;
    }
    
    public String getNature() {
        return nature;
    }
    
    public void setNature(String nature) {
        this.nature = nature;
    }
    
    public String getTaste() {
        return taste;
    }
    
    public void setTaste(String taste) {
        this.taste = taste;
    }
    
    public String getMeridianTropism() {
        return meridianTropism;
    }
    
    public void setMeridianTropism(String meridianTropism) {
        this.meridianTropism = meridianTropism;
    }
    
    public String getProcessingMethod() {
        return processingMethod;
    }
    
    public void setProcessingMethod(String processingMethod) {
        this.processingMethod = processingMethod;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    @Override
    public String toString() {
        return "ChineseMedicineAttr{" +
                "id=" + id +
                ", medicineId=" + medicineId +
                ", nature='" + nature + '\'' +
                ", taste='" + taste + '\'' +
                ", meridianTropism='" + meridianTropism + '\'' +
                ", processingMethod='" + processingMethod + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
