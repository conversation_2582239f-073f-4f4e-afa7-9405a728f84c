package com.hospital.dao.impl;

import com.hospital.dao.ChineseMedicineAttrDao;
import com.hospital.entity.ChineseMedicineAttr;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 中药特殊属性DAO实现类
 */
public class ChineseMedicineAttrDaoImpl implements ChineseMedicineAttrDao {
    
    @Override
    public int insert(ChineseMedicineAttr attr) {
        String sql = "INSERT INTO chinese_medicine_attr (medicine_id, nature, taste, meridian_tropism, processing_method) " +
                    "VALUES (?, ?, ?, ?, ?)";
        return JdbcTemplate.update(sql, attr.getMedicineId(), attr.getNature(), attr.getTaste(),
                                 attr.getMeridianTropism(), attr.getProcessingMethod());
    }
    
    @Override
    public int update(ChineseMedicineAttr attr) {
        String sql = "UPDATE chinese_medicine_attr SET nature = ?, taste = ?, meridian_tropism = ?, " +
                    "processing_method = ? WHERE id = ?";
        return JdbcTemplate.update(sql, attr.getNature(), attr.getTaste(), attr.getMeridianTropism(),
                                 attr.getProcessingMethod(), attr.getId());
    }
    
    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM chinese_medicine_attr WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }
    
    @Override
    public ChineseMedicineAttr findById(Integer id) {
        String sql = "SELECT * FROM chinese_medicine_attr WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToChineseMedicineAttr, id);
    }
    
    @Override
    public List<ChineseMedicineAttr> findAll() {
        String sql = "SELECT * FROM chinese_medicine_attr";
        return JdbcTemplate.queryForList(sql, this::mapRowToChineseMedicineAttr);
    }
    
    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM chinese_medicine_attr";
        return JdbcTemplate.queryForInt(sql);
    }
    
    @Override
    public List<ChineseMedicineAttr> findByPage(int offset, int size) {
        String sql = "SELECT * FROM chinese_medicine_attr LIMIT ?, ?";
        return JdbcTemplate.queryForList(sql, this::mapRowToChineseMedicineAttr, offset, size);
    }
    
    @Override
    public ChineseMedicineAttr findByMedicineId(Integer medicineId) {
        String sql = "SELECT * FROM chinese_medicine_attr WHERE medicine_id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToChineseMedicineAttr, medicineId);
    }
    
    @Override
    public int deleteByMedicineId(Integer medicineId) {
        String sql = "DELETE FROM chinese_medicine_attr WHERE medicine_id = ?";
        return JdbcTemplate.update(sql, medicineId);
    }
    
    /**
     * 将ResultSet映射为ChineseMedicineAttr对象
     */
    private ChineseMedicineAttr mapRowToChineseMedicineAttr(ResultSet rs) throws SQLException {
        ChineseMedicineAttr attr = new ChineseMedicineAttr();
        attr.setId(rs.getInt("id"));
        attr.setMedicineId(rs.getInt("medicine_id"));
        attr.setNature(rs.getString("nature"));
        attr.setTaste(rs.getString("taste"));
        attr.setMeridianTropism(rs.getString("meridian_tropism"));
        attr.setProcessingMethod(rs.getString("processing_method"));
        attr.setCreateTime(rs.getTimestamp("create_time"));
        attr.setUpdateTime(rs.getTimestamp("update_time"));
        return attr;
    }
}
