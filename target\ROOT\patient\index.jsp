<%@ page contentType="text/html;charset=UTF-8" language="java" %> <%@ page
import="com.hospital.entity.Patient" %> <% Patient patient = (Patient)
session.getAttribute("patient"); if (patient == null) {
response.sendRedirect(request.getContextPath() + "/login.jsp"); return; } %>
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>患者中心 - 医院挂号就诊管理系统</title>
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 0;
        padding: 0;
        background: #f5f5f5;
      }

      .header {
        background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
        color: white;
        padding: 20px 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        font-size: 1.5em;
        font-weight: bold;
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 20px;
      }

      .user-info span {
        font-size: 1.1em;
      }

      .logout-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 8px 16px;
        border-radius: 5px;
        text-decoration: none;
        transition: all 0.3s ease;
      }

      .logout-btn:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .container {
        max-width: 1200px;
        margin: 30px auto;
        padding: 0 20px;
      }

      .welcome-card {
        background: white;
        border-radius: 10px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .welcome-card h2 {
        color: #333;
        margin: 0 0 10px 0;
      }

      .welcome-card p {
        color: #666;
        margin: 0;
      }

      .menu-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
      }

      .menu-item {
        background: white;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        text-decoration: none;
        color: inherit;
      }

      .menu-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
      }

      .menu-icon {
        font-size: 3em;
        margin-bottom: 15px;
        color: #667eea;
      }

      .menu-item h3 {
        margin: 0 0 10px 0;
        color: #333;
      }

      .menu-item p {
        margin: 0;
        color: #666;
        font-size: 0.9em;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="header-content">
        <div class="logo">🏥 医院管理系统</div>
        <div class="user-info">
          <span
            >欢迎，<%= patient.getName() %> (患者编号: <%=
            patient.getPatientNo() %>)</span
          >
          <a
            href="${pageContext.request.contextPath}/LogoutServlet"
            class="logout-btn"
            >退出登录</a
          >
        </div>
      </div>
    </div>

    <div class="container">
      <div class="welcome-card">
        <h2>患者中心</h2>
        <p>
          欢迎使用医院挂号就诊管理系统，您可以在这里进行挂号、查询就诊记录等操作。
        </p>
      </div>

      <div class="menu-grid">
        <a
          href="${pageContext.request.contextPath}/patient/registration.jsp"
          class="menu-item"
        >
          <div class="menu-icon">📅</div>
          <h3>在线挂号</h3>
          <p>选择科室和医生进行预约挂号</p>
        </a>

        <a
          href="${pageContext.request.contextPath}/patient/my-registrations.jsp"
          class="menu-item"
        >
          <div class="menu-icon">📋</div>
          <h3>我的挂号</h3>
          <p>查看我的挂号记录</p>
        </a>

        <a
          href="${pageContext.request.contextPath}/patient/medical-records.jsp"
          class="menu-item"
        >
          <div class="menu-icon">📄</div>
          <h3>就诊记录</h3>
          <p>查看历史就诊记录和诊断信息</p>
        </a>

        <a
          href="${pageContext.request.contextPath}/patient/doctors.jsp"
          class="menu-item"
        >
          <div class="menu-icon">👨‍⚕️</div>
          <h3>医生排班</h3>
          <p>查看医生信息和排班情况</p>
        </a>

        <a
          href="${pageContext.request.contextPath}/patient/profile.jsp"
          class="menu-item"
        >
          <div class="menu-icon">👤</div>
          <h3>个人信息</h3>
          <p>查看和修改个人基本信息</p>
        </a>
      </div>
    </div>
  </body>
</html>
