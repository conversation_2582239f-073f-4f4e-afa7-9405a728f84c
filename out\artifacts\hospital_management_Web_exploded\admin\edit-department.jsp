<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.Department" %>
<%@ page import="com.hospital.service.DepartmentService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%
    // 检查管理员登录状态
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    // 获取科室ID参数
    String idStr = request.getParameter("id");
    if (idStr == null || idStr.trim().isEmpty()) {
        response.sendRedirect(request.getContextPath() + "/admin/departments.jsp?error=科室ID不能为空");
        return;
    }
    
    Integer deptId = null;
    try {
        deptId = Integer.parseInt(idStr);
    } catch (NumberFormatException e) {
        response.sendRedirect(request.getContextPath() + "/admin/departments.jsp?error=无效的科室ID");
        return;
    }
    
    // 使用服务获取科室信息
    String deptName = "";
    String location = "";
    String phone = "";
    String description = "";
    String errorMessage = null;
    boolean dataLoaded = false;
    Department department = null;

    try {
        DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
        department = departmentService.findById(deptId);

        if (department != null) {
            deptName = department.getDeptName() != null ? department.getDeptName() : "";
            location = department.getLocation() != null ? department.getLocation() : "";
            phone = department.getPhone() != null ? department.getPhone() : "";
            description = department.getDeptDesc() != null ? department.getDeptDesc() : "";
            dataLoaded = true;
        } else {
            errorMessage = "科室不存在 (ID: " + deptId + ")";
        }

    } catch (Exception e) {
        errorMessage = "获取科室信息失败: " + e.getMessage();
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑科室信息 - 医院管理系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f5f5; }
        
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white; padding: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px; margin: 0 auto; padding: 0 20px;
            display: flex; justify-content: space-between; align-items: center;
        }
        .logo { font-size: 1.5em; font-weight: bold; }
        .nav-links a {
            color: white; text-decoration: none; margin-left: 20px;
            padding: 8px 16px; border-radius: 5px; transition: background 0.3s ease;
        }
        .nav-links a:hover { background: rgba(255,255,255,0.2); }
        
        .container { max-width: 800px; margin: 30px auto; padding: 0 20px; }
        .card {
            background: white; border-radius: 10px; padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px;
        }
        .card h2 {
            color: #333; margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea; padding-bottom: 10px;
        }
        
        .form-group { margin-bottom: 20px; }
        .form-group label {
            display: block; margin-bottom: 5px; font-weight: 500; color: #333;
        }
        .form-group input, .form-group textarea {
            width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px;
            font-size: 1em; transition: border-color 0.3s ease; box-sizing: border-box;
        }
        .form-group input:focus, .form-group textarea:focus {
            outline: none; border-color: #667eea;
        }
        .form-group textarea { resize: vertical; min-height: 100px; }
        .required { color: #dc3545; }
        
        .btn {
            padding: 12px 30px; border: none; border-radius: 8px;
            font-size: 1.1em; cursor: pointer; transition: all 0.3s ease;
            text-decoration: none; display: inline-block; text-align: center; margin-right: 15px;
        }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a6fd8; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-secondary:hover { background: #5a6268; }
        
        .form-actions {
            text-align: center; margin-top: 30px;
            padding-top: 20px; border-top: 1px solid #e1e5e9;
        }
        
        .error-message {
            background: #f8d7da; color: #721c24; padding: 15px;
            border-radius: 5px; margin-bottom: 20px; border: 1px solid #f5c6cb;
        }
        .success-message {
            background: #d4edda; color: #155724; padding: 15px;
            border-radius: 5px; margin-bottom: 20px; border: 1px solid #c3e6cb;
        }
        .info-box {
            background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/departments.jsp">返回科室管理</a>
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>编辑科室信息</h2>
            
            <!-- 显示成功或错误消息 -->
            <% String success = request.getParameter("success"); %>
            <% if (success != null) { %>
                <div class="success-message">
                    科室信息更新成功！
                </div>
            <% } %>
            
            <% String error = request.getParameter("error"); %>
            <% if (error != null) { %>
                <div class="error-message">
                    <%= error %>
                </div>
            <% } %>
            
            <% if (errorMessage != null) { %>
                <div class="error-message">
                    <%= errorMessage %>
                </div>
                <div class="info-box">
                    <p><strong>调试信息:</strong></p>
                    <p>科室ID: <%= deptId %></p>
                    <p>管理员: <%= admin.getUsername() %></p>
                    <p>请检查数据库连接和科室数据。</p>
                </div>
            <% } else if (dataLoaded) { %>
                
                <div class="info-box">
                    <h3>🏢 <%= deptName %></h3>
                    <p>科室编号：<%= deptId %></p>
                </div>
                
                <form action="${pageContext.request.contextPath}/UpdateDepartmentServlet" method="post">
                    <input type="hidden" name="id" value="<%= deptId %>">
                    
                    <div class="form-group">
                        <label for="deptName">科室名称 <span class="required">*</span></label>
                        <input type="text" id="deptName" name="deptName" 
                               value="<%= deptName %>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="location">科室位置</label>
                        <input type="text" id="location" name="location" 
                               value="<%= location %>" 
                               placeholder="例如：1楼东区">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">联系电话</label>
                        <input type="tel" id="phone" name="phone" 
                               value="<%= phone %>" 
                               placeholder="例如：010-12345678">
                    </div>
                    
                    <div class="form-group">
                        <label for="deptDesc">科室介绍</label>
                        <textarea id="deptDesc" name="deptDesc"
                                  placeholder="请输入科室的详细介绍、服务范围、特色等..."><%= description %></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">保存修改</button>
                        <a href="${pageContext.request.contextPath}/admin/departments.jsp" class="btn btn-secondary">取消</a>
                    </div>
                </form>
                
            <% } %>
        </div>
        
        <!-- 调试信息 -->
        <div class="card" style="background: #f8f9fa; border: 1px solid #dee2e6;">
            <h3>🔧 页面调试信息</h3>
            <p><strong>页面状态:</strong> ✅ 简化版编辑页面已加载</p>
            <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
            <p><strong>请求URI:</strong> <%= request.getRequestURI() %></p>
            <p><strong>Context Path:</strong> <%= request.getContextPath() %></p>
            <p><strong>科室ID:</strong> <%= deptId %></p>
            <p><strong>管理员:</strong> <%= admin.getUsername() %></p>
            <p><strong>数据加载:</strong> <%= dataLoaded ? "✅ 成功" : "❌ 失败" %></p>
            <% if (dataLoaded) { %>
                <p><strong>科室名称:</strong> <%= deptName %></p>
            <% } %>
        </div>
    </div>
    
    <script>
        // 表单验证
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const deptName = document.getElementById('deptName').value.trim();
                if (!deptName) {
                    alert('科室名称不能为空！');
                    e.preventDefault();
                    return false;
                }
                
                if (confirm('确定要保存修改吗？')) {
                    return true;
                } else {
                    e.preventDefault();
                    return false;
                }
            });
        }
    </script>
</body>
</html>
