-- 医院管理系统数据库初始化脚本
-- Hospital Management System Database Initialization Script

-- 创建数据库
CREATE DATABASE IF NOT EXISTS hospital_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE hospital_management;

-- 创建管理员表
CREATE TABLE IF NOT EXISTS admin (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    name VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建科室表
CREATE TABLE IF NOT EXISTS department (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dept_name VARCHAR(100) NOT NULL,
    description TEXT,
    location VARCHAR(100),
    phone VARCHAR(20),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建医生表
CREATE TABLE IF NOT EXISTS doctor (
    id INT PRIMARY KEY AUTO_INCREMENT,
    doctor_no VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    gender ENUM('男', '女') NOT NULL,
    age INT,
    phone VARCHAR(20),
    email VARCHAR(100),
    dept_id INT,
    title VARCHAR(50),
    speciality TEXT,
    password VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT '在职',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dept_id) REFERENCES department(id)
);

-- 创建患者表
CREATE TABLE IF NOT EXISTS patient (
    id INT PRIMARY KEY AUTO_INCREMENT,
    patient_no VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    gender ENUM('男', '女') NOT NULL,
    age INT,
    phone VARCHAR(20),
    id_card VARCHAR(18),
    address TEXT,
    emergency_contact VARCHAR(50),
    emergency_phone VARCHAR(20),
    password VARCHAR(100) NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建挂号表
CREATE TABLE IF NOT EXISTS registration (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_no VARCHAR(30) NOT NULL UNIQUE,
    patient_id INT NOT NULL,
    doctor_id INT NOT NULL,
    dept_id INT NOT NULL,
    registration_date DATE NOT NULL,
    registration_time TIME NOT NULL,
    status ENUM('已预约', '已就诊', '已取消') DEFAULT '已预约',
    fee DECIMAL(10,2) DEFAULT 0.00,
    symptoms TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patient(id),
    FOREIGN KEY (doctor_id) REFERENCES doctor(id),
    FOREIGN KEY (dept_id) REFERENCES department(id)
);

-- 创建病历表
CREATE TABLE IF NOT EXISTS medical_record (
    id INT PRIMARY KEY AUTO_INCREMENT,
    registration_id INT NOT NULL,
    patient_id INT NOT NULL,
    doctor_id INT NOT NULL,
    diagnosis TEXT,
    treatment TEXT,
    prescription TEXT,
    notes TEXT,
    record_date DATE NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (registration_id) REFERENCES registration(id),
    FOREIGN KEY (patient_id) REFERENCES patient(id),
    FOREIGN KEY (doctor_id) REFERENCES doctor(id)
);

-- 插入初始数据

-- 插入管理员数据
INSERT INTO admin (username, password, name, email, phone) VALUES 
('admin', 'admin123', '系统管理员', '<EMAIL>', '13800138000');

-- 插入科室数据
INSERT INTO department (dept_name, description, location, phone) VALUES 
('内科', '内科疾病诊疗', '1楼101室', '010-12345001'),
('外科', '外科手术治疗', '2楼201室', '010-12345002'),
('儿科', '儿童疾病诊疗', '3楼301室', '010-12345003'),
('妇产科', '妇科和产科诊疗', '4楼401室', '010-12345004'),
('骨科', '骨科疾病治疗', '5楼501室', '010-12345005');

-- 插入医生数据
INSERT INTO doctor (doctor_no, name, gender, age, phone, email, dept_id, title, speciality, password, status) VALUES 
('DOC001', '张医生', '男', 45, '13800138001', '<EMAIL>', 1, '主任医师', '心血管疾病', '123456', '在职'),
('DOC002', '李医生', '女', 38, '13800138002', '<EMAIL>', 1, '副主任医师', '消化系统疾病', '123456', '在职'),
('DOC003', '王医生', '男', 42, '13800138003', '<EMAIL>', 2, '主任医师', '普外科手术', '123456', '在职'),
('DOC004', '赵医生', '女', 35, '13800138004', '<EMAIL>', 3, '主治医师', '儿童呼吸科', '123456', '在职'),
('DOC005', '刘医生', '女', 40, '13800138005', '<EMAIL>', 4, '副主任医师', '妇科内分泌', '123456', '在职');

-- 插入患者数据
INSERT INTO patient (patient_no, name, gender, age, phone, id_card, address, emergency_contact, emergency_phone, password) VALUES 
('PAT001', '张三', '男', 30, '13900139001', '110101199001011234', '北京市朝阳区', '李四', '13900139002', '123456'),
('PAT002', '李四', '女', 25, '13900139003', '110101199501011234', '北京市海淀区', '张三', '13900139001', '123456'),
('PAT003', '王五', '男', 35, '13900139005', '110101198501011234', '北京市西城区', '赵六', '13900139006', '123456');

-- 创建索引以提高查询性能
CREATE INDEX idx_doctor_dept ON doctor(dept_id);
CREATE INDEX idx_registration_patient ON registration(patient_id);
CREATE INDEX idx_registration_doctor ON registration(doctor_id);
CREATE INDEX idx_registration_date ON registration(registration_date);
CREATE INDEX idx_medical_record_patient ON medical_record(patient_id);
CREATE INDEX idx_medical_record_doctor ON medical_record(doctor_id);

-- 显示创建结果
SELECT 'Database initialization completed successfully!' as Status;
