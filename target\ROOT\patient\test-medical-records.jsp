<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Patient" %>
<%
    Patient patient = (Patient) session.getAttribute("patient");
    if (patient == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>就诊记录测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .nav-links a { display: inline-block; margin-right: 15px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
        .nav-links a:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🏥 就诊记录功能测试</h1>
            
            <div class="success">
                <h2>✅ 测试结果：就诊记录页面访问正常！</h2>
                <p>患者就诊记录模块的Spring依赖已完全移除，现在使用JspServiceUtil获取服务。</p>
            </div>
            
            <div class="info">
                <h3>📋 患者信息</h3>
                <p><strong>患者姓名:</strong> <%= patient.getName() %></p>
                <p><strong>患者编号:</strong> <%= patient.getPatientNo() %></p>
                <p><strong>身份证号:</strong> <%= patient.getIdCard() != null ? patient.getIdCard().substring(0, 6) + "****" + patient.getIdCard().substring(14) : "未设置" %></p>
                <p><strong>联系电话:</strong> <%= patient.getPhone() != null ? patient.getPhone() : "未设置" %></p>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
            </div>
            
            <div class="info">
                <h3>🔧 修复内容</h3>
                <ul>
                    <li>✅ medical-records.jsp - 就诊记录查看页面</li>
                    <li>✅ 移除Spring WebApplicationContextUtils依赖</li>
                    <li>✅ 使用JspServiceUtil.getMedicalRecordService获取服务</li>
                    <li>✅ 文件已部署到运行时目录</li>
                </ul>
            </div>
            
            <div class="nav-links">
                <h3>🚀 功能测试</h3>
                <a href="${pageContext.request.contextPath}/patient/medical-records.jsp">就诊记录</a>
                <a href="${pageContext.request.contextPath}/patient/my-registrations.jsp">我的挂号</a>
                <a href="${pageContext.request.contextPath}/patient/registration.jsp">在线挂号</a>
                <a href="${pageContext.request.contextPath}/patient/index.jsp">返回患者首页</a>
            </div>
            
            <div class="info">
                <h3>💡 就诊记录功能说明</h3>
                <p>现在您可以：</p>
                <ol>
                    <li><strong>查看就诊历史</strong> - 查看所有历史就诊记录</li>
                    <li><strong>查看诊断信息</strong> - 查看医生的诊断结果和建议</li>
                    <li><strong>查看处方信息</strong> - 查看开具的药物处方</li>
                    <li><strong>筛选记录</strong> - 按医生姓名或日期筛选记录</li>
                    <li><strong>查看详细信息</strong> - 查看完整的就诊详情</li>
                </ol>
            </div>
            
            <div class="info">
                <h3>📋 就诊记录包含信息</h3>
                <ul>
                    <li>🏥 <strong>就诊医院</strong>: 就诊的医院信息</li>
                    <li>👨‍⚕️ <strong>主治医生</strong>: 负责诊治的医生姓名</li>
                    <li>🏷️ <strong>科室信息</strong>: 就诊的科室名称</li>
                    <li>📅 <strong>就诊时间</strong>: 具体的就诊日期和时间</li>
                    <li>📝 <strong>主要症状</strong>: 患者描述的症状</li>
                    <li>🔍 <strong>诊断结果</strong>: 医生的诊断结论</li>
                    <li>💊 <strong>处方信息</strong>: 开具的药物和用法</li>
                    <li>💰 <strong>费用信息</strong>: 就诊相关费用</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>🔧 技术信息</h3>
                <ul>
                    <li><strong>服务获取方式</strong>: JspServiceUtil.getMedicalRecordService(application)</li>
                    <li><strong>Spring依赖</strong>: 已完全移除</li>
                    <li><strong>原生IoC容器</strong>: BeanFactory + ApplicationContext</li>
                    <li><strong>数据库连接</strong>: Druid连接池</li>
                    <li><strong>数据查询</strong>: findByPatientIdWithDetails方法</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>🎯 使用建议</h3>
                <ul>
                    <li><strong>定期查看</strong>: 定期查看就诊记录，了解健康状况</li>
                    <li><strong>保存重要信息</strong>: 记录重要的诊断结果和医嘱</li>
                    <li><strong>按时复诊</strong>: 根据医生建议按时复诊</li>
                    <li><strong>药物管理</strong>: 按处方正确服用药物</li>
                    <li><strong>健康档案</strong>: 建立完整的个人健康档案</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
