<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.Department" %>
<%@ page import="com.hospital.service.DepartmentService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    // 获取科室服务
    DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
    List<Department> departments = departmentService.findAll();
%>
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>科室管理 - 医院管理系统</title>
    <link
      rel="stylesheet"
      href="${pageContext.request.contextPath}/css/style.css"
    />
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 0;
        padding: 0;
        background: #f5f5f5;
      }

      .header {
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        color: white;
        padding: 20px 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        font-size: 1.5em;
        font-weight: bold;
      }

      .nav-links a {
        color: white;
        text-decoration: none;
        margin-left: 20px;
        padding: 8px 16px;
        border-radius: 5px;
        transition: background 0.3s ease;
      }

      .nav-links a:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .container {
        max-width: 1200px;
        margin: 30px auto;
        padding: 0 20px;
      }

      .card {
        background: white;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }

      .card h2 {
        color: #333;
        margin: 0 0 20px 0;
        border-bottom: 2px solid var(--primary-color);
        padding-bottom: 10px;
      }

      .stats-bar {
        background: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--secondary-color) 100%
        );
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
      }

      .stats-bar h3 {
        margin: 0 0 5px 0;
        font-size: 2.5em;
      }

      .stats-bar p {
        margin: 0;
        opacity: 0.9;
      }

      .departments-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
      }

      .department-card {
        background: white;
        border: 2px solid #e1e5e9;
        border-radius: 10px;
        padding: 20px;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .department-card:hover {
        border-color: var(--primary-color);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .department-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
      }

      .department-icon {
        font-size: 2em;
        margin-right: 15px;
      }

      .department-name {
        font-size: 1.3em;
        font-weight: bold;
        color: #333;
        margin: 0;
      }

      .department-info {
        color: #666;
        margin-bottom: 15px;
      }

      .department-stats {
        display: flex;
        justify-content: space-between;
        background: #f8f9fa;
        padding: 10px;
        border-radius: 5px;
        font-size: 0.9em;
      }

      .stat-item {
        text-align: center;
      }

      .stat-number {
        font-weight: bold;
        color: var(--primary-color);
        display: block;
      }

      .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
      }

      .empty-state i {
        font-size: 4em;
        margin-bottom: 20px;
        color: #ccc;
      }

      .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        font-size: 1em;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        margin-right: 10px;
      }

      .btn-primary {
        background: var(--primary-color);
        color: white;
      }

      .btn-primary:hover {
        background: var(--primary-dark);
      }

      .btn-success {
        background: #28a745;
        color: white;
      }

      .btn-success:hover {
        background: #218838;
      }

      .toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .search-box {
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .search-box input {
        padding: 8px 12px;
        border: 2px solid #e1e5e9;
        border-radius: 5px;
        font-size: 1em;
      }

      .search-box input:focus {
        outline: none;
        border-color: var(--primary-color);
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="header-content">
        <div class="logo">🏥 医院管理系统</div>
        <div class="nav-links">
          <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
          <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
        </div>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <h2>科室管理</h2>

        <div class="stats-bar">
          <h3><%= departments.size() %></h3>
          <p>科室总数</p>
        </div>

        <div class="toolbar">
          <div class="search-box">
            <input
              type="text"
              id="searchInput"
              placeholder="搜索科室名称..."
              onkeyup="searchDepartments()"
            />
            <button class="btn btn-primary" onclick="searchDepartments()">
              搜索
            </button>
          </div>
          <div>
            <a
              href="${pageContext.request.contextPath}/admin/add-department.jsp"
              class="btn btn-success"
              >添加科室</a
            >
          </div>
        </div>

        <% if (departments != null && !departments.isEmpty()) { %>
        <div class="departments-grid" id="departmentsGrid">
          <% for (Department dept : departments) { %>
          <div
            class="department-card"
            data-name="<%= dept.getDeptName().toLowerCase() %>"
            onclick="viewDepartment(<%= dept.getId() %>)"
          >
            <div class="department-header">
              <div class="department-icon">🏥</div>
              <h3 class="department-name"><%= dept.getDeptName() %></h3>
            </div>
            <div class="department-info">
              <% if (dept.getDescription() != null && !dept.getDescription().trim().isEmpty()) { %>
                <%= dept.getDescription() %>
              <% } else { %>
                暂无科室描述
              <% } %>
            </div>
            <div class="department-stats">
              <div class="stat-item">
                <span class="stat-number">-</span>
                <span>医生数</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">-</span>
                <span>今日挂号</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">-</span>
                <span>本月就诊</span>
              </div>
            </div>
          </div>
          <% } %>
        </div>
        <% } else { %>
        <div class="empty-state">
          <div style="font-size: 4em; margin-bottom: 20px;">🏥</div>
          <h3>暂无科室数据</h3>
          <p>系统中还没有科室信息。</p>
          <a
            href="${pageContext.request.contextPath}/admin/add-department.jsp"
            class="btn btn-success"
            >添加第一个科室</a
          >
        </div>
        <% } %>
      </div>
    </div>

    <script>
      function searchDepartments() {
        const searchTerm = document
          .getElementById("searchInput")
          .value.toLowerCase();
        const grid = document.getElementById("departmentsGrid");

        if (!grid) return;

        const cards = grid.getElementsByClassName("department-card");

        for (let i = 0; i < cards.length; i++) {
          const card = cards[i];
          const name = card.getAttribute("data-name");

          if (searchTerm === "" || name.includes(searchTerm)) {
            card.style.display = "";
          } else {
            card.style.display = "none";
          }
        }
      }

      function viewDepartment(deptId) {
        window.location.href =
          "${pageContext.request.contextPath}/admin/view-department.jsp?id=" +
          deptId;
      }

      // 回车键搜索
      document
        .getElementById("searchInput")
        .addEventListener("keypress", function (e) {
          if (e.key === "Enter") {
            searchDepartments();
          }
        });
    </script>
  </body>
</html>
