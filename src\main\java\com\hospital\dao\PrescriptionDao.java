package com.hospital.dao;

import com.hospital.entity.Prescription;
import java.util.Date;
import java.util.List;

/**
 * 处方DAO接口
 * 提供处方的数据访问方法
 */
public interface PrescriptionDao extends BaseDao<Prescription> {
    
    /**
     * 根据处方编号查询处方
     */
    Prescription findByPrescriptionNo(String prescriptionNo);
    
    /**
     * 根据就诊记录ID查询处方
     */
    List<Prescription> findByMedicalRecordId(Integer medicalRecordId);
    
    /**
     * 根据患者ID查询处方列表
     */
    List<Prescription> findByPatientId(Integer patientId);
    
    /**
     * 根据医生ID查询处方列表
     */
    List<Prescription> findByDoctorId(Integer doctorId);
    
    /**
     * 根据处方类型查询处方列表
     * @param prescriptionType 处方类型（中药处方、西药处方、中西药混合）
     */
    List<Prescription> findByPrescriptionType(String prescriptionType);
    
    /**
     * 根据状态查询处方列表
     * @param status 状态（已开具、已发药、已取消）
     */
    List<Prescription> findByStatus(String status);
    
    /**
     * 根据日期范围查询处方列表
     */
    List<Prescription> findByDateRange(Date startDate, Date endDate);
    
    /**
     * 查询所有处方（包含关联信息）
     */
    List<Prescription> findAllWithDetails();
    
    /**
     * 根据患者ID查询处方（包含关联信息）
     */
    List<Prescription> findByPatientIdWithDetails(Integer patientId);
    
    /**
     * 根据医生ID查询处方（包含关联信息）
     */
    List<Prescription> findByDoctorIdWithDetails(Integer doctorId);
    
    /**
     * 根据处方编号查询处方（包含关联信息）
     */
    Prescription findByPrescriptionNoWithDetails(String prescriptionNo);
    
    /**
     * 检查处方编号是否已存在
     */
    boolean existsByPrescriptionNo(String prescriptionNo);
    
    /**
     * 更新处方状态
     */
    int updateStatus(Integer prescriptionId, String status);
}
