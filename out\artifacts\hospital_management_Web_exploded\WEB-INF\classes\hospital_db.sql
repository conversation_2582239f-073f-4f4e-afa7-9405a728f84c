-- 医院挂号就诊管理系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS hospital_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE hospital_management;

-- 科室信息表
CREATE TABLE IF NOT EXISTS department (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dept_name VARCHAR(50) NOT NULL COMMENT '科室名称',
    dept_desc TEXT COMMENT '科室描述',
    location VARCHAR(100) COMMENT '科室位置',
    phone VARCHAR(20) COMMENT '科室电话',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '科室信息表';

-- 医生信息表
CREATE TABLE IF NOT EXISTS doctor (
    id INT PRIMARY KEY AUTO_INCREMENT,
    doctor_no VARCHAR(20) UNIQUE NOT NULL COMMENT '医生工号',
    name VARCHAR(50) NOT NULL COMMENT '医生姓名',
    gender ENUM('男', '女') NOT NULL COMMENT '性别',
    age INT COMMENT '年龄',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    dept_id INT NOT NULL COMMENT '所属科室ID',
    title VARCHAR(20) COMMENT '职称',
    speciality TEXT COMMENT '专业特长',
    password VARCHAR(100) NOT NULL COMMENT '登录密码',
    status ENUM('在职', '离职') DEFAULT '在职' COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dept_id) REFERENCES department(id)
) COMMENT '医生信息表';

-- 患者信息表
CREATE TABLE IF NOT EXISTS patient (
    id INT PRIMARY KEY AUTO_INCREMENT,
    patient_no VARCHAR(20) UNIQUE NOT NULL COMMENT '患者编号',
    name VARCHAR(50) NOT NULL COMMENT '患者姓名',
    gender ENUM('男', '女') NOT NULL COMMENT '性别',
    age INT COMMENT '年龄',
    phone VARCHAR(20) COMMENT '联系电话',
    id_card VARCHAR(18) COMMENT '身份证号',
    address TEXT COMMENT '家庭住址',
    emergency_contact VARCHAR(50) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
    password VARCHAR(100) NOT NULL COMMENT '登录密码',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '患者信息表';

-- 挂号信息表
CREATE TABLE IF NOT EXISTS registration (
    id INT PRIMARY KEY AUTO_INCREMENT,
    reg_no VARCHAR(30) UNIQUE NOT NULL COMMENT '挂号单号',
    patient_id INT NOT NULL COMMENT '患者ID',
    doctor_id INT NOT NULL COMMENT '医生ID',
    dept_id INT NOT NULL COMMENT '科室ID',
    reg_date DATE NOT NULL COMMENT '挂号日期',
    reg_time TIME NOT NULL COMMENT '挂号时间段',
    reg_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '挂号费',
    status ENUM('已挂号', '已就诊', '已取消') DEFAULT '已挂号' COMMENT '状态',
    symptoms TEXT COMMENT '主要症状',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patient(id),
    FOREIGN KEY (doctor_id) REFERENCES doctor(id),
    FOREIGN KEY (dept_id) REFERENCES department(id)
) COMMENT '挂号信息表';

-- 就诊记录表
CREATE TABLE IF NOT EXISTS medical_record (
    id INT PRIMARY KEY AUTO_INCREMENT,
    reg_id INT NOT NULL COMMENT '挂号ID',
    patient_id INT NOT NULL COMMENT '患者ID',
    doctor_id INT NOT NULL COMMENT '医生ID',
    visit_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '就诊时间',
    chief_complaint TEXT COMMENT '主诉',
    present_illness TEXT COMMENT '现病史',
    physical_exam TEXT COMMENT '体格检查',
    diagnosis TEXT COMMENT '诊断',
    treatment_plan TEXT COMMENT '治疗方案',
    prescription TEXT COMMENT '处方',
    advice TEXT COMMENT '医嘱',
    next_visit_date DATE COMMENT '下次复诊日期',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (reg_id) REFERENCES registration(id),
    FOREIGN KEY (patient_id) REFERENCES patient(id),
    FOREIGN KEY (doctor_id) REFERENCES doctor(id)
) COMMENT '就诊记录表';

-- 管理员表
CREATE TABLE IF NOT EXISTS admin (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    role ENUM('超级管理员', '普通管理员') DEFAULT '普通管理员' COMMENT '角色',
    status ENUM('启用', '禁用') DEFAULT '启用' COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '管理员表';



-- 插入初始数据
-- 插入科室数据
INSERT INTO department (dept_name, dept_desc, location, phone) VALUES
('内科', '内科疾病诊疗', '1楼101室', '010-12345678'),
('外科', '外科手术治疗', '2楼201室', '010-12345679'),
('儿科', '儿童疾病诊疗', '3楼301室', '010-12345680'),
('妇产科', '妇科产科诊疗', '4楼401室', '010-12345681'),
('骨科', '骨科疾病治疗', '5楼501室', '010-12345682');

-- 插入管理员数据
INSERT INTO admin (username, password, name, phone, email, role) VALUES
('admin', '123456', '系统管理员', '13800138000', '<EMAIL>', '超级管理员');

-- 插入医生数据
INSERT INTO doctor (doctor_no, name, gender, age, phone, email, dept_id, title, speciality, password) VALUES
('D001', '张医生', '男', 45, '13800138001', '<EMAIL>', 1, '主任医师', '心血管疾病', '123456'),
('D002', '李医生', '女', 38, '13800138002', '<EMAIL>', 2, '副主任医师', '普外科手术', '123456'),
('D003', '王医生', '男', 42, '13800138003', '<EMAIL>', 3, '主治医师', '儿童呼吸科', '123456'),
('D004', '赵医生', '女', 40, '13800138004', '<EMAIL>', 4, '主任医师', '妇科肿瘤', '123456'),
('D005', '刘医生', '男', 35, '13800138005', '<EMAIL>', 5, '主治医师', '骨折治疗', '123456');

-- 插入患者数据
INSERT INTO patient (patient_no, name, gender, age, phone, id_card, address, emergency_contact, emergency_phone, password) VALUES
('P001', '张三', '男', 30, '13900139001', '110101199001011234', '北京市朝阳区', '李四', '13900139002', '123456'),
('P002', '李梅', '女', 25, '13900139003', '110101199501011234', '北京市海淀区', '王五', '13900139004', '123456');
