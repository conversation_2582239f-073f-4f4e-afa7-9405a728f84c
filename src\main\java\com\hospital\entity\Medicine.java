package com.hospital.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 药品实体类
 * 用于管理药品的基本信息，包括中药和西药
 * 数据库对应表：medicine
 */
public class Medicine {
    
    private Integer id;
    private String medicineCode;
    private String medicineName;
    private String medicineType; // 中药、西药
    private Integer categoryId;
    private String specification;
    private String dosageForm;
    private String unit;
    private BigDecimal price;
    private Integer stock;
    private String manufacturer;
    private String approvalNumber;
    private String description;
    private String usageMethod;
    private String status; // 在用、停用
    private Date createTime;
    private Date updateTime;
    
    // 非数据库字段，用于显示
    private String categoryName;
    private ChineseMedicineAttr chineseMedicineAttr; // 中药特殊属性
    
    public Medicine() {}
    
    public Medicine(String medicineCode, String medicineName, String medicineType, 
                   Integer categoryId, String specification, String dosageForm, 
                   String unit, BigDecimal price, Integer stock, String manufacturer, 
                   String usageMethod) {
        this.medicineCode = medicineCode;
        this.medicineName = medicineName;
        this.medicineType = medicineType;
        this.categoryId = categoryId;
        this.specification = specification;
        this.dosageForm = dosageForm;
        this.unit = unit;
        this.price = price;
        this.stock = stock;
        this.manufacturer = manufacturer;
        this.usageMethod = usageMethod;
        this.status = "在用";
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getMedicineCode() {
        return medicineCode;
    }
    
    public void setMedicineCode(String medicineCode) {
        this.medicineCode = medicineCode;
    }
    
    public String getMedicineName() {
        return medicineName;
    }
    
    public void setMedicineName(String medicineName) {
        this.medicineName = medicineName;
    }
    
    public String getMedicineType() {
        return medicineType;
    }
    
    public void setMedicineType(String medicineType) {
        this.medicineType = medicineType;
    }
    
    public Integer getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }
    
    public String getSpecification() {
        return specification;
    }
    
    public void setSpecification(String specification) {
        this.specification = specification;
    }
    
    public String getDosageForm() {
        return dosageForm;
    }
    
    public void setDosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public Integer getStock() {
        return stock;
    }
    
    public void setStock(Integer stock) {
        this.stock = stock;
    }
    
    public String getManufacturer() {
        return manufacturer;
    }
    
    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }
    
    public String getApprovalNumber() {
        return approvalNumber;
    }
    
    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getUsageMethod() {
        return usageMethod;
    }
    
    public void setUsageMethod(String usageMethod) {
        this.usageMethod = usageMethod;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getCategoryName() {
        return categoryName;
    }
    
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
    
    public ChineseMedicineAttr getChineseMedicineAttr() {
        return chineseMedicineAttr;
    }
    
    public void setChineseMedicineAttr(ChineseMedicineAttr chineseMedicineAttr) {
        this.chineseMedicineAttr = chineseMedicineAttr;
    }
    
    @Override
    public String toString() {
        return "Medicine{" +
                "id=" + id +
                ", medicineCode='" + medicineCode + '\'' +
                ", medicineName='" + medicineName + '\'' +
                ", medicineType='" + medicineType + '\'' +
                ", categoryId=" + categoryId +
                ", specification='" + specification + '\'' +
                ", dosageForm='" + dosageForm + '\'' +
                ", unit='" + unit + '\'' +
                ", price=" + price +
                ", stock=" + stock +
                ", manufacturer='" + manufacturer + '\'' +
                ", status='" + status + '\'' +
                ", categoryName='" + categoryName + '\'' +
                '}';
    }
}
