package com.hospital.service.impl;

import com.hospital.dao.PrescriptionDao;
import com.hospital.dao.PrescriptionDetailDao;
import com.hospital.dao.ChinesePrescriptionAttrDao;
import com.hospital.dao.MedicineDao;
import com.hospital.entity.Prescription;
import com.hospital.entity.PrescriptionDetail;
import com.hospital.entity.ChinesePrescriptionAttr;
import com.hospital.service.PrescriptionService;
import com.hospital.util.TransactionManager;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 处方服务实现类
 */
public class PrescriptionServiceImpl implements PrescriptionService {
    
    private PrescriptionDao prescriptionDao;
    private PrescriptionDetailDao prescriptionDetailDao;
    private ChinesePrescriptionAttrDao chinesePrescriptionAttrDao;
    private MedicineDao medicineDao;
    
    public void setPrescriptionDao(PrescriptionDao prescriptionDao) {
        this.prescriptionDao = prescriptionDao;
    }
    
    public void setPrescriptionDetailDao(PrescriptionDetailDao prescriptionDetailDao) {
        this.prescriptionDetailDao = prescriptionDetailDao;
    }
    
    public void setChinesePrescriptionAttrDao(ChinesePrescriptionAttrDao chinesePrescriptionAttrDao) {
        this.chinesePrescriptionAttrDao = chinesePrescriptionAttrDao;
    }
    
    public void setMedicineDao(MedicineDao medicineDao) {
        this.medicineDao = medicineDao;
    }
    
    @Override
    public boolean addPrescription(Prescription prescription, List<PrescriptionDetail> details) {
        return TransactionManager.executeInTransaction(() -> {
            // 生成处方编号
            if (prescription.getPrescriptionNo() == null || prescription.getPrescriptionNo().isEmpty()) {
                prescription.setPrescriptionNo(generatePrescriptionNo());
            }
            
            // 插入处方主表
            boolean success = prescriptionDao.insert(prescription) > 0;
            if (!success) {
                return false;
            }
            
            // 插入处方明细
            if (details != null && !details.isEmpty()) {
                for (PrescriptionDetail detail : details) {
                    detail.setPrescriptionId(prescription.getId());
                }
                success = prescriptionDetailDao.batchInsert(details) > 0;
                if (!success) {
                    return false;
                }
                
                // 减少药品库存
                for (PrescriptionDetail detail : details) {
                    medicineDao.reduceStock(detail.getMedicineId(), detail.getQuantity().intValue());
                }
            }
            
            return true;
        });
    }
    
    @Override
    public boolean addChinesePrescription(Prescription prescription, List<PrescriptionDetail> details, 
                                         ChinesePrescriptionAttr attr) {
        return TransactionManager.executeInTransaction(() -> {
            // 添加处方和明细
            boolean success = addPrescription(prescription, details);
            if (!success) {
                return false;
            }
            
            // 插入中药处方特殊属性
            if (attr != null) {
                attr.setPrescriptionId(prescription.getId());
                success = chinesePrescriptionAttrDao.insert(attr) > 0;
            }
            
            return success;
        });
    }
    
    @Override
    public boolean updatePrescription(Prescription prescription) {
        return prescriptionDao.update(prescription) > 0;
    }
    
    @Override
    public boolean updatePrescriptionDetails(Integer prescriptionId, List<PrescriptionDetail> details) {
        return TransactionManager.executeInTransaction(() -> {
            // 删除原有明细
            prescriptionDetailDao.deleteByPrescriptionId(prescriptionId);
            
            // 插入新明细
            if (details != null && !details.isEmpty()) {
                for (PrescriptionDetail detail : details) {
                    detail.setPrescriptionId(prescriptionId);
                }
                return prescriptionDetailDao.batchInsert(details) > 0;
            }
            
            return true;
        });
    }
    
    @Override
    public boolean updateChinesePrescriptionAttr(ChinesePrescriptionAttr attr) {
        return chinesePrescriptionAttrDao.update(attr) > 0;
    }
    
    @Override
    public boolean deletePrescription(Integer id) {
        return TransactionManager.executeInTransaction(() -> {
            // 删除中药处方特殊属性
            chinesePrescriptionAttrDao.deleteByPrescriptionId(id);
            
            // 删除处方明细
            prescriptionDetailDao.deleteByPrescriptionId(id);
            
            // 删除处方主表
            return prescriptionDao.deleteById(id) > 0;
        });
    }
    
    @Override
    public Prescription findById(Integer id) {
        return prescriptionDao.findById(id);
    }
    
    @Override
    public Prescription findByPrescriptionNo(String prescriptionNo) {
        return prescriptionDao.findByPrescriptionNo(prescriptionNo);
    }
    
    @Override
    public List<Prescription> findByMedicalRecordId(Integer medicalRecordId) {
        return prescriptionDao.findByMedicalRecordId(medicalRecordId);
    }
    
    @Override
    public List<Prescription> findByPatientId(Integer patientId) {
        return prescriptionDao.findByPatientId(patientId);
    }
    
    @Override
    public List<Prescription> findByDoctorId(Integer doctorId) {
        return prescriptionDao.findByDoctorId(doctorId);
    }
    
    @Override
    public List<Prescription> findByPrescriptionType(String prescriptionType) {
        return prescriptionDao.findByPrescriptionType(prescriptionType);
    }
    
    @Override
    public List<Prescription> findByStatus(String status) {
        return prescriptionDao.findByStatus(status);
    }
    
    @Override
    public List<Prescription> findByDateRange(Date startDate, Date endDate) {
        return prescriptionDao.findByDateRange(startDate, endDate);
    }
    
    @Override
    public List<Prescription> findAllWithDetails() {
        return prescriptionDao.findAllWithDetails();
    }
    
    @Override
    public List<Prescription> findByPatientIdWithDetails(Integer patientId) {
        return prescriptionDao.findByPatientIdWithDetails(patientId);
    }
    
    @Override
    public List<Prescription> findByDoctorIdWithDetails(Integer doctorId) {
        return prescriptionDao.findByDoctorIdWithDetails(doctorId);
    }
    
    @Override
    public Prescription findByPrescriptionNoWithDetails(String prescriptionNo) {
        return prescriptionDao.findByPrescriptionNoWithDetails(prescriptionNo);
    }
    
    @Override
    public List<PrescriptionDetail> findDetailsByPrescriptionId(Integer prescriptionId) {
        return prescriptionDetailDao.findByPrescriptionIdWithMedicine(prescriptionId);
    }
    
    @Override
    public ChinesePrescriptionAttr findChineseAttrByPrescriptionId(Integer prescriptionId) {
        return chinesePrescriptionAttrDao.findByPrescriptionId(prescriptionId);
    }
    
    @Override
    public boolean updateStatus(Integer prescriptionId, String status) {
        return prescriptionDao.updateStatus(prescriptionId, status) > 0;
    }
    
    @Override
    public String generatePrescriptionNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateTimeStr = sdf.format(new Date());
        
        // 生成处方编号：CF + 日期时间 + 3位随机数
        int random = (int) (Math.random() * 1000);
        String prescriptionNo = String.format("CF%s%03d", dateTimeStr, random);
        
        // 检查编号是否已存在，如果存在则重新生成
        while (isPrescriptionNoExists(prescriptionNo)) {
            random = (int) (Math.random() * 1000);
            prescriptionNo = String.format("CF%s%03d", dateTimeStr, random);
        }
        
        return prescriptionNo;
    }
    
    @Override
    public boolean isPrescriptionNoExists(String prescriptionNo) {
        return prescriptionDao.existsByPrescriptionNo(prescriptionNo);
    }
    
    @Override
    public List<Prescription> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return prescriptionDao.findByPage(offset, size);
    }
    
    @Override
    public int count() {
        return prescriptionDao.count();
    }
}
