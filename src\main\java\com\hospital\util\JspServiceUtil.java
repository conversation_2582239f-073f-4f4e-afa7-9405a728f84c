package com.hospital.util;

import com.hospital.ioc.BeanFactory;
import com.hospital.service.*;

/**
 JSP页面服务工具类
 */
public class JspServiceUtil {

    /**
     获取PatientService
     */
    public static PatientService getPatientService(Object application) {
        BeanFactory beanFactory = BeanFactory.getInstance();
        return beanFactory.getApplicationContext().getBean(PatientService.class);
    }

    /**
     获取DoctorService
     */
    public static DoctorService getDoctorService(Object application) {
        BeanFactory beanFactory = BeanFactory.getInstance();
        return beanFactory.getApplicationContext().getBean(DoctorService.class);
    }

    /**
     获取DepartmentService
     */
    public static DepartmentService getDepartmentService(Object application) {
        BeanFactory beanFactory = BeanFactory.getInstance();
        return beanFactory.getApplicationContext().getBean(DepartmentService.class);
    }

    /**
     获取RegistrationService
     */
    public static RegistrationService getRegistrationService(Object application) {
        BeanFactory beanFactory = BeanFactory.getInstance();
        return beanFactory.getApplicationContext().getBean(RegistrationService.class);
    }

    /**
     获取MedicalRecordService
     */
    public static MedicalRecordService getMedicalRecordService(Object application) {
        BeanFactory beanFactory = BeanFactory.getInstance();
        return beanFactory.getApplicationContext().getBean(MedicalRecordService.class);
    }

    /**
     通用方法：根据类型获取服务
     */
    @SuppressWarnings("unchecked")
    public static <T> T getService(Object application, Class<T> serviceClass) {
        BeanFactory beanFactory = BeanFactory.getInstance();
        return beanFactory.getApplicationContext().getBean(serviceClass);
    }
}
