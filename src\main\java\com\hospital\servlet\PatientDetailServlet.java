package com.hospital.servlet;

import com.hospital.entity.Patient;
import com.hospital.service.PatientService;
import com.hospital.ioc.BeanFactory;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

/**
 患者详情API
 */
@WebServlet("/PatientDetailServlet")
public class PatientDetailServlet extends HttpServlet {
    
    private PatientService patientService;
    
    @Override
    public void init() throws ServletException {
        super.init();
        BeanFactory beanFactory = BeanFactory.getInstance();
        patientService = beanFactory.getApplicationContext().getBean(PatientService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        String patientIdStr = request.getParameter("id");
        if (patientIdStr == null || patientIdStr.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "患者ID不能为空");
            return;
        }
        
        try {
            Integer patientId = Integer.parseInt(patientIdStr);
            Patient patient = patientService.findById(patientId);
            
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> result = new HashMap<>();
            
            if (patient != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                
                result.put("success", true);
                result.put("id", patient.getId());
                result.put("name", patient.getName());
                result.put("gender", patient.getGender());
                result.put("age", patient.getAge());
                result.put("phone", patient.getPhone());
                result.put("idCard", patient.getIdCard());
                result.put("address", patient.getAddress());
                result.put("emergencyContact", patient.getEmergencyContact());
                result.put("emergencyPhone", patient.getEmergencyPhone());
                result.put("patientNo", patient.getPatientNo());
                result.put("createTime", patient.getCreateTime() != null ? dateFormat.format(patient.getCreateTime()) : null);
                result.put("updateTime", patient.getUpdateTime() != null ? dateFormat.format(patient.getUpdateTime()) : null);
            } else {
                result.put("success", false);
                result.put("error", "患者不存在");
            }
            
            String jsonResponse = mapper.writeValueAsString(result);
            response.getWriter().write(jsonResponse);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "无效的患者ID");
        } catch (Exception e) {
            e.printStackTrace();
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "获取患者信息失败: " + e.getMessage());
            
            ObjectMapper mapper = new ObjectMapper();
            String jsonResponse = mapper.writeValueAsString(errorResponse);
            response.getWriter().write(jsonResponse);
        }
    }
}
