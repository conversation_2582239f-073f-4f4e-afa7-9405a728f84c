-- 初始化处方相关数据库表的脚本
-- 请在MySQL中执行此脚本来创建处方功能所需的表

USE hospital_management;

-- 药品分类表
CREATE TABLE IF NOT EXISTS medicine_category (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(50) NOT NULL COMMENT '分类名称',
    category_type ENUM('中药', '西药') NOT NULL COMMENT '药品类型',
    description TEXT COMMENT '分类描述',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '药品分类表';

-- 药品信息表
CREATE TABLE IF NOT EXISTS medicine (
    id INT PRIMARY KEY AUTO_INCREMENT,
    medicine_code VARCHAR(20) UNIQUE NOT NULL COMMENT '药品编码',
    medicine_name VARCHAR(100) NOT NULL COMMENT '药品名称',
    medicine_type ENUM('中药', '西药') NOT NULL COMMENT '药品类型',
    category_id INT COMMENT '所属分类ID',
    specification VARCHAR(100) COMMENT '规格',
    dosage_form VARCHAR(50) COMMENT '剂型',
    unit VARCHAR(20) NOT NULL COMMENT '单位',
    price DECIMAL(10,2) NOT NULL COMMENT '单价',
    stock INT DEFAULT 0 COMMENT '库存',
    manufacturer VARCHAR(100) COMMENT '生产厂家',
    approval_number VARCHAR(50) COMMENT '批准文号',
    description TEXT COMMENT '药品说明',
    usage_method TEXT COMMENT '用法',
    status ENUM('在用', '停用') DEFAULT '在用' COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES medicine_category(id)
) COMMENT '药品信息表';

-- 中药特殊属性表
CREATE TABLE IF NOT EXISTS chinese_medicine_attr (
    id INT PRIMARY KEY AUTO_INCREMENT,
    medicine_id INT NOT NULL COMMENT '药品ID',
    nature VARCHAR(50) COMMENT '药性',
    taste VARCHAR(50) COMMENT '药味',
    meridian_tropism VARCHAR(100) COMMENT '归经',
    processing_method TEXT COMMENT '炮制方法',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (medicine_id) REFERENCES medicine(id) ON DELETE CASCADE
) COMMENT '中药特殊属性表';

-- 处方主表
CREATE TABLE IF NOT EXISTS prescription (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prescription_no VARCHAR(30) UNIQUE NOT NULL COMMENT '处方编号',
    medical_record_id INT NOT NULL COMMENT '关联的就诊记录ID',
    patient_id INT NOT NULL COMMENT '患者ID',
    doctor_id INT NOT NULL COMMENT '医生ID',
    prescription_type ENUM('中药处方', '西药处方', '中西药混合') NOT NULL COMMENT '处方类型',
    diagnosis TEXT COMMENT '诊断',
    prescription_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开具日期',
    status ENUM('已开具', '已发药', '已取消') DEFAULT '已开具' COMMENT '处方状态',
    notes TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (medical_record_id) REFERENCES medical_record(id),
    FOREIGN KEY (patient_id) REFERENCES patient(id),
    FOREIGN KEY (doctor_id) REFERENCES doctor(id)
) COMMENT '处方主表';

-- 处方明细表
CREATE TABLE IF NOT EXISTS prescription_detail (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prescription_id INT NOT NULL COMMENT '处方ID',
    medicine_id INT NOT NULL COMMENT '药品ID',
    medicine_name VARCHAR(100) NOT NULL COMMENT '药品名称',
    medicine_type ENUM('中药', '西药') NOT NULL COMMENT '药品类型',
    specification VARCHAR(100) COMMENT '规格',
    dosage DECIMAL(10,2) NOT NULL COMMENT '剂量',
    dosage_unit VARCHAR(20) NOT NULL COMMENT '剂量单位',
    frequency VARCHAR(50) COMMENT '用药频次',
    usage_method TEXT COMMENT '用法',
    days INT COMMENT '用药天数',
    quantity DECIMAL(10,2) NOT NULL COMMENT '数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_price DECIMAL(10,2) NOT NULL COMMENT '总价',
    notes TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (prescription_id) REFERENCES prescription(id) ON DELETE CASCADE,
    FOREIGN KEY (medicine_id) REFERENCES medicine(id)
) COMMENT '处方明细表';

-- 中药处方特殊属性表
CREATE TABLE IF NOT EXISTS chinese_prescription_attr (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prescription_id INT NOT NULL COMMENT '处方ID',
    decoction_method TEXT COMMENT '煎煮方法',
    decoction_times INT DEFAULT 1 COMMENT '煎煮次数',
    usage_method TEXT COMMENT '服用方法',
    dosage_per_time VARCHAR(50) COMMENT '每次用量',
    frequency VARCHAR(50) COMMENT '服用频次',
    notes TEXT COMMENT '特殊说明',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (prescription_id) REFERENCES prescription(id) ON DELETE CASCADE
) COMMENT '中药处方特殊属性表';

-- 插入药品分类数据
INSERT IGNORE INTO medicine_category (category_name, category_type, description) VALUES 
('抗生素类', '西药', '用于治疗细菌感染的药物'),
('解热镇痛类', '西药', '用于缓解疼痛和退热的药物'),
('心血管类', '西药', '用于治疗心血管疾病的药物'),
('消化系统类', '西药', '用于治疗消化系统疾病的药物'),
('呼吸系统类', '西药', '用于治疗呼吸系统疾病的药物'),
('解表药', '中药', '用于治疗风寒感冒的药物'),
('清热药', '中药', '用于清热解毒的药物'),
('补益药', '中药', '用于补气养血的药物'),
('活血化瘀药', '中药', '用于活血化瘀的药物'),
('平肝息风药', '中药', '用于平肝息风的药物');

-- 插入药品数据
INSERT IGNORE INTO medicine (medicine_code, medicine_name, medicine_type, category_id, specification, dosage_form, unit, price, stock, manufacturer, usage_method, status) VALUES 
('XY001', '阿莫西林胶囊', '西药', 1, '0.25g*24粒', '胶囊', '盒', 15.80, 1000, '国药集团', '口服，一次1粒，一日3次', '在用'),
('XY002', '布洛芬片', '西药', 2, '0.2g*24片', '片剂', '盒', 12.50, 1000, '白云山制药', '口服，一次1片，一日3次', '在用'),
('XY003', '硝苯地平片', '西药', 3, '10mg*30片', '片剂', '盒', 18.60, 800, '上海医药', '口服，一次1片，一日3次', '在用'),
('XY004', '奥美拉唑肠溶胶囊', '西药', 4, '20mg*14粒', '胶囊', '盒', 25.40, 600, '华润医药', '口服，一次1粒，一日2次', '在用'),
('XY005', '氨溴索片', '西药', 5, '30mg*20片', '片剂', '盒', 22.80, 700, '哈药集团', '口服，一次1片，一日3次', '在用'),
('ZY001', '金银花', '中药', 6, '500g/袋', '中药饮片', '克', 0.15, 10000, '同仁堂', '煎煮后服用', '在用'),
('ZY002', '板蓝根', '中药', 7, '500g/袋', '中药饮片', '克', 0.12, 10000, '同仁堂', '煎煮后服用', '在用'),
('ZY003', '人参', '中药', 8, '100g/袋', '中药饮片', '克', 2.50, 5000, '同仁堂', '煎煮后服用', '在用'),
('ZY004', '当归', '中药', 9, '500g/袋', '中药饮片', '克', 0.18, 8000, '同仁堂', '煎煮后服用', '在用'),
('ZY005', '天麻', '中药', 10, '500g/袋', '中药饮片', '克', 0.35, 6000, '同仁堂', '煎煮后服用', '在用');

-- 插入中药特殊属性数据
INSERT IGNORE INTO chinese_medicine_attr (medicine_id, nature, taste, meridian_tropism, processing_method) VALUES 
((SELECT id FROM medicine WHERE medicine_code = 'ZY001'), '寒', '甘、苦', '肺、心、胃', '洗净切段'),
((SELECT id FROM medicine WHERE medicine_code = 'ZY002'), '寒', '苦', '心、肺、胃', '洗净切片'),
((SELECT id FROM medicine WHERE medicine_code = 'ZY003'), '温', '甘、微苦', '脾、肺', '切片或整根使用'),
((SELECT id FROM medicine WHERE medicine_code = 'ZY004'), '温', '甘、辛', '肝、心、脾', '切片'),
((SELECT id FROM medicine WHERE medicine_code = 'ZY005'), '平', '甘', '肝、胆', '切片');

COMMIT;
