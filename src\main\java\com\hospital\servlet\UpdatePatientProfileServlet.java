package com.hospital.servlet;

import com.hospital.entity.Patient;
import com.hospital.service.PatientService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * 更新患者信息控制器
 */
@WebServlet("/UpdatePatientProfileServlet")
public class UpdatePatientProfileServlet extends HttpServlet {
    
    private PatientService patientService;
    
    @Override
    public void init() throws ServletException {
        BeanFactory beanFactory = BeanFactory.getInstance();
        patientService = beanFactory.getApplicationContext().getBean(PatientService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        
        HttpSession session = request.getSession();
        Patient currentPatient = (Patient) session.getAttribute("patient");
        
        if (currentPatient == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取表单参数
        String name = request.getParameter("name");
        String gender = request.getParameter("gender");
        String ageStr = request.getParameter("age");
        String phone = request.getParameter("phone");
        String idCard = request.getParameter("idCard");
        String address = request.getParameter("address");
        String emergencyContact = request.getParameter("emergencyContact");
        String emergencyPhone = request.getParameter("emergencyPhone");
        
        // 参数验证
        if (name == null || name.trim().isEmpty() ||
            gender == null || gender.trim().isEmpty() ||
            phone == null || phone.trim().isEmpty()) {

            response.sendRedirect(request.getContextPath() + "/patient/profile.jsp?error=" + URLEncoder.encode("请填写所有必填项", "UTF-8"));
            return;
        }
        
        try {
            // 获取最新的患者信息
            Patient patient = patientService.findById(currentPatient.getId());
            if (patient == null) {
                response.sendRedirect(request.getContextPath() + "/login.jsp");
                return;
            }
            
            // 更新患者信息
            patient.setName(name.trim());
            patient.setGender(gender);
            patient.setPhone(phone.trim());
            
            // 处理可选字段
            if (ageStr != null && !ageStr.trim().isEmpty()) {
                try {
                    patient.setAge(Integer.parseInt(ageStr.trim()));
                } catch (NumberFormatException e) {
                    response.sendRedirect(request.getContextPath() + "/patient/profile.jsp?error=" + URLEncoder.encode("年龄格式不正确", "UTF-8"));
                    return;
                }
            } else {
                patient.setAge(null);
            }
            
            if (idCard != null && !idCard.trim().isEmpty()) {
                patient.setIdCard(idCard.trim());
            } else {
                patient.setIdCard(null);
            }
            
            if (address != null && !address.trim().isEmpty()) {
                patient.setAddress(address.trim());
            } else {
                patient.setAddress(null);
            }
            
            if (emergencyContact != null && !emergencyContact.trim().isEmpty()) {
                patient.setEmergencyContact(emergencyContact.trim());
            } else {
                patient.setEmergencyContact(null);
            }
            
            if (emergencyPhone != null && !emergencyPhone.trim().isEmpty()) {
                patient.setEmergencyPhone(emergencyPhone.trim());
            } else {
                patient.setEmergencyPhone(null);
            }
            
            // 保存更新
            boolean success = patientService.updatePatient(patient);
            
            if (success) {
                // 更新session中的患者信息
                session.setAttribute("patient", patient);
                response.sendRedirect(request.getContextPath() + "/patient/profile.jsp?success=" + URLEncoder.encode("个人信息更新成功", "UTF-8"));
            } else {
                response.sendRedirect(request.getContextPath() + "/patient/profile.jsp?error=" + URLEncoder.encode("更新失败，请重试", "UTF-8"));
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect(request.getContextPath() + "/patient/profile.jsp?error=" + URLEncoder.encode("系统错误：" + e.getMessage(), "UTF-8"));
        }
    }
}
