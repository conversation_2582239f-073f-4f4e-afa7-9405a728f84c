package com.hospital.servlet;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hospital.entity.Doctor;
import com.hospital.service.DoctorService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 获取医生列表控制器
 */
@WebServlet("/GetDoctorsServlet")
public class GetDoctorsServlet extends HttpServlet {

    private DoctorService doctorService;
    private ObjectMapper objectMapper;

    @Override
    public void init() throws ServletException {
        BeanFactory beanFactory = BeanFactory.getInstance();
        doctorService = beanFactory.getApplicationContext().getBean(DoctorService.class);
        objectMapper = new ObjectMapper();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=UTF-8");

        String deptIdStr = request.getParameter("deptId");

        if (deptIdStr == null || deptIdStr.trim().isEmpty()) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"error\":\"缺少科室ID参数\"}");
            return;
        }

        try {
            Integer deptId = Integer.parseInt(deptIdStr);

            // 查询该科室的在职医生
            List<Doctor> doctors = doctorService.findActiveDoctorsByDeptId(deptId);

            // 转换为JSON并返回
            String json = objectMapper.writeValueAsString(doctors);
            response.getWriter().write(json);

        } catch (NumberFormatException e) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"error\":\"科室ID格式错误\"}");
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"error\":\"系统错误\"}");
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }
}
