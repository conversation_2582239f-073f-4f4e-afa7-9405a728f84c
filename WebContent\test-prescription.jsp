<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.hospital.entity.*" %>
<%@ page import="com.hospital.service.*" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.*" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>处方系统测试 - 医院管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #667eea;
            margin-top: 0;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 处方系统功能测试</h1>
        
        <div class="test-section">
            <h3>1. 数据库连接测试</h3>
            <%
                try {
                    com.hospital.util.DatabaseUtil.getConnection();
                    out.println("<span class='success'>✅ 数据库连接成功</span>");
                } catch (Exception e) {
                    out.println("<span class='error'>❌ 数据库连接失败: " + e.getMessage() + "</span>");
                }
            %>
        </div>
        
        <div class="test-section">
            <h3>2. IoC容器测试</h3>
            <%
                try {
                    MedicineCategoryService medicineCategoryService = JspServiceUtil.getMedicineCategoryService(application);
                    MedicineService medicineService = JspServiceUtil.getMedicineService(application);
                    PrescriptionService prescriptionService = JspServiceUtil.getPrescriptionService(application);
                    
                    if (medicineCategoryService != null && medicineService != null && prescriptionService != null) {
                        out.println("<span class='success'>✅ IoC容器初始化成功</span>");
                    } else {
                        out.println("<span class='error'>❌ IoC容器初始化失败</span>");
                    }
                } catch (Exception e) {
                    out.println("<span class='error'>❌ IoC容器测试失败: " + e.getMessage() + "</span>");
                }
            %>
        </div>
        
        <div class="test-section">
            <h3>3. 药品分类服务测试</h3>
            <%
                try {
                    MedicineCategoryService medicineCategoryService = JspServiceUtil.getMedicineCategoryService(application);
                    List<MedicineCategory> categories = medicineCategoryService.findAll();
                    
                    if (categories != null && !categories.isEmpty()) {
                        out.println("<span class='success'>✅ 药品分类查询成功，共找到 " + categories.size() + " 个分类</span>");
                        out.println("<table>");
                        out.println("<tr><th>ID</th><th>分类名称</th><th>类型</th><th>描述</th></tr>");
                        for (MedicineCategory category : categories) {
                            out.println("<tr>");
                            out.println("<td>" + category.getId() + "</td>");
                            out.println("<td>" + category.getCategoryName() + "</td>");
                            out.println("<td>" + category.getCategoryType() + "</td>");
                            out.println("<td>" + (category.getDescription() != null ? category.getDescription() : "") + "</td>");
                            out.println("</tr>");
                        }
                        out.println("</table>");
                    } else {
                        out.println("<span class='error'>❌ 药品分类查询失败或无数据</span>");
                    }
                } catch (Exception e) {
                    out.println("<span class='error'>❌ 药品分类服务测试失败: " + e.getMessage() + "</span>");
                    e.printStackTrace();
                }
            %>
        </div>
        
        <div class="test-section">
            <h3>4. 药品服务测试</h3>
            <%
                try {
                    MedicineService medicineService = JspServiceUtil.getMedicineService(application);
                    List<Medicine> medicines = medicineService.findAllWithCategory();
                    
                    if (medicines != null && !medicines.isEmpty()) {
                        out.println("<span class='success'>✅ 药品查询成功，共找到 " + medicines.size() + " 种药品</span>");
                        out.println("<table>");
                        out.println("<tr><th>ID</th><th>药品编码</th><th>药品名称</th><th>类型</th><th>规格</th><th>价格</th><th>库存</th></tr>");
                        for (Medicine medicine : medicines) {
                            out.println("<tr>");
                            out.println("<td>" + medicine.getId() + "</td>");
                            out.println("<td>" + medicine.getMedicineCode() + "</td>");
                            out.println("<td>" + medicine.getMedicineName() + "</td>");
                            out.println("<td>" + medicine.getMedicineType() + "</td>");
                            out.println("<td>" + (medicine.getSpecification() != null ? medicine.getSpecification() : "") + "</td>");
                            out.println("<td>￥" + medicine.getPrice() + "</td>");
                            out.println("<td>" + medicine.getStock() + medicine.getUnit() + "</td>");
                            out.println("</tr>");
                        }
                        out.println("</table>");
                    } else {
                        out.println("<span class='error'>❌ 药品查询失败或无数据</span>");
                    }
                } catch (Exception e) {
                    out.println("<span class='error'>❌ 药品服务测试失败: " + e.getMessage() + "</span>");
                    e.printStackTrace();
                }
            %>
        </div>
        
        <div class="test-section">
            <h3>5. 中药和西药分类测试</h3>
            <%
                try {
                    MedicineService medicineService = JspServiceUtil.getMedicineService(application);
                    List<Medicine> chineseMedicines = medicineService.findByMedicineTypeWithCategory("中药");
                    List<Medicine> westernMedicines = medicineService.findByMedicineTypeWithCategory("西药");
                    
                    out.println("<span class='info'>中药数量: " + (chineseMedicines != null ? chineseMedicines.size() : 0) + "</span><br>");
                    out.println("<span class='info'>西药数量: " + (westernMedicines != null ? westernMedicines.size() : 0) + "</span>");
                    
                    if ((chineseMedicines != null && !chineseMedicines.isEmpty()) || 
                        (westernMedicines != null && !westernMedicines.isEmpty())) {
                        out.println("<br><span class='success'>✅ 中药和西药分类查询成功</span>");
                    } else {
                        out.println("<br><span class='error'>❌ 中药和西药分类查询失败</span>");
                    }
                } catch (Exception e) {
                    out.println("<span class='error'>❌ 中药和西药分类测试失败: " + e.getMessage() + "</span>");
                    e.printStackTrace();
                }
            %>
        </div>
        
        <div class="test-section">
            <h3>6. 处方编号生成测试</h3>
            <%
                try {
                    PrescriptionService prescriptionService = JspServiceUtil.getPrescriptionService(application);
                    String prescriptionNo = prescriptionService.generatePrescriptionNo();
                    
                    if (prescriptionNo != null && !prescriptionNo.isEmpty()) {
                        out.println("<span class='success'>✅ 处方编号生成成功: " + prescriptionNo + "</span>");
                    } else {
                        out.println("<span class='error'>❌ 处方编号生成失败</span>");
                    }
                } catch (Exception e) {
                    out.println("<span class='error'>❌ 处方编号生成测试失败: " + e.getMessage() + "</span>");
                    e.printStackTrace();
                }
            %>
        </div>
        
        <div class="test-section">
            <h3>7. 系统状态总结</h3>
            <p><span class='info'>📋 测试完成时间: <%= new java.util.Date() %></span></p>
            <p><span class='info'>💡 如果所有测试都显示 ✅，说明处方系统基本功能正常</span></p>
            <p><span class='info'>🔧 如果有 ❌ 错误，请检查数据库连接和表结构</span></p>
            
            <h4>快速链接:</h4>
            <ul>
                <li><a href="${pageContext.request.contextPath}/doctor/login.jsp">医生登录</a></li>
                <li><a href="${pageContext.request.contextPath}/doctor/index.jsp">医生主页</a></li>
                <li><a href="${pageContext.request.contextPath}/init_prescription_tables.sql" target="_blank">数据库初始化脚本</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
