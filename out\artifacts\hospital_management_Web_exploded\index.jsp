<%@ page contentType="text/html;charset=UTF-8" language="java" %> <% // 调试信息
System.out.println("访问首页 - Context Path: " + request.getContextPath());
System.out.println("访问首页 - Request URI: " + request.getRequestURI());
System.out.println("访问首页 - Request URL: " + request.getRequestURL()); %>
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>医院挂号就诊管理系统</title>
    <link
      rel="stylesheet"
      href="${pageContext.request.contextPath}/css/style.css"
    />
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #667eea 0%, #4ba24c 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;
        text-align: center;
        max-width: 600px;
        width: 90%;
      }

      .logo {
        font-size: 2.5em;
        color: #667eea;
        margin-bottom: 10px;
        font-weight: bold;
      }

      .subtitle {
        color: #666;
        margin-bottom: 40px;
        font-size: 1.1em;
      }

      .btn-group {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
      }

      .btn {
        padding: 15px 30px;
        border: none;
        border-radius: 8px;
        font-size: 1.1em;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        min-width: 120px;
      }

      .btn-primary {
        background: #667eea;
        color: white;
      }

      .btn-primary:hover {
        background: #5a6fd8;
        transform: translateY(-2px);
      }

      .btn-secondary {
        background: #764ba2;
        color: white;
      }

      .btn-secondary:hover {
        background: #6a4190;
        transform: translateY(-2px);
      }

      .features {
        margin-top: 40px;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
      }

      .feature {
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
      }

      .feature h3 {
        margin: 0 0 10px 0;
        color: #333;
      }

      .feature p {
        margin: 0;
        color: #666;
        font-size: 0.9em;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="logo">🏥 医院管理系统</div>
      <div class="subtitle">Hospital Management System</div>

      <div class="btn-group">
        <a
          href="${pageContext.request.contextPath}/login.jsp"
          class="btn btn-primary"
          >用户登录</a
        >
        <a
          href="${pageContext.request.contextPath}/register.jsp"
          class="btn btn-secondary"
          >患者注册</a
        >
      </div>

      <div class="features">
        <div class="feature">
          <h3>在线挂号</h3>
          <p>便捷的在线挂号服务，支持多科室预约</p>
        </div>
        <div class="feature">
          <h3>就诊管理</h3>
          <p>完整的就诊记录管理和查询功能</p>
        </div>
        <div class="feature">
          <h3>医生排班</h3>
          <p>实时查看医生排班信息和专业特长</p>
        </div>
        <div class="feature">
          <h3>系统安全</h3>
          <p>多角色权限管理，保障信息安全</p>
        </div>
      </div>
    </div>
  </body>
</html>
