package com.hospital.ioc;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 简单的应用上下文容器
 */
public class ApplicationContext {
    
    private static ApplicationContext instance;
    private final Map<String, Object> beans = new ConcurrentHashMap<>();
    private final Map<Class<?>, Object> beansByType = new ConcurrentHashMap<>();
    
    private ApplicationContext() {}
    
    public static ApplicationContext getInstance() {
        if (instance == null) {
            synchronized (ApplicationContext.class) {
                if (instance == null) {
                    instance = new ApplicationContext();
                }
            }
        }
        return instance;
    }
    
    /**
     * 注册Bean
     */
    public void registerBean(String name, Object bean) {
        beans.put(name, bean);
        beansByType.put(bean.getClass(), bean);
        
        // 注册接口类型
        Class<?>[] interfaces = bean.getClass().getInterfaces();
        for (Class<?> interfaceClass : interfaces) {
            beansByType.put(interfaceClass, bean);
        }
    }
    
    /**
     * 根据名称获取Bean
     */
    @SuppressWarnings("unchecked")
    public <T> T getBean(String name) {
        return (T) beans.get(name);
    }
    
    /**
     * 根据类型获取Bean
     */
    @SuppressWarnings("unchecked")
    public <T> T getBean(Class<T> type) {
        return (T) beansByType.get(type);
    }
    
    /**
     * 检查是否包含指定名称的Bean
     */
    public boolean containsBean(String name) {
        return beans.containsKey(name);
    }
    
    /**
     * 检查是否包含指定类型的Bean
     */
    public boolean containsBean(Class<?> type) {
        return beansByType.containsKey(type);
    }
    
    /**
     * 获取所有Bean名称
     */
    public String[] getBeanNames() {
        return beans.keySet().toArray(new String[0]);
    }
    
    /**
     * 清空所有Bean
     */
    public void clear() {
        beans.clear();
        beansByType.clear();
    }
}
