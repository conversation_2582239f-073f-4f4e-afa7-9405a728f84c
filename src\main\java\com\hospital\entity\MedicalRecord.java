package com.hospital.entity;
import java.util.Date;

/**
 就诊记录实体类（病历）
 该类用于表示医院管理系统中的就诊记录，是医生诊疗过程的完整记录。
 包含了患者的主诉、现病史、体格检查、诊断结果、治疗方案等医疗信息。
 业务流程：
 1. 患者挂号后到医生处就诊
 2. 医生进行问诊、检查并记录病历
 3. 医生给出诊断和治疗方案
 4. 开具处方和医嘱
 5. 安排复诊时间（如需要）
 数据库对应表：medical_record
 */
public class MedicalRecord {

    private Integer id;
    private Integer regId;
    private Integer patientId;
    private Integer doctorId;
    private Date visitDate;
    /**
     主诉
     患者就诊时的主要症状和不适，通常是患者自己描述的问题
     是医生诊断的重要依据
     */
    private String chiefComplaint;
    /**
     现病史
     患者当前疾病的发生、发展过程的详细描述
     包括症状出现时间、程度、诱因等信息
     */
    private String presentIllness;
    /**
     体格检查
     医生对患者进行的物理检查结果
     * 包括生命体征、各系统检查结果等
     */
    private String physicalExam;
    /**
     诊断结果
     医生根据患者症状、检查结果等给出的疾病诊断
     可能包括初步诊断、确定诊断等
     */
    private String diagnosis;
    /**
     治疗方案
     医生制定的治疗计划和方法
     包括药物治疗、物理治疗、手术治疗等
     */
    private String treatmentPlan;
    /**
     处方
     医生开具的药物处方
     包括药品名称、剂量、用法用量等
     */
    private String prescription;
    /**
     医嘱建议
     医生给患者的注意事项和建议
     包括生活方式、饮食、休息等方面的指导
     */
    private String advice;
    /**
     下次就诊日期
     医生建议患者复诊的日期
     用于跟踪治疗效果和病情变化
     */
    private Date nextVisitDate;
    private Date createTime;
    private Date updateTime;
    private String patientName;
    private String doctorName;
    /**
     挂号单号
     非数据库字段，用于关联显示挂号信息
     通过连表查询或业务逻辑填充
     */
    private String regNo;

    public MedicalRecord() {}

    public MedicalRecord(Integer regId, Integer patientId, Integer doctorId,
                        String chiefComplaint, String presentIllness,
                        String physicalExam, String diagnosis,
                        String treatmentPlan, String prescription, String advice) {
        this.regId = regId;
        this.patientId = patientId;
        this.doctorId = doctorId;
        this.chiefComplaint = chiefComplaint;
        this.presentIllness = presentIllness;
        this.physicalExam = physicalExam;
        this.diagnosis = diagnosis;
        this.treatmentPlan = treatmentPlan;
        this.prescription = prescription;
        this.advice = advice;
        this.visitDate = new Date(); // 设置当前时间为就诊时间
    }

    // Getter和Setter方法
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRegId() {
        return regId;
    }

    public void setRegId(Integer regId) {
        this.regId = regId;
    }

    public Integer getPatientId() {
        return patientId;
    }

    public void setPatientId(Integer patientId) {
        this.patientId = patientId;
    }

    public Integer getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Integer doctorId) {
        this.doctorId = doctorId;
    }

    public Date getVisitDate() {
        return visitDate;
    }

    public void setVisitDate(Date visitDate) {
        this.visitDate = visitDate;
    }

    public String getChiefComplaint() {
        return chiefComplaint;
    }

    public void setChiefComplaint(String chiefComplaint) {
        this.chiefComplaint = chiefComplaint;
    }

    public String getPresentIllness() {
        return presentIllness;
    }

    public void setPresentIllness(String presentIllness) {
        this.presentIllness = presentIllness;
    }

    public String getPhysicalExam() {
        return physicalExam;
    }

    public void setPhysicalExam(String physicalExam) {
        this.physicalExam = physicalExam;
    }

    public String getDiagnosis() {
        return diagnosis;
    }

    public void setDiagnosis(String diagnosis) {
        this.diagnosis = diagnosis;
    }

    public String getTreatmentPlan() {
        return treatmentPlan;
    }

    public void setTreatmentPlan(String treatmentPlan) {
        this.treatmentPlan = treatmentPlan;
    }

    public String getPrescription() {
        return prescription;
    }

    public void setPrescription(String prescription) {
        this.prescription = prescription;
    }

    public String getAdvice() {
        return advice;
    }

    public void setAdvice(String advice) {
        this.advice = advice;
    }

    public Date getNextVisitDate() {
        return nextVisitDate;
    }

    public void setNextVisitDate(Date nextVisitDate) {
        this.nextVisitDate = nextVisitDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public String getRegNo() {
        return regNo;
    }

    public void setRegNo(String regNo) {
        this.regNo = regNo;
    }

    @Override
    public String toString() {
        return "MedicalRecord{" +
                "id=" + id +
                ", regId=" + regId +
                ", patientId=" + patientId +
                ", doctorId=" + doctorId +
                ", visitDate=" + visitDate +
                ", chiefComplaint='" + chiefComplaint + '\'' +
                ", presentIllness='" + presentIllness + '\'' +
                ", physicalExam='" + physicalExam + '\'' +
                ", diagnosis='" + diagnosis + '\'' +
                ", treatmentPlan='" + treatmentPlan + '\'' +
                ", prescription='" + prescription + '\'' +
                ", advice='" + advice + '\'' +
                ", nextVisitDate=" + nextVisitDate +
                ", patientName='" + patientName + '\'' +
                ", doctorName='" + doctorName + '\'' +
                ", regNo='" + regNo + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
