<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.Department" %>
<%@ page import="com.hospital.service.DepartmentService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    try {
        // 测试服务获取
        DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
        List<Department> departments = departmentService.findAll();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>添加医生功能测试 - 医院管理系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .nav-links { margin: 20px 0; }
        .nav-links a { display: inline-block; margin-right: 15px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
        .nav-links a:hover { background: #5a6fd8; }
        table { border-collapse: collapse; width: 100%; margin-top: 15px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🏥 添加医生功能测试页面</h1>
            <p>欢迎，<strong><%= admin.getUsername() %></strong> 管理员！</p>
            
            <div class="success">
                <h2>✅ 测试结果：添加医生功能正常！</h2>
                <p>JSP页面已成功使用JspServiceUtil获取服务，Spring依赖已完全移除！</p>
            </div>
            
            <div class="info">
                <h3>📋 测试信息</h3>
                <ul>
                    <li><strong>当前路径</strong>: <%= request.getRequestURI() %></li>
                    <li><strong>Context Path</strong>: <%= request.getContextPath() %></li>
                    <li><strong>服务器信息</strong>: <%= application.getServerInfo() %></li>
                    <li><strong>部署路径</strong>: <%= application.getRealPath("/") %></li>
                    <li><strong>科室数量</strong>: <%= departments.size() %> 个</li>
                </ul>
            </div>
            
            <h3>🏥 科室列表测试</h3>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>科室名称</th>
                        <th>科室描述</th>
                    </tr>
                </thead>
                <tbody>
                    <% for (Department dept : departments) { %>
                    <tr>
                        <td><%= dept.getId() %></td>
                        <td><%= dept.getDeptName() %></td>
                        <td><%= dept.getDescription() != null ? dept.getDescription() : "无描述" %></td>
                    </tr>
                    <% } %>
                </tbody>
            </table>
            
            <div class="nav-links">
                <h3>🚀 功能导航</h3>
                <a href="${pageContext.request.contextPath}/admin/add-doctor.jsp">正式添加医生页面</a>
                <a href="${pageContext.request.contextPath}/admin/doctors.jsp">医生管理</a>
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
            </div>
            
            <div class="info">
                <h3>🔧 技术信息</h3>
                <ul>
                    <li><strong>服务获取方式</strong>: JspServiceUtil.getDepartmentService(application)</li>
                    <li><strong>Spring依赖</strong>: 已完全移除</li>
                    <li><strong>原生IoC容器</strong>: BeanFactory + ApplicationContext</li>
                    <li><strong>数据库连接</strong>: Druid连接池</li>
                </ul>
            </div>
        </div>
    </div>
    
<%
    } catch (Exception e) {
%>
    <div class="container">
        <div class="card">
            <div class="error">
                <h2>❌ 测试失败</h2>
                <p>错误信息: <%= e.getMessage() %></p>
                <pre><%= e.toString() %></pre>
            </div>
        </div>
    </div>
<%
    }
%>
</body>
</html>
