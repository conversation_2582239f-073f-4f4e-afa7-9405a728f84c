<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.Department" %>
<%@ page import="com.hospital.service.DepartmentService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }

    DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
    List<Department> departments = departmentService.findAll();
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
%>
  <!DOCTYPE html>
  <html lang="zh-CN">
    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>科室管理 - 医院管理系统</title>
      <style>
        body {
          font-family: "Microsoft YaHei", Arial, sans-serif;
          margin: 0;
          padding: 0;
          background: #f5f5f5;
        }
        .header {
          background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
          color: white;
          padding: 20px 0;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header-content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .logo {
          font-size: 1.5em;
          font-weight: bold;
        }
        .nav-links a {
          color: white;
          text-decoration: none;
          margin-left: 20px;
          padding: 8px 16px;
          border-radius: 5px;
          transition: background 0.3s ease;
        }
        .nav-links a:hover {
          background: rgba(255, 255, 255, 0.2);
        }
        .container {
          max-width: 1200px;
          margin: 30px auto;
          padding: 0 20px;
        }
        .card {
          background: white;
          border-radius: 10px;
          padding: 30px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          margin-bottom: 20px;
        }
        .card h2 {
          color: #333;
          margin: 0 0 20px 0;
          border-bottom: 2px solid #667eea;
          padding-bottom: 10px;
        }
        .stats-bar {
          background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
          color: white;
          padding: 20px;
          border-radius: 10px;
          margin-bottom: 30px;
          text-align: center;
        }
        .stats-bar h3 {
          margin: 0 0 10px 0;
          font-size: 2em;
        }
        .stats-bar p {
          margin: 0;
          opacity: 0.9;
        }
        .departments-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 20px;
        }
        .dept-card {
          background: white;
          border: 1px solid #e1e5e9;
          border-radius: 10px;
          padding: 20px;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          transition: transform 0.3s ease;
        }
        .dept-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }
        .dept-header {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
        }
        .dept-icon {
          width: 50px;
          height: 50px;
          background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 1.5em;
          margin-right: 15px;
        }
        .dept-info h3 {
          margin: 0 0 5px 0;
          color: #333;
          font-size: 1.3em;
        }
        .dept-info p {
          margin: 0;
          color: #666;
          font-size: 0.9em;
        }
        .dept-details {
          margin-bottom: 15px;
        }
        .detail-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 0.9em;
        }
        .detail-label {
          color: #666;
          font-weight: 500;
        }
        .detail-value {
          color: #333;
          font-weight: 500;
        }
        .dept-description {
          background: #f8f9fa;
          padding: 12px;
          border-radius: 6px;
          margin-bottom: 15px;
        }
        .dept-description p {
          margin: 0;
          color: #666;
          line-height: 1.4;
          font-size: 0.9em;
        }
        .dept-actions {
          display: flex;
          gap: 8px;
          justify-content: center;
        }
        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 5px;
          font-size: 0.9em;
          cursor: pointer;
          text-decoration: none;
          display: inline-block;
          transition: all 0.3s ease;
        }
        .btn-info {
          background: #17a2b8;
          color: white;
        }
        .btn-info:hover {
          background: #138496;
        }
        .btn-warning {
          background: #ffc107;
          color: #212529;
        }
        .btn-warning:hover {
          background: #e0a800;
        }
        .empty-state {
          text-align: center;
          padding: 60px 20px;
          color: #666;
        }
        .empty-state h3 {
          margin-bottom: 10px;
          color: #999;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="header-content">
          <div class="logo">🏥 医院管理系统</div>
          <div class="nav-links">
            <a href="${pageContext.request.contextPath}/admin/index.jsp"
              >返回首页</a
            >
            <a href="${pageContext.request.contextPath}/LogoutServlet"
              >退出登录</a
            >
          </div>
        </div>
      </div>

      <div class="container">
        <div class="card">
          <h2>科室管理</h2>

          <div class="stats-bar">
            <h3><%= departments.size() %></h3>
            <p>科室总数</p>
          </div>

          <% if (departments != null && !departments.isEmpty()) { %>
          <div class="departments-grid">
            <% for (Department dept : departments) { %>
            <div class="dept-card">
              <div class="dept-header">
                <div class="dept-icon">🏢</div>
                <div class="dept-info">
                  <h3><%= dept.getDeptName() %></h3>
                  <p>科室编号：<%= dept.getId() %></p>
                </div>
              </div>

              <div class="dept-details">
                <div class="detail-item">
                  <span class="detail-label">科室位置：</span>
                  <span class="detail-value"
                    ><%= dept.getLocation() != null ? dept.getLocation() :
                    "未设置" %></span
                  >
                </div>
                <div class="detail-item">
                  <span class="detail-label">联系电话：</span>
                  <span class="detail-value"
                    ><%= dept.getPhone() != null ? dept.getPhone() : "未设置"
                    %></span
                  >
                </div>
                <div class="detail-item">
                  <span class="detail-label">创建时间：</span>
                  <span class="detail-value"
                    ><%= dept.getCreateTime() != null ?
                    dateFormat.format(dept.getCreateTime()) : "未知" %></span
                  >
                </div>
              </div>

              <div class="dept-description">
                <p>
                  <%= dept.getDeptDesc() != null ? dept.getDeptDesc() :
                  "暂无描述" %>
                </p>
              </div>

              <div class="dept-actions">
                <a
                  href="${pageContext.request.contextPath}/admin/view-department.jsp?id=<%= dept.getId() %>"
                  class="btn btn-info"
                  >查看详情</a
                >
                <a
                  href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=<%= dept.getId() %>"
                  class="btn btn-warning"
                  >编辑</a
                >
              </div>
            </div>
            <% } %>
          </div>
          <% } else { %>
          <div class="empty-state">
            <h3>📋 暂无科室数据</h3>
            <p>还没有添加任何科室，请联系系统管理员添加科室信息。</p>
          </div>
          <% } %>
        </div>
      </div>
    </body>
  </html>
