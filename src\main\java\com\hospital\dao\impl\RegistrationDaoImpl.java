package com.hospital.dao.impl;

import com.hospital.dao.RegistrationDao;
import com.hospital.entity.Registration;
import com.hospital.util.DatabaseUtil;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 挂号DAO实现类
 */
public class RegistrationDaoImpl implements RegistrationDao {

    private DatabaseUtil databaseUtil;

    public void setDatabaseUtil(DatabaseUtil databaseUtil) {
        this.databaseUtil = databaseUtil;
    }
    
    @Override
    public int insert(Registration registration) {
        String sql = "INSERT INTO registration (reg_no, patient_id, doctor_id, dept_id, " +
                    "reg_date, reg_time, reg_fee, status, symptoms) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        return JdbcTemplate.update(sql, registration.getRegNo(), registration.getPatientId(),
                registration.getDoctorId(), registration.getDeptId(), registration.getRegDate(),
                registration.getRegTime(), registration.getRegFee(), registration.getStatus(),
                registration.getSymptoms());
    }

    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM registration WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }

    @Override
    public int update(Registration registration) {
        String sql = "UPDATE registration SET patient_id = ?, doctor_id = ?, dept_id = ?, " +
                    "reg_date = ?, reg_time = ?, reg_fee = ?, status = ?, symptoms = ? " +
                    "WHERE id = ?";
        return JdbcTemplate.update(sql, registration.getPatientId(), registration.getDoctorId(),
                registration.getDeptId(), registration.getRegDate(), registration.getRegTime(),
                registration.getRegFee(), registration.getStatus(), registration.getSymptoms(),
                registration.getId());
    }

    @Override
    public Registration findById(Integer id) {
        String sql = "SELECT * FROM registration WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRegistration, id);
    }
    
    @Override
    public List<Registration> findAll() {
        String sql = "SELECT * FROM registration ORDER BY create_time DESC";
        return JdbcTemplate.query(sql, this::mapRegistration);
    }

    @Override
    public List<Registration> findByPage(int offset, int limit) {
        String sql = "SELECT * FROM registration ORDER BY create_time DESC LIMIT ?, ?";
        return JdbcTemplate.query(sql, this::mapRegistration, offset, limit);
    }

    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM registration";
        Long count = JdbcTemplate.queryForObject(sql, Long.class);
        return count != null ? count.intValue() : 0;
    }

    @Override
    public Registration findByRegNo(String regNo) {
        String sql = "SELECT * FROM registration WHERE reg_no = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRegistration, regNo);
    }

    @Override
    public List<Registration> findByPatientId(Integer patientId) {
        String sql = "SELECT * FROM registration WHERE patient_id = ? ORDER BY create_time DESC";
        return JdbcTemplate.query(sql, this::mapRegistration, patientId);
    }

    @Override
    public List<Registration> findByDoctorId(Integer doctorId) {
        String sql = "SELECT * FROM registration WHERE doctor_id = ? ORDER BY create_time DESC";
        return JdbcTemplate.query(sql, this::mapRegistration, doctorId);
    }

    @Override
    public List<Registration> findByDeptId(Integer deptId) {
        String sql = "SELECT * FROM registration WHERE dept_id = ? ORDER BY create_time DESC";
        return JdbcTemplate.query(sql, this::mapRegistration, deptId);
    }

    @Override
    public List<Registration> findByRegDate(Date regDate) {
        String sql = "SELECT * FROM registration WHERE reg_date = ? ORDER BY create_time DESC";
        return JdbcTemplate.query(sql, this::mapRegistration, regDate);
    }
    
    @Override
    public List<Registration> findAllWithDetails() {
        String sql = "SELECT r.*, p.name as patient_name, p.phone as patient_phone, d.name as doctor_name, dept.dept_name " +
                    "FROM registration r " +
                    "LEFT JOIN patient p ON r.patient_id = p.id " +
                    "LEFT JOIN doctor d ON r.doctor_id = d.id " +
                    "LEFT JOIN department dept ON r.dept_id = dept.id " +
                    "ORDER BY r.create_time DESC";
        return JdbcTemplate.query(sql, this::mapRegistrationWithDetails);
    }

    @Override
    public List<Registration> findByPatientIdWithDetails(Integer patientId) {
        String sql = "SELECT r.*, p.name as patient_name, p.phone as patient_phone, d.name as doctor_name, dept.dept_name " +
                    "FROM registration r " +
                    "LEFT JOIN patient p ON r.patient_id = p.id " +
                    "LEFT JOIN doctor d ON r.doctor_id = d.id " +
                    "LEFT JOIN department dept ON r.dept_id = dept.id " +
                    "WHERE r.patient_id = ? " +
                    "ORDER BY r.create_time DESC";
        return JdbcTemplate.query(sql, this::mapRegistrationWithDetails, patientId);
    }

    @Override
    public List<Registration> findByDoctorIdWithDetails(Integer doctorId) {
        String sql = "SELECT r.*, p.name as patient_name, p.phone as patient_phone, d.name as doctor_name, dept.dept_name " +
                    "FROM registration r " +
                    "LEFT JOIN patient p ON r.patient_id = p.id " +
                    "LEFT JOIN doctor d ON r.doctor_id = d.id " +
                    "LEFT JOIN department dept ON r.dept_id = dept.id " +
                    "WHERE r.doctor_id = ? " +
                    "ORDER BY r.create_time DESC";
        return JdbcTemplate.query(sql, this::mapRegistrationWithDetails, doctorId);
    }

    @Override
    public int updateStatus(Integer id, String status) {
        String sql = "UPDATE registration SET status = ? WHERE id = ?";
        return JdbcTemplate.update(sql, status, id);
    }

    @Override
    public int countByDoctorAndDateRange(Integer doctorId, Date startDate, Date endDate) {
        String sql = "SELECT COUNT(*) FROM registration WHERE doctor_id = ? AND reg_date BETWEEN ? AND ?";
        Long count = JdbcTemplate.queryForObject(sql, Long.class, doctorId, startDate, endDate);
        return count != null ? count.intValue() : 0;
    }

    @Override
    public int countByDeptId(Integer deptId) {
        String sql = "SELECT COUNT(*) FROM registration WHERE dept_id = ?";
        Long count = JdbcTemplate.queryForObject(sql, Long.class, deptId);
        return count != null ? count.intValue() : 0;
    }

    @Override
    public int countByDeptIdAndMonth(Integer deptId) {
        String sql = "SELECT COUNT(*) FROM registration WHERE dept_id = ? AND YEAR(reg_date) = YEAR(CURDATE()) AND MONTH(reg_date) = MONTH(CURDATE())";
        Long count = JdbcTemplate.queryForObject(sql, Long.class, deptId);
        return count != null ? count.intValue() : 0;
    }

    /**
     映射ResultSet到Registration对象
     */
    private Registration mapRegistration(ResultSet rs) throws SQLException {
        Registration registration = new Registration();
        registration.setId(rs.getInt("id"));
        registration.setRegNo(rs.getString("reg_no"));
        registration.setPatientId(rs.getInt("patient_id"));
        registration.setDoctorId(rs.getInt("doctor_id"));
        registration.setDeptId(rs.getInt("dept_id"));
        registration.setRegDate(rs.getDate("reg_date"));
        registration.setRegTime(rs.getTime("reg_time"));
        registration.setRegFee(rs.getBigDecimal("reg_fee"));
        registration.setStatus(rs.getString("status"));
        registration.setSymptoms(rs.getString("symptoms"));
        registration.setCreateTime(rs.getTimestamp("create_time"));
        return registration;
    }

    /**
     映射ResultSet到Registration对象（包含详细信息）
     */
    private Registration mapRegistrationWithDetails(ResultSet rs) throws SQLException {
        Registration registration = mapRegistration(rs);
        try {
            registration.setPatientName(rs.getString("patient_name"));
            registration.setPatientPhone(rs.getString("patient_phone"));
            registration.setDoctorName(rs.getString("doctor_name"));
            registration.setDeptName(rs.getString("dept_name"));
        } catch (SQLException e) {
            // 忽略，可能没有关联信息
        }
        return registration;
    }

}
