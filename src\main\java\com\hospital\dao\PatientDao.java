package com.hospital.dao;

import com.hospital.entity.Patient;
import java.util.List;

/**
 患者DAO接口
 继承BaseDao接口，提供患者相关的数据访问方法。
 除了基本的CRUD操作外，还提供了患者特有的查询方法，
 如根据患者编号、身份证号、手机号查询等。
 该接口主要用于：
 1. 患者注册和登录
 2. 患者信息管理
 3. 患者信息查询和检索
 4. 患者密码管理
 */
public interface PatientDao extends BaseDao<Patient> {

    /**
     根据患者编号查询患者
     患者编号是系统生成的唯一标识，常用于患者登录和挂号
     */
    Patient findByPatientNo(String patientNo);

    /**
     根据身份证号查询患者
     身份证号用于患者身份验证和实名制挂号，具有唯一性
     */
    Patient findByIdCard(String idCard);

    /**
     根据手机号查询患者
     手机号用于联系患者和发送通知，具有唯一性
     */
    Patient findByPhone(String phone);

    /**
     根据姓名模糊查询患者
     支持模糊匹配，用于管理员搜索患者信息
     */
    List<Patient> findByNameLike(String name);

    /**
     患者登录验证
     验证患者编号和密码的匹配性，用于患者登录系统
     */
    Patient login(String patientNo, String password);

    /**
     修改密码
     更新指定患者的登录密码，密码应在调用前进行加密处理
     */
    int updatePassword(Integer id, String newPassword);
}
