<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%@ page import="com.hospital.entity.Department" %>
<%@ page import="com.hospital.service.DoctorService" %>
<%@ page import="com.hospital.service.DepartmentService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    String idStr = request.getParameter("id");
    if (idStr == null || idStr.trim().isEmpty()) {
        response.sendRedirect(request.getContextPath() + "/admin/doctors.jsp?error=医生ID不能为空");
        return;
    }
    
    Integer doctorId = null;
    try {
        doctorId = Integer.parseInt(idStr);
    } catch (NumberFormatException e) {
        response.sendRedirect(request.getContextPath() + "/admin/doctors.jsp?error=无效的医生ID");
        return;
    }
    
    DoctorService doctorService = JspServiceUtil.getDoctorService(application);
    DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
    
    Doctor doctor = doctorService.findById(doctorId);
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/admin/doctors.jsp?error=医生不存在");
        return;
    }
    
    List<Department> departments = departmentService.findAll();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑医生信息 - 医院管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 20px;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        .required {
            color: #dc3545;
        }
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-right: 15px;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .form-actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }
        .doctor-header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .doctor-header h3 {
            margin: 0 0 10px 0;
            font-size: 1.5em;
        }
        .doctor-header p {
            margin: 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/doctors.jsp">返回医生管理</a>
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>编辑医生信息</h2>
            
            <div class="doctor-header">
                <h3>👨‍⚕️ <%= doctor.getName() %></h3>
                <p>工号：<%= doctor.getDoctorNo() %> | 当前状态：<%= doctor.getStatus() %></p>
            </div>
            
            <form action="${pageContext.request.contextPath}/UpdateDoctorServlet" method="post">
                <input type="hidden" name="id" value="<%= doctor.getId() %>">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">姓名 <span class="required">*</span></label>
                        <input type="text" id="name" name="name" value="<%= doctor.getName() %>" required>
                    </div>
                    <div class="form-group">
                        <label for="gender">性别 <span class="required">*</span></label>
                        <select id="gender" name="gender" required>
                            <option value="男" <%= "男".equals(doctor.getGender()) ? "selected" : "" %>>男</option>
                            <option value="女" <%= "女".equals(doctor.getGender()) ? "selected" : "" %>>女</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="age">年龄</label>
                        <input type="number" id="age" name="age" value="<%= doctor.getAge() != null ? doctor.getAge() : "" %>" min="20" max="80">
                    </div>
                    <div class="form-group">
                        <label for="phone">手机号码 <span class="required">*</span></label>
                        <input type="tel" id="phone" name="phone" value="<%= doctor.getPhone() %>" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="email">电子邮箱</label>
                        <input type="email" id="email" name="email" value="<%= doctor.getEmail() != null ? doctor.getEmail() : "" %>">
                    </div>
                    <div class="form-group">
                        <label for="deptId">所属科室 <span class="required">*</span></label>
                        <select id="deptId" name="deptId" required>
                            <option value="">请选择科室</option>
                            <% for (Department dept : departments) { %>
                                <option value="<%= dept.getId() %>" <%= dept.getId().equals(doctor.getDeptId()) ? "selected" : "" %>>
                                    <%= dept.getDeptName() %>
                                </option>
                            <% } %>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="title">职称</label>
                        <select id="title" name="title">
                            <option value="">请选择职称</option>
                            <option value="住院医师" <%= "住院医师".equals(doctor.getTitle()) ? "selected" : "" %>>住院医师</option>
                            <option value="主治医师" <%= "主治医师".equals(doctor.getTitle()) ? "selected" : "" %>>主治医师</option>
                            <option value="副主任医师" <%= "副主任医师".equals(doctor.getTitle()) ? "selected" : "" %>>副主任医师</option>
                            <option value="主任医师" <%= "主任医师".equals(doctor.getTitle()) ? "selected" : "" %>>主任医师</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="status">工作状态 <span class="required">*</span></label>
                        <select id="status" name="status" required>
                            <option value="在职" <%= "在职".equals(doctor.getStatus()) ? "selected" : "" %>>在职</option>
                            <option value="休假" <%= "休假".equals(doctor.getStatus()) ? "selected" : "" %>>休假</option>
                            <option value="离职" <%= "离职".equals(doctor.getStatus()) ? "selected" : "" %>>离职</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label for="speciality">专业特长</label>
                        <textarea id="speciality" name="speciality" placeholder="请输入医生的专业特长和擅长领域..."><%= doctor.getSpeciality() != null ? doctor.getSpeciality() : "" %></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">保存修改</button>
                    <a href="${pageContext.request.contextPath}/admin/view-doctor.jsp?id=<%= doctor.getId() %>" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
