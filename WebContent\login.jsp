﻿<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 智慧医疗管理系统</title>
<%--    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">--%>
    <link rel="stylesheet" href="css/style.css"/>
    <style>

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        /* 背景装饰 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        .login-container {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            padding: var(--spacing-2xl);
            width: 450px;
            max-width: 90%;
            position: relative;
            z-index: 1;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .login-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-md);
            font-size: 2rem;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .login-header h2 {
            color: var(--text-primary);
            margin: 0 0 var(--spacing-sm) 0;
            font-size: 1.875rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-header p {
            color: var(--text-secondary);
            margin: 0;
            font-size: 0.875rem;
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.875rem;
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
            background: var(--surface-color);
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(13, 115, 119, 0.1);
            transform: translateY(-1px);
        }

        .form-group select::placeholder,
        .form-group input::placeholder {
            color: var(--text-muted);
        }

        .btn-login {
            width: 100%;
            padding: var(--spacing-md);
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-size: 1.125rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(13, 115, 119, 0.3);
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(13, 115, 119, 0.4);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .error {
            background: rgba(229, 62, 62, 0.1);
            color: var(--error-color);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            border: 1px solid rgba(229, 62, 62, 0.2);
            border-left: 4px solid var(--error-color);
            font-size: 0.875rem;
        }

        .links {
            text-align: center;
            margin-top: var(--spacing-lg);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--border-color);
        }

        .links a {
            color: var(--primary-color);
            text-decoration: none;
            margin: 0 var(--spacing-md);
            font-weight: 500;
            font-size: 0.875rem;
            transition: color 0.3s ease;
        }

        .links a:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }

        /* 用户类型选择样式 */
        .user-type-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }

        .user-type-option {
            display: none;
        }

        .user-type-label {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            background: var(--surface-color);
        }

        .user-type-label:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .user-type-option:checked + .user-type-label {
            border-color: var(--primary-color);
            background: rgba(13, 115, 119, 0.05);
            color: var(--primary-color);
        }

        .user-type-icon {
            font-size: 1.5rem;
            margin-bottom: var(--spacing-xs);
        }

        .user-type-text {
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* 鍝嶅簲寮忚璁?*/
        @media (max-width: 768px) {
            .login-container {
                padding: var(--spacing-lg);
                margin: var(--spacing-md);
            }

            .user-type-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-xs);
            }

            .user-type-label {
                flex-direction: row;
                justify-content: flex-start;
                text-align: left;
            }

            .user-type-icon {
                margin-right: var(--spacing-sm);
                margin-bottom: 0;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-icon">🔐</div>
            <h2>用户登录</h2>
            <p>请选择用户类型并输入登录信息</p>
        </div>

        <% if (request.getAttribute("error") != null) { %>
            <div class="error">
                <strong>登录失败：</strong><%= request.getAttribute("error") %>
            </div>
        <% } %>

        <form action="${pageContext.request.contextPath}/LoginServlet" method="post">
            <div class="form-group">
                <label>用户类型</label>
                <div class="user-type-grid">
                    <input type="radio" id="patient" name="userType" value="patient" class="user-type-option" required>
                    <label for="patient" class="user-type-label">
                        <span class="user-type-icon">👤</span>
                        <span class="user-type-text">患者</span>
                    </label>

                    <input type="radio" id="doctor" name="userType" value="doctor" class="user-type-option" required>
                    <label for="doctor" class="user-type-label">
                        <span class="user-type-icon">👨‍⚕️</span>
                        <span class="user-type-text">医生</span>
                    </label>

                    <input type="radio" id="admin" name="userType" value="admin" class="user-type-option" required>
                    <label for="admin" class="user-type-label">
                        <span class="user-type-icon">👨‍💼</span>
                        <span class="user-type-text">管理员</span>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" placeholder="请输入用户名/工号/患者编号" required>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" placeholder="请输入密码" required>
            </div>

            <button type="submit" class="btn-login">
                <span>登录系统</span>
            </button>
        </form>

        <div class="links">
            <a href="${pageContext.request.contextPath}/register.jsp">
                <span>📝</span> 患者注册
            </a>
            <a href="${pageContext.request.contextPath}/index.jsp">
                <span>🏠</span> 返回首页
            </a>
        </div>
    </div>
</body>
</html>

