<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>患者注册 - 智慧医疗管理系统</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/style.css">
    <style>
        body {
            margin: 0;
            padding: var(--spacing-lg);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* 背景装饰 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        .register-container {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            padding: var(--spacing-2xl);
            max-width: 700px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .register-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .register-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-md);
            font-size: 2rem;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .register-header h2 {
            color: var(--text-primary);
            margin: 0 0 var(--spacing-sm) 0;
            font-size: 1.875rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .register-header p {
            color: var(--text-secondary);
            margin: 0;
            font-size: 0.875rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-group label {
            display: block;
            margin-bottom: var(--spacing-xs);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.875rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
            background: var(--surface-color);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(13, 115, 119, 0.1);
            transform: translateY(-1px);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: var(--text-muted);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }

        .btn-register {
            width: 100%;
            padding: var(--spacing-md);
            background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-size: 1.125rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
            margin-top: var(--spacing-lg);
        }

        .btn-register::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-register:hover::before {
            left: 100%;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        }

        .btn-register:active {
            transform: translateY(0);
        }

        .error {
            background: rgba(229, 62, 62, 0.1);
            color: var(--error-color);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            border: 1px solid rgba(229, 62, 62, 0.2);
            border-left: 4px solid var(--error-color);
            font-size: 0.875rem;
        }

        .success {
            background: rgba(56, 161, 105, 0.1);
            color: var(--success-color);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            border: 1px solid rgba(56, 161, 105, 0.2);
            border-left: 4px solid var(--success-color);
            font-size: 0.875rem;
        }

        .links {
            text-align: center;
            margin-top: var(--spacing-lg);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--border-color);
        }

        .links a {
            color: var(--primary-color);
            text-decoration: none;
            margin: 0 var(--spacing-md);
            font-weight: 500;
            font-size: 0.875rem;
            transition: color 0.3s ease;
        }

        .links a:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }

        .required {
            color: var(--error-color);
            font-weight: 600;
        }

        /* 表单验证样式 */
        .form-group input:invalid:not(:placeholder-shown) {
            border-color: var(--error-color);
        }

        .form-group input:valid:not(:placeholder-shown) {
            border-color: var(--success-color);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: var(--spacing-sm);
            }

            .register-container {
                padding: var(--spacing-lg);
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }

            .register-header h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <div class="register-icon">📝</div>
            <h2>患者注册</h2>
            <p>请填写完整的个人信息进行注册，我们将为您提供优质的医疗服务</p>
        </div>

        <% if (request.getAttribute("error") != null) { %>
            <div class="error">
                <strong>注册失败：</strong><%= request.getAttribute("error") %>
            </div>
        <% } %>

        <% if (request.getAttribute("success") != null) { %>
            <div class="success">
                <strong>注册成功：</strong><%= request.getAttribute("success") %>
            </div>
        <% } %>

        <form action="${pageContext.request.contextPath}/RegisterServlet" method="post">
            <div class="form-row">
                <div class="form-group">
                    <label for="name">👤 姓名 <span class="required">*</span></label>
                    <input type="text" id="name" name="name" placeholder="请输入真实姓名" required>
                </div>
                <div class="form-group">
                    <label for="gender">⚧ 性别 <span class="required">*</span></label>
                    <select id="gender" name="gender" required>
                        <option value="">请选择性别</option>
                        <option value="男">👨 男</option>
                        <option value="女">👩 女</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="age">🎂 年龄</label>
                    <input type="number" id="age" name="age" placeholder="请输入年龄" min="1" max="150">
                </div>
                <div class="form-group">
                    <label for="phone">📱 联系电话 <span class="required">*</span></label>
                    <input type="tel" id="phone" name="phone" placeholder="请输入手机号码" pattern="[0-9]{11}" required>
                </div>
            </div>

            <div class="form-group">
                <label for="idCard">🆔 身份证号</label>
                <input type="text" id="idCard" name="idCard" placeholder="请输入18位身份证号码" maxlength="18" pattern="[0-9X]{18}">
            </div>

            <div class="form-group">
                <label for="address">🏠 家庭住址</label>
                <textarea id="address" name="address" placeholder="请输入详细地址，包括省市区街道门牌号等"></textarea>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="emergencyContact">🚨 紧急联系人</label>
                    <input type="text" id="emergencyContact" name="emergencyContact" placeholder="请输入紧急联系人姓名">
                </div>
                <div class="form-group">
                    <label for="emergencyPhone">☎️ 紧急联系电话</label>
                    <input type="tel" id="emergencyPhone" name="emergencyPhone" placeholder="请输入紧急联系电话" pattern="[0-9]{11}">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="password">🔒 登录密码 <span class="required">*</span></label>
                    <input type="password" id="password" name="password" placeholder="请输入6-20位登录密码" minlength="6" maxlength="20" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">🔐 确认密码 <span class="required">*</span></label>
                    <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" minlength="6" maxlength="20" required>
                </div>
            </div>

            <button type="submit" class="btn-register">
                <span>立即注册</span>
            </button>
        </form>

        <div class="links">
            <a href="${pageContext.request.contextPath}/login.jsp">
                <span>🔐</span> 已有账号？立即登录
            </a>
            <a href="${pageContext.request.contextPath}/index.jsp">
                <span>🏠</span> 返回首页
            </a>
        </div>
    </div>

    <script>
        // 密码确认验证
        document.getElementById('confirmPassword').addEventListener('blur', function() {
            var password = document.getElementById('password').value;
            var confirmPassword = this.value;

            if (password !== confirmPassword) {
                this.setCustomValidity('两次输入的密码不一致');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
