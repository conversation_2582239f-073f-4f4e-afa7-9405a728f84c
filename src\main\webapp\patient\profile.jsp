<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Patient" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Patient patient = (Patient) session.getAttribute("patient");
    if (patient == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人信息 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            border-radius: 10px;
            color: white;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            font-weight: bold;
            margin-right: 20px;
        }
        
        .profile-info h3 {
            margin: 0 0 10px 0;
            font-size: 1.5em;
        }
        
        .profile-info p {
            margin: 0;
            opacity: 0.9;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .info-section h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.1em;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .info-item:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #666;
            min-width: 100px;
        }
        
        .info-value {
            color: #333;
            flex: 1;
            text-align: right;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group textarea {
            resize: vertical;
            height: 80px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .edit-mode {
            display: none;
        }
        
        .readonly-field {
            background: #f8f9fa;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/patient/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>个人信息</h2>
            
            <% if (request.getParameter("success") != null) { %>
                <div class="alert alert-success">
                    <%= request.getParameter("success") %>
                </div>
            <% } %>
            
            <% if (request.getParameter("error") != null) { %>
                <div class="alert alert-error">
                    <%= request.getParameter("error") %>
                </div>
            <% } %>
            
            <div class="profile-header">
                <div class="profile-avatar">
                    <%= patient.getName().substring(0, 1) %>
                </div>
                <div class="profile-info">
                    <h3><%= patient.getName() %></h3>
                    <p>患者编号：<%= patient.getPatientNo() %></p>
                    <p>注册时间：<%= patient.getCreateTime() != null ? dateFormat.format(patient.getCreateTime()) : "未知" %></p>
                </div>
            </div>
            
            <!-- 查看模式 -->
            <div id="viewMode">
                <div class="info-grid">
                    <div class="info-section">
                        <h4>基本信息</h4>
                        <div class="info-item">
                            <span class="info-label">姓名</span>
                            <span class="info-value"><%= patient.getName() %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">性别</span>
                            <span class="info-value"><%= patient.getGender() %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">年龄</span>
                            <span class="info-value"><%= patient.getAge() != null ? patient.getAge() + "岁" : "未填写" %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">身份证号</span>
                            <span class="info-value"><%= patient.getIdCard() != null ? patient.getIdCard() : "未填写" %></span>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h4>联系信息</h4>
                        <div class="info-item">
                            <span class="info-label">手机号码</span>
                            <span class="info-value"><%= patient.getPhone() %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">家庭住址</span>
                            <span class="info-value"><%= patient.getAddress() != null ? patient.getAddress() : "未填写" %></span>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h4>紧急联系人</h4>
                        <div class="info-item">
                            <span class="info-label">联系人姓名</span>
                            <span class="info-value"><%= patient.getEmergencyContact() != null ? patient.getEmergencyContact() : "未填写" %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">联系人电话</span>
                            <span class="info-value"><%= patient.getEmergencyPhone() != null ? patient.getEmergencyPhone() : "未填写" %></span>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h4>账户信息</h4>
                        <div class="info-item">
                            <span class="info-label">患者编号</span>
                            <span class="info-value"><%= patient.getPatientNo() %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">注册时间</span>
                            <span class="info-value"><%= patient.getCreateTime() != null ? dateFormat.format(patient.getCreateTime()) : "未知" %></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">最后更新</span>
                            <span class="info-value"><%= patient.getUpdateTime() != null ? dateFormat.format(patient.getUpdateTime()) : "未更新" %></span>
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn btn-primary" onclick="toggleEditMode()">编辑信息</button>
                </div>
            </div>
            
            <!-- 编辑模式 -->
            <div id="editMode" class="edit-mode">
                <form action="${pageContext.request.contextPath}/UpdatePatientProfileServlet" method="post">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">姓名</label>
                            <input type="text" id="name" name="name" value="<%= patient.getName() %>" required>
                        </div>
                        <div class="form-group">
                            <label for="gender">性别</label>
                            <select id="gender" name="gender" required>
                                <option value="男" <%= "男".equals(patient.getGender()) ? "selected" : "" %>>男</option>
                                <option value="女" <%= "女".equals(patient.getGender()) ? "selected" : "" %>>女</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="age">年龄</label>
                            <input type="number" id="age" name="age" value="<%= patient.getAge() != null ? patient.getAge() : "" %>" min="1" max="150">
                        </div>
                        <div class="form-group">
                            <label for="phone">手机号码</label>
                            <input type="tel" id="phone" name="phone" value="<%= patient.getPhone() %>" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="idCard">身份证号</label>
                        <input type="text" id="idCard" name="idCard" value="<%= patient.getIdCard() != null ? patient.getIdCard() : "" %>" maxlength="18">
                    </div>
                    
                    <div class="form-group">
                        <label for="address">家庭住址</label>
                        <textarea id="address" name="address"><%= patient.getAddress() != null ? patient.getAddress() : "" %></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="emergencyContact">紧急联系人</label>
                            <input type="text" id="emergencyContact" name="emergencyContact" value="<%= patient.getEmergencyContact() != null ? patient.getEmergencyContact() : "" %>">
                        </div>
                        <div class="form-group">
                            <label for="emergencyPhone">紧急联系电话</label>
                            <input type="tel" id="emergencyPhone" name="emergencyPhone" value="<%= patient.getEmergencyPhone() != null ? patient.getEmergencyPhone() : "" %>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>患者编号（不可修改）</label>
                        <input type="text" value="<%= patient.getPatientNo() %>" class="readonly-field" readonly>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" class="btn btn-primary">保存修改</button>
                        <button type="button" class="btn btn-secondary" onclick="toggleEditMode()">取消</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        function toggleEditMode() {
            const viewMode = document.getElementById('viewMode');
            const editMode = document.getElementById('editMode');
            
            if (viewMode.style.display === 'none') {
                viewMode.style.display = 'block';
                editMode.style.display = 'none';
            } else {
                viewMode.style.display = 'none';
                editMode.style.display = 'block';
            }
        }
    </script>
</body>
</html>
