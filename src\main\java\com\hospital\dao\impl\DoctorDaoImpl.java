package com.hospital.dao.impl;

import com.hospital.dao.DoctorDao;
import com.hospital.entity.Doctor;
import com.hospital.util.DatabaseUtil;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 医生DAO实现类
 */
public class Doctor<PERSON>aoImpl implements DoctorDao {

    private DatabaseUtil databaseUtil;

    public void setDatabaseUtil(DatabaseUtil databaseUtil) {
        this.databaseUtil = databaseUtil;
    }
    
    @Override
    public int insert(Doctor doctor) {
        String sql = "INSERT INTO doctor (doctor_no, name, gender, age, phone, email, " +
                    "dept_id, title, speciality, password, status) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        return JdbcTemplate.update(sql, doctor.getDoctorNo(), doctor.getName(),
                doctor.getGender(), doctor.getAge(), doctor.getPhone(),
                doctor.getEmail(), doctor.getDeptId(), doctor.getTitle(),
                doctor.getSpeciality(), doctor.getPassword(), doctor.getStatus());
    }

    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM doctor WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }

    @Override
    public int update(Doctor doctor) {
        String sql = "UPDATE doctor SET name = ?, gender = ?, age = ?, phone = ?, " +
                    "email = ?, dept_id = ?, title = ?, speciality = ?, status = ? " +
                    "WHERE id = ?";
        return JdbcTemplate.update(sql, doctor.getName(), doctor.getGender(),
                doctor.getAge(), doctor.getPhone(), doctor.getEmail(),
                doctor.getDeptId(), doctor.getTitle(), doctor.getSpeciality(),
                doctor.getStatus(), doctor.getId());
    }

    @Override
    public Doctor findById(Integer id) {
        String sql = "SELECT * FROM doctor WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapDoctor, id);
    }

    @Override
    public List<Doctor> findAll() {
        String sql = "SELECT * FROM doctor ORDER BY create_time DESC";
        return JdbcTemplate.query(sql, this::mapDoctor);
    }

    @Override
    public List<Doctor> findByPage(int offset, int limit) {
        String sql = "SELECT * FROM doctor ORDER BY create_time DESC LIMIT ?, ?";
        return JdbcTemplate.query(sql, this::mapDoctor, offset, limit);
    }

    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM doctor";
        Long count = JdbcTemplate.queryForObject(sql, Long.class);
        return count != null ? count.intValue() : 0;
    }
    
    @Override
    public Doctor findByDoctorNo(String doctorNo) {
        String sql = "SELECT * FROM doctor WHERE doctor_no = ?";
        return JdbcTemplate.queryForObject(sql, this::mapDoctor, doctorNo);
    }

    @Override
    public List<Doctor> findByDeptId(Integer deptId) {
        String sql = "SELECT * FROM doctor WHERE dept_id = ? ORDER BY create_time DESC";
        return JdbcTemplate.queryForList(sql, this::mapDoctor, deptId);
    }

    @Override
    public List<Doctor> findByNameLike(String name) {
        String sql = "SELECT * FROM doctor WHERE name LIKE ? ORDER BY create_time DESC";
        return JdbcTemplate.queryForList(sql, this::mapDoctor, "%" + name + "%");
    }

    @Override
    public Doctor login(String doctorNo, String password) {
        String sql = "SELECT d.*, dept.dept_name as deptName FROM doctor d " +
                    "LEFT JOIN department dept ON d.dept_id = dept.id " +
                    "WHERE d.doctor_no = ? AND d.password = ? AND d.status = '在职'";
        return JdbcTemplate.queryForObject(sql, this::mapDoctorWithDept, doctorNo, password);
    }

    @Override
    public int updatePassword(Integer id, String newPassword) {
        String sql = "UPDATE doctor SET password = ? WHERE id = ?";
        return JdbcTemplate.update(sql, newPassword, id);
    }

    @Override
    public List<Doctor> findAllWithDept() {
        String sql = "SELECT d.*, dept.dept_name as deptName FROM doctor d " +
                    "LEFT JOIN department dept ON d.dept_id = dept.id " +
                    "ORDER BY d.create_time DESC";
        return JdbcTemplate.queryForList(sql, this::mapDoctorWithDept);
    }

    @Override
    public List<Doctor> findActiveDoctorsByDeptId(Integer deptId) {
        String sql = "SELECT * FROM doctor WHERE dept_id = ? AND status = '在职' ORDER BY name";
        return JdbcTemplate.queryForList(sql, this::mapDoctor, deptId);
    }

    @Override
    public int countByDeptId(Integer deptId) {
        String sql = "SELECT COUNT(*) FROM doctor WHERE dept_id = ? AND status = '在职'";
        Long count = JdbcTemplate.queryForObject(sql, Long.class, deptId);
        return count != null ? count.intValue() : 0;
    }

    /**
     映射ResultSet到Doctor对象
     */
    private Doctor mapDoctor(ResultSet rs) throws SQLException {
        Doctor doctor = new Doctor();
        doctor.setId(rs.getInt("id"));
        doctor.setDoctorNo(rs.getString("doctor_no"));
        doctor.setName(rs.getString("name"));
        doctor.setGender(rs.getString("gender"));
        doctor.setAge(rs.getInt("age"));
        doctor.setPhone(rs.getString("phone"));
        doctor.setEmail(rs.getString("email"));
        doctor.setDeptId(rs.getInt("dept_id"));
        doctor.setTitle(rs.getString("title"));
        doctor.setSpeciality(rs.getString("speciality"));
        doctor.setPassword(rs.getString("password"));
        doctor.setStatus(rs.getString("status"));
        doctor.setCreateTime(rs.getTimestamp("create_time"));
        return doctor;
    }

    /**
     映射ResultSet到Doctor对象（包含部门信息）
     */
    private Doctor mapDoctorWithDept(ResultSet rs) throws SQLException {
        Doctor doctor = mapDoctor(rs);
        try {
            doctor.setDeptName(rs.getString("deptName"));
        } catch (SQLException e) {
            // 忽略，可能没有部门信息
        }
        return doctor;
    }
}
