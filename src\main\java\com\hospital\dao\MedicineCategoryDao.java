package com.hospital.dao;

import com.hospital.entity.MedicineCategory;
import java.util.List;

/**
 * 药品分类DAO接口
 * 提供药品分类的数据访问方法
 */
public interface MedicineCategoryDao extends BaseDao<MedicineCategory> {
    
    /**
     * 根据分类名称查询药品分类
     */
    MedicineCategory findByCategoryName(String categoryName);
    
    /**
     * 根据药品类型查询分类列表
     * @param categoryType 药品类型（中药、西药）
     */
    List<MedicineCategory> findByCategoryType(String categoryType);
    
    /**
     * 检查分类名称是否已存在
     */
    boolean existsByCategoryName(String categoryName);
}
