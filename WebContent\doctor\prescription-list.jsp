<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.hospital.entity.*" %>
<%@ page import="java.util.*" %>
<%
    // 检查医生登录状态
    Doctor doctor = (Doctor) session.getAttribute("doctor");
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/doctor/login.jsp");
        return;
    }
    
    // 获取处方列表
    @SuppressWarnings("unchecked")
    List<Prescription> prescriptions = (List<Prescription>) request.getAttribute("prescriptions");
    if (prescriptions == null) {
        prescriptions = new ArrayList<>();
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>处方管理 - 医院管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
        }
        
        .nav-links a {
            color: #666;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            background: #667eea;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .prescription-item {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .prescription-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .prescription-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .prescription-no {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        
        .prescription-type {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .type-chinese {
            background: #fff8dc;
            color: #8b4513;
            border: 1px solid #ddd;
        }
        
        .type-western {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        
        .type-mixed {
            background: #f3e5f5;
            color: #7b1fa2;
            border: 1px solid #ce93d8;
        }
        
        .prescription-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 4px;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
        }
        
        .prescription-actions {
            text-align: right;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-left: 5px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .status-issued {
            background: #d4edda;
            color: #155724;
        }
        
        .status-dispensed {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            color: #999;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/doctor/medical-records.jsp">就诊记录</a>
                <a href="${pageContext.request.contextPath}/doctor/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>处方管理</h2>
            
            <% if (request.getParameter("success") != null) { %>
                <div class="alert alert-success">
                    <% if ("status_updated".equals(request.getParameter("success"))) { %>
                        ✅ 处方状态更新成功！
                    <% } else { %>
                        ✅ 操作成功！
                    <% } %>
                </div>
            <% } %>
            
            <% if (request.getParameter("error") != null) { %>
                <div class="alert alert-error">
                    <% if ("status_update_failed".equals(request.getParameter("error"))) { %>
                        ❌ 处方状态更新失败！
                    <% } else { %>
                        ❌ 操作失败！
                    <% } %>
                </div>
            <% } %>
            
            <% if (prescriptions.isEmpty()) { %>
                <div class="empty-state">
                    <h3>暂无处方记录</h3>
                    <p>您还没有开具任何处方，请先为患者就诊并开具处方。</p>
                </div>
            <% } else { %>
                <% for (Prescription prescription : prescriptions) { %>
                    <div class="prescription-item">
                        <div class="prescription-header">
                            <div class="prescription-no">处方编号：<%= prescription.getPrescriptionNo() %></div>
                            <div class="prescription-type <%= "中药处方".equals(prescription.getPrescriptionType()) ? "type-chinese" : 
                                                              "西药处方".equals(prescription.getPrescriptionType()) ? "type-western" : "type-mixed" %>">
                                <%= prescription.getPrescriptionType() %>
                            </div>
                        </div>
                        
                        <div class="prescription-info">
                            <div class="info-item">
                                <span class="info-label">患者姓名</span>
                                <span class="info-value"><%= prescription.getPatientName() != null ? prescription.getPatientName() : "未知" %></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">开具日期</span>
                                <span class="info-value"><%= new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm").format(prescription.getPrescriptionDate()) %></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">诊断</span>
                                <span class="info-value"><%= prescription.getDiagnosis() != null ? prescription.getDiagnosis() : "无" %></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">状态</span>
                                <span class="status <%= "已开具".equals(prescription.getStatus()) ? "status-issued" : 
                                                      "已发药".equals(prescription.getStatus()) ? "status-dispensed" : "status-cancelled" %>">
                                    <%= prescription.getStatus() %>
                                </span>
                            </div>
                        </div>
                        
                        <div class="prescription-actions">
                            <a href="${pageContext.request.contextPath}/PrescriptionServlet?action=view&id=<%= prescription.getId() %>" 
                               class="btn btn-info">查看详情</a>
                            <% if ("已开具".equals(prescription.getStatus())) { %>
                                <a href="${pageContext.request.contextPath}/PrescriptionServlet?action=updateStatus&id=<%= prescription.getId() %>&status=已发药" 
                                   class="btn btn-success" onclick="return confirm('确认标记为已发药？')">标记发药</a>
                                <a href="${pageContext.request.contextPath}/PrescriptionServlet?action=updateStatus&id=<%= prescription.getId() %>&status=已取消" 
                                   class="btn btn-warning" onclick="return confirm('确认取消此处方？')">取消处方</a>
                            <% } %>
                        </div>
                    </div>
                <% } %>
            <% } %>
        </div>
    </div>
</body>
</html>
