<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    String idStr = request.getParameter("id");
    if (idStr == null || idStr.trim().isEmpty()) {
        response.sendRedirect(request.getContextPath() + "/admin/departments.jsp?error=科室ID不能为空");
        return;
    }
    
    Integer deptId = null;
    try {
        deptId = Integer.parseInt(idStr);
    } catch (NumberFormatException e) {
        response.sendRedirect(request.getContextPath() + "/admin/departments.jsp?error=无效的科室ID");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>编辑科室信息 - 医院管理系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; margin: -30px -30px 30px -30px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 500; color: #333; }
        .form-group input, .form-group textarea { width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 1em; box-sizing: border-box; }
        .form-group input:focus, .form-group textarea:focus { outline: none; border-color: #667eea; }
        .form-group textarea { resize: vertical; min-height: 100px; }
        .required { color: #dc3545; }
        .btn { padding: 12px 30px; border: none; border-radius: 8px; font-size: 1.1em; cursor: pointer; margin-right: 15px; text-decoration: none; display: inline-block; text-align: center; }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a6fd8; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-secondary:hover { background: #5a6268; }
        .form-actions { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e1e5e9; }
        .error-message { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px; border: 1px solid #f5c6cb; }
        .success-message { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px; border: 1px solid #c3e6cb; }
        .info-box { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="header">
                <h1>🏥 编辑科室信息</h1>
                <p>科室ID: <%= deptId %> | 管理员: <%= admin.getUsername() %></p>
            </div>
            
            <!-- 显示消息 -->
            <% String success = request.getParameter("success"); %>
            <% if (success != null) { %>
                <div class="success-message">科室信息更新成功！</div>
            <% } %>
            
            <% String error = request.getParameter("error"); %>
            <% if (error != null) { %>
                <div class="error-message"><%= error %></div>
            <% } %>
            
            <div class="info-box">
                <h3>📝 编辑科室信息</h3>
                <p>请填写下面的表单来更新科室信息。标有 <span class="required">*</span> 的字段为必填项。</p>
            </div>
            
            <form action="${pageContext.request.contextPath}/UpdateDepartmentServlet" method="post">
                <input type="hidden" name="id" value="<%= deptId %>">
                
                <div class="form-group">
                    <label for="deptName">科室名称 <span class="required">*</span></label>
                    <input type="text" id="deptName" name="deptName" required 
                           placeholder="请输入科室名称">
                </div>
                
                <div class="form-group">
                    <label for="location">科室位置</label>
                    <input type="text" id="location" name="location" 
                           placeholder="例如：1楼东区">
                </div>
                
                <div class="form-group">
                    <label for="phone">联系电话</label>
                    <input type="tel" id="phone" name="phone" 
                           placeholder="例如：010-12345678">
                </div>
                
                <div class="form-group">
                    <label for="description">科室介绍</label>
                    <textarea id="description" name="description" 
                              placeholder="请输入科室的详细介绍、服务范围、特色等..."></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">保存修改</button>
                    <a href="${pageContext.request.contextPath}/admin/departments.jsp" class="btn btn-secondary">取消</a>
                </div>
            </form>
            
            <div class="info-box">
                <h3>💡 使用说明</h3>
                <ul>
                    <li>这是简化版的编辑页面，不会预加载现有数据</li>
                    <li>请手动填写所有需要的信息</li>
                    <li>科室名称是必填项，其他字段可选</li>
                    <li>点击"保存修改"提交表单</li>
                </ul>
            </div>
            
            <div class="info-box" style="background: #f8f9fa; border: 1px solid #dee2e6;">
                <h3>🔧 页面信息</h3>
                <p><strong>页面类型:</strong> 简化版编辑页面</p>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
                <p><strong>请求URI:</strong> <%= request.getRequestURI() %></p>
                <p><strong>科室ID:</strong> <%= deptId %></p>
                <p><strong>管理员:</strong> <%= admin.getUsername() %></p>
            </div>
        </div>
    </div>
    
    <script>
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const deptName = document.getElementById('deptName').value.trim();
            if (!deptName) {
                alert('科室名称不能为空！');
                e.preventDefault();
                return false;
            }
            
            if (confirm('确定要保存修改吗？\n\n注意：这将覆盖现有的科室信息。')) {
                return true;
            } else {
                e.preventDefault();
                return false;
            }
        });
    </script>
</body>
</html>
