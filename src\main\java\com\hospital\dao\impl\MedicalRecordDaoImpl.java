package com.hospital.dao.impl;

import com.hospital.dao.MedicalRecordDao;
import com.hospital.entity.MedicalRecord;
import com.hospital.util.DatabaseUtil;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 就诊记录DAO实现类
 */
public class MedicalRecordDaoImpl implements MedicalRecordDao {

    private DatabaseUtil databaseUtil;

    public void setDatabaseUtil(DatabaseUtil databaseUtil) {
        this.databaseUtil = databaseUtil;
    }
    
    @Override
    public int insert(MedicalRecord medicalRecord) {
        String sql = "INSERT INTO medical_record (reg_id, patient_id, doctor_id, visit_date, " +
                    "chief_complaint, present_illness, physical_exam, diagnosis, " +
                    "treatment_plan, prescription, advice, next_visit_date) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        return JdbcTemplate.update(sql, medicalRecord.getRegId(), medicalRecord.getPatientId(),
                medicalRecord.getDoctorId(), medicalRecord.getVisitDate(),
                medicalRecord.getChiefComplaint(), medicalRecord.getPresentIllness(),
                medicalRecord.getPhysicalExam(), medicalRecord.getDiagnosis(),
                medicalRecord.getTreatmentPlan(), medicalRecord.getPrescription(),
                medicalRecord.getAdvice(), medicalRecord.getNextVisitDate());
    }

    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM medical_record WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }

    @Override
    public int update(MedicalRecord medicalRecord) {
        String sql = "UPDATE medical_record SET chief_complaint = ?, present_illness = ?, " +
                    "physical_exam = ?, diagnosis = ?, treatment_plan = ?, prescription = ?, " +
                    "advice = ?, next_visit_date = ? WHERE id = ?";
        return JdbcTemplate.update(sql, medicalRecord.getChiefComplaint(),
                medicalRecord.getPresentIllness(), medicalRecord.getPhysicalExam(),
                medicalRecord.getDiagnosis(), medicalRecord.getTreatmentPlan(),
                medicalRecord.getPrescription(), medicalRecord.getAdvice(),
                medicalRecord.getNextVisitDate(), medicalRecord.getId());
    }
    
    @Override
    public MedicalRecord findById(Integer id) {
        String sql = "SELECT * FROM medical_record WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapMedicalRecord, id);
    }

    @Override
    public List<MedicalRecord> findAll() {
        String sql = "SELECT * FROM medical_record ORDER BY visit_date DESC";
        return JdbcTemplate.query(sql, this::mapMedicalRecord);
    }

    @Override
    public List<MedicalRecord> findByPage(int offset, int limit) {
        String sql = "SELECT * FROM medical_record ORDER BY visit_date DESC LIMIT ?, ?";
        return JdbcTemplate.query(sql, this::mapMedicalRecord, offset, limit);
    }

    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM medical_record";
        Long count = JdbcTemplate.queryForObject(sql, Long.class);
        return count != null ? count.intValue() : 0;
    }
    
    @Override
    public MedicalRecord findByRegId(Integer regId) {
        String sql = "SELECT * FROM medical_record WHERE reg_id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapMedicalRecord, regId);
    }

    @Override
    public List<MedicalRecord> findByPatientId(Integer patientId) {
        String sql = "SELECT * FROM medical_record WHERE patient_id = ? ORDER BY visit_date DESC";
        return JdbcTemplate.query(sql, this::mapMedicalRecord, patientId);
    }

    @Override
    public List<MedicalRecord> findByDoctorId(Integer doctorId) {
        String sql = "SELECT * FROM medical_record WHERE doctor_id = ? ORDER BY visit_date DESC";
        return JdbcTemplate.query(sql, this::mapMedicalRecord, doctorId);
    }

    @Override
    public List<MedicalRecord> findByVisitDate(Date visitDate) {
        String sql = "SELECT * FROM medical_record WHERE DATE(visit_date) = DATE(?) ORDER BY visit_date DESC";
        return JdbcTemplate.query(sql, this::mapMedicalRecord, visitDate);
    }
    
    @Override
    public List<MedicalRecord> findAllWithDetails() {
        String sql = "SELECT mr.*, p.name as patient_name, d.name as doctor_name, r.reg_no " +
                    "FROM medical_record mr " +
                    "LEFT JOIN patient p ON mr.patient_id = p.id " +
                    "LEFT JOIN doctor d ON mr.doctor_id = d.id " +
                    "LEFT JOIN registration r ON mr.reg_id = r.id " +
                    "ORDER BY mr.visit_date DESC";
        return JdbcTemplate.query(sql, this::mapMedicalRecordWithDetails);
    }
    
    @Override
    public List<MedicalRecord> findByPatientIdWithDetails(Integer patientId) {
        String sql = "SELECT mr.*, p.name as patient_name, d.name as doctor_name, r.reg_no " +
                    "FROM medical_record mr " +
                    "LEFT JOIN patient p ON mr.patient_id = p.id " +
                    "LEFT JOIN doctor d ON mr.doctor_id = d.id " +
                    "LEFT JOIN registration r ON mr.reg_id = r.id " +
                    "WHERE mr.patient_id = ? " +
                    "ORDER BY mr.visit_date DESC";
        return JdbcTemplate.query(sql, this::mapMedicalRecordWithDetails, patientId);
    }
    
    @Override
    public List<MedicalRecord> findByDoctorIdWithDetails(Integer doctorId) {
        String sql = "SELECT mr.*, p.name as patient_name, d.name as doctor_name, r.reg_no " +
                    "FROM medical_record mr " +
                    "LEFT JOIN patient p ON mr.patient_id = p.id " +
                    "LEFT JOIN doctor d ON mr.doctor_id = d.id " +
                    "LEFT JOIN registration r ON mr.reg_id = r.id " +
                    "WHERE mr.doctor_id = ? " +
                    "ORDER BY mr.visit_date DESC";
        return JdbcTemplate.query(sql, this::mapMedicalRecordWithDetails, doctorId);
    }
    
    @Override
    public MedicalRecord findByRegIdWithDetails(Integer regId) {
        String sql = "SELECT mr.*, p.name as patient_name, d.name as doctor_name, r.reg_no " +
                    "FROM medical_record mr " +
                    "LEFT JOIN patient p ON mr.patient_id = p.id " +
                    "LEFT JOIN doctor d ON mr.doctor_id = d.id " +
                    "LEFT JOIN registration r ON mr.reg_id = r.id " +
                    "WHERE mr.reg_id = ?";
        try {
            return JdbcTemplate.queryForObject(sql, this::mapMedicalRecordWithDetails, regId);
        } catch (Exception e) {
            return null;
        }
    }
    
    @Override
    public int countByDoctorAndDateRange(Integer doctorId, Date startDate, Date endDate) {
        String sql = "SELECT COUNT(*) FROM medical_record WHERE doctor_id = ? AND DATE(visit_date) BETWEEN DATE(?) AND DATE(?)";
        Long count = JdbcTemplate.queryForObject(sql, Long.class, doctorId, startDate, endDate);
        return count != null ? count.intValue() : 0;
    }

    @Override
    public int countByPatientId(Integer patientId) {
        String sql = "SELECT COUNT(*) FROM medical_record WHERE patient_id = ?";
        Long count = JdbcTemplate.queryForObject(sql, Long.class, patientId);
        return count != null ? count.intValue() : 0;
    }

    /**
     映射ResultSet到MedicalRecord对象
     */
    private MedicalRecord mapMedicalRecord(ResultSet rs) throws SQLException {
        MedicalRecord record = new MedicalRecord();
        record.setId(rs.getInt("id"));
        record.setRegId(rs.getInt("reg_id"));
        record.setPatientId(rs.getInt("patient_id"));
        record.setDoctorId(rs.getInt("doctor_id"));
        record.setVisitDate(rs.getTimestamp("visit_date"));
        record.setChiefComplaint(rs.getString("chief_complaint"));
        record.setPresentIllness(rs.getString("present_illness"));
        record.setPhysicalExam(rs.getString("physical_exam"));
        record.setDiagnosis(rs.getString("diagnosis"));
        record.setTreatmentPlan(rs.getString("treatment_plan"));
        record.setPrescription(rs.getString("prescription"));
        record.setAdvice(rs.getString("advice"));
        record.setNextVisitDate(rs.getDate("next_visit_date"));
        record.setCreateTime(rs.getTimestamp("create_time"));
        record.setUpdateTime(rs.getTimestamp("update_time"));
        return record;
    }

    /**
     映射ResultSet到MedicalRecord对象（包含详细信息）
     */
    private MedicalRecord mapMedicalRecordWithDetails(ResultSet rs) throws SQLException {
        MedicalRecord record = mapMedicalRecord(rs);
        try {
            record.setPatientName(rs.getString("patient_name"));
            record.setDoctorName(rs.getString("doctor_name"));
            record.setRegNo(rs.getString("reg_no"));
        } catch (SQLException e) {
            // 忽略，可能没有关联信息
        }
        return record;
    }
}
