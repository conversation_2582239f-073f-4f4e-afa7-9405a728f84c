package com.hospital.entity;

import java.util.Date;

/**
 * 中药处方特殊属性实体类
 * 用于存储中药处方的特殊属性信息，如煎煮方法、服用方法等
 * 数据库对应表：chinese_prescription_attr
 */
public class ChinesePrescriptionAttr {
    
    private Integer id;
    private Integer prescriptionId;
    private String decoctionMethod; // 煎煮方法
    private Integer decoctionTimes; // 煎煮次数
    private String usageMethod; // 服用方法
    private String dosagePerTime; // 每次用量
    private String frequency; // 服用频次
    private String notes; // 特殊说明
    private Date createTime;
    private Date updateTime;
    
    public ChinesePrescriptionAttr() {}
    
    public ChinesePrescriptionAttr(Integer prescriptionId, String decoctionMethod, 
                                  Integer decoctionTimes, String usageMethod, 
                                  String dosagePerTime, String frequency) {
        this.prescriptionId = prescriptionId;
        this.decoctionMethod = decoctionMethod;
        this.decoctionTimes = decoctionTimes;
        this.usageMethod = usageMethod;
        this.dosagePerTime = dosagePerTime;
        this.frequency = frequency;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public Integer getPrescriptionId() {
        return prescriptionId;
    }
    
    public void setPrescriptionId(Integer prescriptionId) {
        this.prescriptionId = prescriptionId;
    }
    
    public String getDecoctionMethod() {
        return decoctionMethod;
    }
    
    public void setDecoctionMethod(String decoctionMethod) {
        this.decoctionMethod = decoctionMethod;
    }
    
    public Integer getDecoctionTimes() {
        return decoctionTimes;
    }
    
    public void setDecoctionTimes(Integer decoctionTimes) {
        this.decoctionTimes = decoctionTimes;
    }
    
    public String getUsageMethod() {
        return usageMethod;
    }
    
    public void setUsageMethod(String usageMethod) {
        this.usageMethod = usageMethod;
    }
    
    public String getDosagePerTime() {
        return dosagePerTime;
    }
    
    public void setDosagePerTime(String dosagePerTime) {
        this.dosagePerTime = dosagePerTime;
    }
    
    public String getFrequency() {
        return frequency;
    }
    
    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    @Override
    public String toString() {
        return "ChinesePrescriptionAttr{" +
                "id=" + id +
                ", prescriptionId=" + prescriptionId +
                ", decoctionMethod='" + decoctionMethod + '\'' +
                ", decoctionTimes=" + decoctionTimes +
                ", usageMethod='" + usageMethod + '\'' +
                ", dosagePerTime='" + dosagePerTime + '\'' +
                ", frequency='" + frequency + '\'' +
                ", notes='" + notes + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
