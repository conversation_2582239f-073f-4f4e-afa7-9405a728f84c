<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="java.io.*" %>
<%@ page import="java.util.*" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>快速诊断 - 医院管理系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .test-btn { display: inline-block; margin: 5px; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .test-btn:hover { background: #0056b3; }
        .status-ok { color: green; }
        .status-error { color: red; }
        .status-warning { color: orange; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🔍 快速系统诊断</h1>
            
            <div class="success">
                <h2>✅ 诊断页面加载成功！</h2>
                <p>当前时间: <%= new java.util.Date() %></p>
                <p>管理员: <%= admin.getUsername() %></p>
            </div>
            
            <div class="card">
                <h3>📋 文件存在性检查</h3>
                <table>
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>路径</th>
                            <th>存在状态</th>
                            <th>大小</th>
                            <th>最后修改</th>
                        </tr>
                    </thead>
                    <tbody>
                        <%
                            String[] checkFiles = {
                                "edit-department.jsp",
                                "edit-department-test.jsp", 
                                "edit-department-backup.jsp",
                                "departments.jsp",
                                "index.jsp"
                            };
                            
                            for (String fileName : checkFiles) {
                                String filePath = application.getRealPath("/admin/" + fileName);
                                File file = new File(filePath);
                                boolean exists = file.exists();
                                long size = exists ? file.length() : 0;
                                Date lastModified = exists ? new Date(file.lastModified()) : null;
                        %>
                            <tr>
                                <td><%= fileName %></td>
                                <td><%= filePath %></td>
                                <td class="<%= exists ? "status-ok" : "status-error" %>">
                                    <%= exists ? "✅ 存在" : "❌ 不存在" %>
                                </td>
                                <td><%= exists ? size + " 字节" : "-" %></td>
                                <td><%= exists ? lastModified.toString() : "-" %></td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            </div>
            
            <div class="card">
                <h3>🔗 链接测试</h3>
                <p>点击下面的链接测试各个页面的可访问性：</p>
                
                <div style="margin: 15px 0;">
                    <h4>基础页面测试：</h4>
                    <a href="${pageContext.request.contextPath}/admin/index.jsp" class="test-btn" target="_blank">管理员首页</a>
                    <a href="${pageContext.request.contextPath}/admin/departments.jsp" class="test-btn" target="_blank">科室管理</a>
                </div>
                
                <div style="margin: 15px 0;">
                    <h4>编辑页面测试：</h4>
                    <a href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=1" class="test-btn" target="_blank">原始编辑页面(ID=1)</a>
                    <a href="${pageContext.request.contextPath}/admin/edit-department-backup.jsp?id=1" class="test-btn" target="_blank">备用编辑页面(ID=1)</a>
                    <a href="${pageContext.request.contextPath}/admin/edit-department-test.jsp?id=1" class="test-btn" target="_blank">测试页面(ID=1)</a>
                </div>
                
                <div style="margin: 15px 0;">
                    <h4>不同ID测试：</h4>
                    <a href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=2" class="test-btn" target="_blank">编辑科室2</a>
                    <a href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=3" class="test-btn" target="_blank">编辑科室3</a>
                    <a href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=7" class="test-btn" target="_blank">编辑科室7</a>
                </div>
            </div>
            
            <div class="card">
                <h3>🔧 系统信息</h3>
                <table>
                    <tr>
                        <td><strong>服务器信息</strong></td>
                        <td><%= application.getServerInfo() %></td>
                    </tr>
                    <tr>
                        <td><strong>Java版本</strong></td>
                        <td><%= System.getProperty("java.version") %></td>
                    </tr>
                    <tr>
                        <td><strong>操作系统</strong></td>
                        <td><%= System.getProperty("os.name") %> <%= System.getProperty("os.version") %></td>
                    </tr>
                    <tr>
                        <td><strong>应用路径</strong></td>
                        <td><%= application.getRealPath("/") %></td>
                    </tr>
                    <tr>
                        <td><strong>Context Path</strong></td>
                        <td><%= request.getContextPath() %></td>
                    </tr>
                    <tr>
                        <td><strong>请求URI</strong></td>
                        <td><%= request.getRequestURI() %></td>
                    </tr>
                    <tr>
                        <td><strong>服务器端口</strong></td>
                        <td><%= request.getServerPort() %></td>
                    </tr>
                </table>
            </div>
            
            <div class="card">
                <h3>📝 测试结果记录</h3>
                <div class="info">
                    <p><strong>测试说明：</strong></p>
                    <ol>
                        <li>点击上面的测试链接，记录每个链接的访问结果</li>
                        <li>如果出现404错误，记录具体的错误页面</li>
                        <li>如果出现其他错误，记录错误信息</li>
                        <li>将测试结果反馈给技术支持</li>
                    </ol>
                </div>
                
                <div class="info">
                    <p><strong>常见结果解释：</strong></p>
                    <ul>
                        <li><span class="status-ok">✅ 正常显示</span> - 页面加载成功</li>
                        <li><span class="status-error">❌ 404错误</span> - 页面未找到</li>
                        <li><span class="status-warning">⚠️ 500错误</span> - 服务器内部错误</li>
                        <li><span class="status-warning">⚠️ 其他错误</span> - 需要具体分析</li>
                    </ul>
                </div>
            </div>
            
            <div class="card">
                <h3>🚀 下一步行动建议</h3>
                <div class="info">
                    <p><strong>根据测试结果采取行动：</strong></p>
                    <ul>
                        <li><strong>如果所有页面都正常</strong> - 问题可能已解决</li>
                        <li><strong>如果只有edit-department.jsp有问题</strong> - 使用备用页面</li>
                        <li><strong>如果多个页面有问题</strong> - 需要重启服务器</li>
                        <li><strong>如果基础页面都有问题</strong> - 检查服务器状态</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 自动刷新页面信息
        setTimeout(function() {
            document.querySelector('h2').innerHTML = '✅ 诊断页面运行正常 - ' + new Date().toLocaleString();
        }, 1000);
    </script>
</body>
</html>
