package com.hospital.dao.impl;

import com.hospital.dao.ChinesePrescriptionAttrDao;
import com.hospital.entity.ChinesePrescriptionAttr;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 中药处方特殊属性DAO实现类
 */
public class ChinesePrescriptionAttrDaoImpl implements ChinesePrescriptionAttrDao {
    
    @Override
    public int insert(ChinesePrescriptionAttr attr) {
        String sql = "INSERT INTO chinese_prescription_attr (prescription_id, decoction_method, " +
                    "decoction_times, usage_method, dosage_per_time, frequency, notes) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?)";
        return JdbcTemplate.update(sql, attr.getPrescriptionId(), attr.getDecoctionMethod(),
                attr.getDecoctionTimes(), attr.getUsageMethod(), attr.getDosagePerTime(),
                attr.getFrequency(), attr.getNotes());
    }
    
    @Override
    public int update(ChinesePrescriptionAttr attr) {
        String sql = "UPDATE chinese_prescription_attr SET decoction_method = ?, decoction_times = ?, " +
                    "usage_method = ?, dosage_per_time = ?, frequency = ?, notes = ? WHERE id = ?";
        return JdbcTemplate.update(sql, attr.getDecoctionMethod(), attr.getDecoctionTimes(),
                attr.getUsageMethod(), attr.getDosagePerTime(), attr.getFrequency(),
                attr.getNotes(), attr.getId());
    }
    
    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM chinese_prescription_attr WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }
    
    @Override
    public ChinesePrescriptionAttr findById(Integer id) {
        String sql = "SELECT * FROM chinese_prescription_attr WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToChinesePrescriptionAttr, id);
    }
    
    @Override
    public List<ChinesePrescriptionAttr> findAll() {
        String sql = "SELECT * FROM chinese_prescription_attr";
        return JdbcTemplate.queryForList(sql, this::mapRowToChinesePrescriptionAttr);
    }
    
    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM chinese_prescription_attr";
        return JdbcTemplate.queryForInt(sql);
    }
    
    @Override
    public List<ChinesePrescriptionAttr> findByPage(int offset, int size) {
        String sql = "SELECT * FROM chinese_prescription_attr LIMIT ?, ?";
        return JdbcTemplate.queryForList(sql, this::mapRowToChinesePrescriptionAttr, offset, size);
    }
    
    @Override
    public ChinesePrescriptionAttr findByPrescriptionId(Integer prescriptionId) {
        String sql = "SELECT * FROM chinese_prescription_attr WHERE prescription_id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToChinesePrescriptionAttr, prescriptionId);
    }
    
    @Override
    public int deleteByPrescriptionId(Integer prescriptionId) {
        String sql = "DELETE FROM chinese_prescription_attr WHERE prescription_id = ?";
        return JdbcTemplate.update(sql, prescriptionId);
    }
    
    /**
     * 将ResultSet映射为ChinesePrescriptionAttr对象
     */
    private ChinesePrescriptionAttr mapRowToChinesePrescriptionAttr(ResultSet rs) throws SQLException {
        ChinesePrescriptionAttr attr = new ChinesePrescriptionAttr();
        attr.setId(rs.getInt("id"));
        attr.setPrescriptionId(rs.getInt("prescription_id"));
        attr.setDecoctionMethod(rs.getString("decoction_method"));
        attr.setDecoctionTimes(rs.getInt("decoction_times"));
        attr.setUsageMethod(rs.getString("usage_method"));
        attr.setDosagePerTime(rs.getString("dosage_per_time"));
        attr.setFrequency(rs.getString("frequency"));
        attr.setNotes(rs.getString("notes"));
        attr.setCreateTime(rs.getTimestamp("create_time"));
        attr.setUpdateTime(rs.getTimestamp("update_time"));
        return attr;
    }
}
