package com.hospital.util;

import java.sql.Connection;
import java.sql.SQLException;

/**
 简单的事务管理器，替代Spring的事务管理
 */
public class TransactionManager {
    
    private static final ThreadLocal<Connection> connectionHolder = new ThreadLocal<>();
    
    /**
     开始事务
     */
    public static void beginTransaction() {
        try {
            Connection conn = DatabaseUtil.getConnection();
            conn.setAutoCommit(false);
            connectionHolder.set(conn);
        } catch (SQLException e) {
            throw new RuntimeException("Failed to begin transaction", e);
        }
    }
    
    /**
     提交事务
     */
    public static void commit() {
        Connection conn = connectionHolder.get();
        if (conn != null) {
            try {
                conn.commit();
            } catch (SQLException e) {
                throw new RuntimeException("Failed to commit transaction", e);
            } finally {
                closeConnection();
            }
        }
    }
    
    /**
     回滚事务
     */
    public static void rollback() {
        Connection conn = connectionHolder.get();
        if (conn != null) {
            try {
                conn.rollback();
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                closeConnection();
            }
        }
    }
    
    /**
     获取当前事务的连接
     */
    public static Connection getCurrentConnection() {
        Connection conn = connectionHolder.get();
        if (conn != null) {
            return conn;
        }
        // 如果没有事务，返回新连接
        try {
            return DatabaseUtil.getConnection();
        } catch (SQLException e) {
            throw new RuntimeException("Failed to get connection", e);
        }
    }
    
    /**
     检查是否在事务中
     */
    public static boolean isInTransaction() {
        return connectionHolder.get() != null;
    }
    
    /**
     关闭连接并清理ThreadLocal
     */
    private static void closeConnection() {
        Connection conn = connectionHolder.get();
        if (conn != null) {
            try {
                conn.setAutoCommit(true);
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                connectionHolder.remove();
            }
        }
    }
    
    /**
     执行事务操作
     */
    public static <T> T executeInTransaction(TransactionCallback<T> callback) {
        beginTransaction();
        try {
            T result = callback.doInTransaction();
            commit();
            return result;
        } catch (Exception e) {
            rollback();
            throw new RuntimeException("Transaction failed", e);
        }
    }
    
    /**
     事务回调接口
     */
    public interface TransactionCallback<T> {
        T doInTransaction() throws Exception;
    }
}
