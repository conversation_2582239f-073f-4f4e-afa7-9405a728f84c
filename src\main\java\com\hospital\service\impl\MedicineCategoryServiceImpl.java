package com.hospital.service.impl;

import com.hospital.dao.MedicineCategoryDao;
import com.hospital.entity.MedicineCategory;
import com.hospital.service.MedicineCategoryService;

import java.util.List;

/**
 * 药品分类服务实现类
 */
public class MedicineCategoryServiceImpl implements MedicineCategoryService {
    
    private MedicineCategoryDao medicineCategoryDao;
    
    public void setMedicineCategoryDao(MedicineCategoryDao medicineCategoryDao) {
        this.medicineCategoryDao = medicineCategoryDao;
    }
    
    @Override
    public boolean addMedicineCategory(MedicineCategory category) {
        // 检查分类名称是否已存在
        if (isCategoryNameExists(category.getCategoryName())) {
            return false;
        }
        
        return medicineCategoryDao.insert(category) > 0;
    }
    
    @Override
    public boolean updateMedicineCategory(MedicineCategory category) {
        return medicineCategoryDao.update(category) > 0;
    }
    
    @Override
    public boolean deleteMedicineCategory(Integer id) {
        return medicineCategoryDao.deleteById(id) > 0;
    }
    
    @Override
    public MedicineCategory findById(Integer id) {
        return medicineCategoryDao.findById(id);
    }
    
    @Override
    public List<MedicineCategory> findAll() {
        return medicineCategoryDao.findAll();
    }
    
    @Override
    public MedicineCategory findByCategoryName(String categoryName) {
        return medicineCategoryDao.findByCategoryName(categoryName);
    }
    
    @Override
    public List<MedicineCategory> findByCategoryType(String categoryType) {
        return medicineCategoryDao.findByCategoryType(categoryType);
    }
    
    @Override
    public boolean isCategoryNameExists(String categoryName) {
        return medicineCategoryDao.existsByCategoryName(categoryName);
    }
    
    @Override
    public List<MedicineCategory> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return medicineCategoryDao.findByPage(offset, size);
    }
    
    @Override
    public int count() {
        return medicineCategoryDao.count();
    }
}
