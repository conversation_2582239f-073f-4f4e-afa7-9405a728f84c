<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.MedicalRecord" %>
<%@ page import="com.hospital.service.MedicalRecordService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    // 获取服务
    MedicalRecordService medicalRecordService = JspServiceUtil.getMedicalRecordService(application);
    
    List<MedicalRecord> medicalRecords = medicalRecordService.findAllWithDetails();
    SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>就诊记录管理 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .stats-bar {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stat-item h4 {
            margin: 0 0 5px 0;
            font-size: 1.8em;
        }
        
        .stat-item p {
            margin: 0;
            opacity: 0.9;
        }
        
        .filter-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-bar input {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1em;
        }
        
        .filter-bar input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            font-size: 1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .record-list {
            display: grid;
            gap: 20px;
        }
        
        .record-item {
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            padding: 25px;
            background: #fafafa;
            transition: box-shadow 0.3s ease;
        }
        
        .record-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #667eea;
        }
        
        .record-info {
            flex: 1;
        }
        
        .patient-name {
            font-weight: bold;
            color: #333;
            font-size: 1.2em;
            margin-bottom: 5px;
        }
        
        .doctor-name {
            color: #667eea;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .reg-no {
            color: #666;
            font-size: 0.9em;
            font-family: monospace;
        }
        
        .visit-time {
            color: #667eea;
            font-weight: 500;
            text-align: right;
        }
        
        .record-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .content-section {
            background: white;
            padding: 18px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .content-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            font-size: 1em;
            display: flex;
            align-items: center;
        }
        
        .content-label::before {
            margin-right: 8px;
            font-size: 1.2em;
        }
        
        .complaint-section .content-label::before {
            content: "📋";
        }
        
        .diagnosis-section .content-label::before {
            content: "🔍";
        }
        
        .treatment-section .content-label::before {
            content: "💊";
        }
        
        .prescription-section .content-label::before {
            content: "📝";
        }
        
        .advice-section .content-label::before {
            content: "⚠️";
        }
        
        .content-text {
            color: #555;
            line-height: 1.6;
            font-size: 0.95em;
        }
        
        .record-actions {
            text-align: right;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            color: #ccc;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>就诊记录管理</h2>
            
            <% if (request.getParameter("success") != null) { %>
                <div class="alert alert-success">
                    <%= request.getParameter("success") %>
                </div>
            <% } %>
            
            <% if (request.getParameter("error") != null) { %>
                <div class="alert alert-error">
                    <%= request.getParameter("error") %>
                </div>
            <% } %>
            

            
            <div class="filter-bar">
                <label>患者姓名：</label>
                <input type="text" id="patientFilter" placeholder="输入患者姓名" onkeyup="filterRecords()">
                
                <label>医生姓名：</label>
                <input type="text" id="doctorFilter" placeholder="输入医生姓名" onkeyup="filterRecords()">
                
                <label>就诊日期：</label>
                <input type="date" id="dateFilter" onchange="filterRecords()">
                

            </div>
            
            <% if (medicalRecords != null && !medicalRecords.isEmpty()) { %>
                <div class="record-list" id="recordList">
                    <% for (MedicalRecord record : medicalRecords) { %>
                        <div class="record-item" 
                             data-patient="<%= record.getPatientName() != null ? record.getPatientName().toLowerCase() : "" %>" 
                             data-doctor="<%= record.getDoctorName() != null ? record.getDoctorName().toLowerCase() : "" %>"
                             data-date="<%= dateFormat.format(record.getVisitDate()) %>">
                            <div class="record-header">
                                <div class="record-info">
                                    <div class="patient-name">患者：<%= record.getPatientName() != null ? record.getPatientName() : "未知" %></div>
                                    <div class="doctor-name">主诊医生：<%= record.getDoctorName() != null ? record.getDoctorName() : "未知" %></div>
                                    <div class="reg-no">挂号单号：<%= record.getRegNo() != null ? record.getRegNo() : "未知" %></div>
                                </div>
                                <div class="visit-time">
                                    <%= dateTimeFormat.format(record.getVisitDate()) %>
                                </div>
                            </div>
                            
                            <div class="record-content">
                                <div class="content-section complaint-section">
                                    <div class="content-label">主诉</div>
                                    <div class="content-text"><%= record.getChiefComplaint() != null ? record.getChiefComplaint() : "-" %></div>
                                </div>
                                
                                <div class="content-section diagnosis-section">
                                    <div class="content-label">诊断结果</div>
                                    <div class="content-text"><%= record.getDiagnosis() != null ? record.getDiagnosis() : "-" %></div>
                                </div>
                                
                                <% if (record.getTreatmentPlan() != null && !record.getTreatmentPlan().trim().isEmpty()) { %>
                                <div class="content-section treatment-section">
                                    <div class="content-label">治疗方案</div>
                                    <div class="content-text"><%= record.getTreatmentPlan() %></div>
                                </div>
                                <% } %>
                                
                                <% if (record.getPrescription() != null && !record.getPrescription().trim().isEmpty()) { %>
                                <div class="content-section prescription-section">
                                    <div class="content-label">处方药物</div>
                                    <div class="content-text"><%= record.getPrescription() %></div>
                                </div>
                                <% } %>
                                
                                <% if (record.getAdvice() != null && !record.getAdvice().trim().isEmpty()) { %>
                                <div class="content-section advice-section">
                                    <div class="content-label">医嘱建议</div>
                                    <div class="content-text"><%= record.getAdvice() %></div>
                                </div>
                                <% } %>
                                
                                <% if (record.getNextVisitDate() != null) { %>
                                <div class="content-section">
                                    <div class="content-label">下次复诊</div>
                                    <div class="content-text"><%= dateFormat.format(record.getNextVisitDate()) %></div>
                                </div>
                                <% } %>
                            </div>
                            

                        </div>
                    <% } %>
                </div>
            <% } else { %>
                <div class="empty-state">
                    <div style="font-size: 4em; margin-bottom: 20px;">📄</div>
                    <h3>暂无就诊记录</h3>
                    <p>系统中还没有就诊记录。</p>
                </div>
            <% } %>
        </div>
    </div>
    
    <script>
        function filterRecords() {
            const patientFilter = document.getElementById('patientFilter').value.toLowerCase();
            const doctorFilter = document.getElementById('doctorFilter').value.toLowerCase();
            const dateFilter = document.getElementById('dateFilter').value;
            const recordList = document.getElementById('recordList');
            
            if (!recordList) return;
            
            const records = recordList.getElementsByClassName('record-item');
            
            for (let i = 0; i < records.length; i++) {
                const record = records[i];
                const patient = record.getAttribute('data-patient');
                const doctor = record.getAttribute('data-doctor');
                const date = record.getAttribute('data-date');
                
                let showRecord = true;
                
                // 患者姓名筛选
                if (patientFilter && !patient.includes(patientFilter)) {
                    showRecord = false;
                }
                
                // 医生姓名筛选
                if (doctorFilter && !doctor.includes(doctorFilter)) {
                    showRecord = false;
                }
                
                // 日期筛选
                if (dateFilter && date !== dateFilter) {
                    showRecord = false;
                }
                
                record.style.display = showRecord ? '' : 'none';
            }
        }
        

    </script>
</body>
</html>
