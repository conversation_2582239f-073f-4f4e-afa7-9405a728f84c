-- 医院挂号就诊管理系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS hospital_management DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE hospital_management;

-- 科室信息表
CREATE TABLE IF NOT EXISTS department (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dept_name VARCHAR(50) NOT NULL COMMENT '科室名称',
    dept_desc TEXT COMMENT '科室描述',
    location VARCHAR(100) COMMENT '科室位置',
    phone VARCHAR(20) COMMENT '科室电话',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '科室信息表';

-- 医生信息表
CREATE TABLE IF NOT EXISTS doctor (
    id INT PRIMARY KEY AUTO_INCREMENT,
    doctor_no VARCHAR(20) UNIQUE NOT NULL COMMENT '医生工号',
    name VARCHAR(50) NOT NULL COMMENT '医生姓名',
    gender ENUM('男', '女') NOT NULL COMMENT '性别',
    age INT COMMENT '年龄',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    dept_id INT NOT NULL COMMENT '所属科室ID',
    title VARCHAR(20) COMMENT '职称',
    speciality TEXT COMMENT '专业特长',
    password VARCHAR(100) NOT NULL COMMENT '登录密码',
    status ENUM('在职', '离职') DEFAULT '在职' COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dept_id) REFERENCES department(id)
) COMMENT '医生信息表';

-- 患者信息表
CREATE TABLE IF NOT EXISTS patient (
    id INT PRIMARY KEY AUTO_INCREMENT,
    patient_no VARCHAR(20) UNIQUE NOT NULL COMMENT '患者编号',
    name VARCHAR(50) NOT NULL COMMENT '患者姓名',
    gender ENUM('男', '女') NOT NULL COMMENT '性别',
    age INT COMMENT '年龄',
    phone VARCHAR(20) COMMENT '联系电话',
    id_card VARCHAR(18) COMMENT '身份证号',
    address TEXT COMMENT '家庭住址',
    emergency_contact VARCHAR(50) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
    password VARCHAR(100) NOT NULL COMMENT '登录密码',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '患者信息表';

-- 挂号信息表
CREATE TABLE IF NOT EXISTS registration (
    id INT PRIMARY KEY AUTO_INCREMENT,
    reg_no VARCHAR(30) UNIQUE NOT NULL COMMENT '挂号单号',
    patient_id INT NOT NULL COMMENT '患者ID',
    doctor_id INT NOT NULL COMMENT '医生ID',
    dept_id INT NOT NULL COMMENT '科室ID',
    reg_date DATE NOT NULL COMMENT '挂号日期',
    reg_time TIME NOT NULL COMMENT '挂号时间段',
    reg_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '挂号费',
    status ENUM('已挂号', '已就诊', '已取消') DEFAULT '已挂号' COMMENT '状态',
    symptoms TEXT COMMENT '主要症状',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patient(id),
    FOREIGN KEY (doctor_id) REFERENCES doctor(id),
    FOREIGN KEY (dept_id) REFERENCES department(id)
) COMMENT '挂号信息表';

-- 就诊记录表
CREATE TABLE IF NOT EXISTS medical_record (
    id INT PRIMARY KEY AUTO_INCREMENT,
    reg_id INT NOT NULL COMMENT '挂号ID',
    patient_id INT NOT NULL COMMENT '患者ID',
    doctor_id INT NOT NULL COMMENT '医生ID',
    visit_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '就诊时间',
    chief_complaint TEXT COMMENT '主诉',
    present_illness TEXT COMMENT '现病史',
    physical_exam TEXT COMMENT '体格检查',
    diagnosis TEXT COMMENT '诊断',
    treatment_plan TEXT COMMENT '治疗方案',
    prescription TEXT COMMENT '处方',
    advice TEXT COMMENT '医嘱',
    next_visit_date DATE COMMENT '下次复诊日期',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (reg_id) REFERENCES registration(id),
    FOREIGN KEY (patient_id) REFERENCES patient(id),
    FOREIGN KEY (doctor_id) REFERENCES doctor(id)
) COMMENT '就诊记录表';

-- 管理员表
CREATE TABLE IF NOT EXISTS admin (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    role ENUM('超级管理员', '普通管理员') DEFAULT '普通管理员' COMMENT '角色',
    status ENUM('启用', '禁用') DEFAULT '启用' COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '管理员表';

-- 药品分类表
CREATE TABLE IF NOT EXISTS medicine_category (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(50) NOT NULL COMMENT '分类名称',
    category_type ENUM('中药', '西药') NOT NULL COMMENT '药品类型',
    description TEXT COMMENT '分类描述',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '药品分类表';

-- 药品信息表
CREATE TABLE IF NOT EXISTS medicine (
    id INT PRIMARY KEY AUTO_INCREMENT,
    medicine_code VARCHAR(20) UNIQUE NOT NULL COMMENT '药品编码',
    medicine_name VARCHAR(100) NOT NULL COMMENT '药品名称',
    medicine_type ENUM('中药', '西药') NOT NULL COMMENT '药品类型',
    category_id INT COMMENT '所属分类ID',
    specification VARCHAR(100) COMMENT '规格',
    dosage_form VARCHAR(50) COMMENT '剂型',
    unit VARCHAR(20) NOT NULL COMMENT '单位',
    price DECIMAL(10,2) NOT NULL COMMENT '单价',
    stock INT DEFAULT 0 COMMENT '库存',
    manufacturer VARCHAR(100) COMMENT '生产厂家',
    approval_number VARCHAR(50) COMMENT '批准文号',
    description TEXT COMMENT '药品说明',
    usage_method TEXT COMMENT '用法',
    status ENUM('在用', '停用') DEFAULT '在用' COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES medicine_category(id)
) COMMENT '药品信息表';

-- 中药特殊属性表
CREATE TABLE IF NOT EXISTS chinese_medicine_attr (
    id INT PRIMARY KEY AUTO_INCREMENT,
    medicine_id INT NOT NULL COMMENT '药品ID',
    nature VARCHAR(50) COMMENT '药性',
    taste VARCHAR(50) COMMENT '药味',
    meridian_tropism VARCHAR(100) COMMENT '归经',
    processing_method TEXT COMMENT '炮制方法',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (medicine_id) REFERENCES medicine(id) ON DELETE CASCADE
) COMMENT '中药特殊属性表';

-- 处方主表
CREATE TABLE IF NOT EXISTS prescription (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prescription_no VARCHAR(30) UNIQUE NOT NULL COMMENT '处方编号',
    medical_record_id INT NOT NULL COMMENT '关联的就诊记录ID',
    patient_id INT NOT NULL COMMENT '患者ID',
    doctor_id INT NOT NULL COMMENT '医生ID',
    prescription_type ENUM('中药处方', '西药处方', '中西药混合') NOT NULL COMMENT '处方类型',
    diagnosis TEXT COMMENT '诊断',
    prescription_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开具日期',
    status ENUM('已开具', '已发药', '已取消') DEFAULT '已开具' COMMENT '处方状态',
    notes TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (medical_record_id) REFERENCES medical_record(id),
    FOREIGN KEY (patient_id) REFERENCES patient(id),
    FOREIGN KEY (doctor_id) REFERENCES doctor(id)
) COMMENT '处方主表';

-- 处方明细表
CREATE TABLE IF NOT EXISTS prescription_detail (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prescription_id INT NOT NULL COMMENT '处方ID',
    medicine_id INT NOT NULL COMMENT '药品ID',
    medicine_name VARCHAR(100) NOT NULL COMMENT '药品名称',
    medicine_type ENUM('中药', '西药') NOT NULL COMMENT '药品类型',
    specification VARCHAR(100) COMMENT '规格',
    dosage DECIMAL(10,2) NOT NULL COMMENT '剂量',
    dosage_unit VARCHAR(20) NOT NULL COMMENT '剂量单位',
    frequency VARCHAR(50) COMMENT '用药频次',
    usage_method TEXT COMMENT '用法',
    days INT COMMENT '用药天数',
    quantity DECIMAL(10,2) NOT NULL COMMENT '数量',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    total_price DECIMAL(10,2) NOT NULL COMMENT '总价',
    notes TEXT COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (prescription_id) REFERENCES prescription(id) ON DELETE CASCADE,
    FOREIGN KEY (medicine_id) REFERENCES medicine(id)
) COMMENT '处方明细表';

-- 中药处方特殊属性表
CREATE TABLE IF NOT EXISTS chinese_prescription_attr (
    id INT PRIMARY KEY AUTO_INCREMENT,
    prescription_id INT NOT NULL COMMENT '处方ID',
    decoction_method TEXT COMMENT '煎煮方法',
    decoction_times INT DEFAULT 1 COMMENT '煎煮次数',
    usage_method TEXT COMMENT '服用方法',
    dosage_per_time VARCHAR(50) COMMENT '每次用量',
    frequency VARCHAR(50) COMMENT '服用频次',
    notes TEXT COMMENT '特殊说明',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (prescription_id) REFERENCES prescription(id) ON DELETE CASCADE
) COMMENT '中药处方特殊属性表';

-- 插入初始数据
-- 插入科室数据
INSERT INTO department (dept_name, dept_desc, location, phone) VALUES
('内科', '内科疾病诊疗', '1楼101室', '010-12345678'),
('外科', '外科手术治疗', '2楼201室', '010-12345679'),
('儿科', '儿童疾病诊疗', '3楼301室', '010-12345680'),
('妇产科', '妇科产科诊疗', '4楼401室', '010-12345681'),
('骨科', '骨科疾病治疗', '5楼501室', '010-12345682');

-- 插入管理员数据
INSERT INTO admin (username, password, name, phone, email, role) VALUES
('admin', '123456', '系统管理员', '13800138000', '<EMAIL>', '超级管理员');

-- 插入医生数据
INSERT INTO doctor (doctor_no, name, gender, age, phone, email, dept_id, title, speciality, password) VALUES
('D001', '张医生', '男', 45, '13800138001', '<EMAIL>', 1, '主任医师', '心血管疾病', '123456'),
('D002', '李医生', '女', 38, '13800138002', '<EMAIL>', 2, '副主任医师', '普外科手术', '123456'),
('D003', '王医生', '男', 42, '13800138003', '<EMAIL>', 3, '主治医师', '儿童呼吸科', '123456'),
('D004', '赵医生', '女', 40, '13800138004', '<EMAIL>', 4, '主任医师', '妇科肿瘤', '123456'),
('D005', '刘医生', '男', 35, '13800138005', '<EMAIL>', 5, '主治医师', '骨折治疗', '123456');

-- 插入患者数据
INSERT INTO patient (patient_no, name, gender, age, phone, id_card, address, emergency_contact, emergency_phone, password) VALUES
('P001', '张三', '男', 30, '13900139001', '110101199001011234', '北京市朝阳区', '李四', '13900139002', '123456'),
('P002', '李梅', '女', 25, '13900139003', '110101199501011234', '北京市海淀区', '王五', '13900139004', '123456');

-- 插入药品分类数据
INSERT INTO medicine_category (category_name, category_type, description) VALUES
('抗生素类', '西药', '用于治疗细菌感染的药物'),
('解热镇痛类', '西药', '用于缓解疼痛和退热的药物'),
('心血管类', '西药', '用于治疗心血管疾病的药物'),
('消化系统类', '西药', '用于治疗消化系统疾病的药物'),
('呼吸系统类', '西药', '用于治疗呼吸系统疾病的药物'),
('解表药', '中药', '用于治疗风寒感冒的药物'),
('清热药', '中药', '用于清热解毒的药物'),
('补益药', '中药', '用于补气养血的药物'),
('活血化瘀药', '中药', '用于活血化瘀的药物'),
('平肝息风药', '中药', '用于平肝息风的药物');

-- 插入药品数据
INSERT INTO medicine (medicine_code, medicine_name, medicine_type, category_id, specification, dosage_form, unit, price, stock, manufacturer, usage_method, status) VALUES
('XY001', '阿莫西林胶囊', '西药', 1, '0.25g*24粒', '胶囊', '盒', 15.80, 1000, '国药集团', '口服，一次1粒，一日3次', '在用'),
('XY002', '布洛芬片', '西药', 2, '0.2g*24片', '片剂', '盒', 12.50, 1000, '白云山制药', '口服，一次1片，一日3次', '在用'),
('XY003', '硝苯地平片', '西药', 3, '10mg*30片', '片剂', '盒', 18.60, 800, '上海医药', '口服，一次1片，一日3次', '在用'),
('XY004', '奥美拉唑肠溶胶囊', '西药', 4, '20mg*14粒', '胶囊', '盒', 25.40, 600, '华润医药', '口服，一次1粒，一日2次', '在用'),
('XY005', '氨溴索片', '西药', 5, '30mg*20片', '片剂', '盒', 22.80, 700, '哈药集团', '口服，一次1片，一日3次', '在用'),
('ZY001', '金银花', '中药', 6, '500g/袋', '中药饮片', '克', 0.15, 10000, '同仁堂', '煎煮后服用', '在用'),
('ZY002', '板蓝根', '中药', 7, '500g/袋', '中药饮片', '克', 0.12, 10000, '同仁堂', '煎煮后服用', '在用'),
('ZY003', '人参', '中药', 8, '100g/袋', '中药饮片', '克', 2.50, 5000, '同仁堂', '煎煮后服用', '在用'),
('ZY004', '当归', '中药', 9, '500g/袋', '中药饮片', '克', 0.18, 8000, '同仁堂', '煎煮后服用', '在用'),
('ZY005', '天麻', '中药', 10, '500g/袋', '中药饮片', '克', 0.35, 6000, '同仁堂', '煎煮后服用', '在用');

-- 插入中药特殊属性数据
INSERT INTO chinese_medicine_attr (medicine_id, nature, taste, meridian_tropism, processing_method) VALUES
(6, '寒', '甘、苦', '肺、心、胃', '洗净切段'),
(7, '寒', '苦', '心、肺、胃', '洗净切片'),
(8, '温', '甘、微苦', '脾、肺', '切片或整根使用'),
(9, '温', '甘、辛', '肝、心、脾', '切片'),
(10, '平', '甘', '肝、胆', '切片');
