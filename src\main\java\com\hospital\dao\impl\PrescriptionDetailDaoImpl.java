package com.hospital.dao.impl;

import com.hospital.dao.PrescriptionDetailDao;
import com.hospital.entity.PrescriptionDetail;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 处方明细DAO实现类
 */
public class PrescriptionDetailDaoImpl implements PrescriptionDetailDao {
    
    @Override
    public int insert(PrescriptionDetail detail) {
        String sql = "INSERT INTO prescription_detail (prescription_id, medicine_id, medicine_name, " +
                    "medicine_type, specification, dosage, dosage_unit, frequency, usage_method, " +
                    "days, quantity, unit_price, total_price, notes) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        return JdbcTemplate.update(sql, detail.getPrescriptionId(), detail.getMedicineId(),
                detail.getMedicineName(), detail.getMedicineType(), detail.getSpecification(),
                detail.getDosage(), detail.getDosageUnit(), detail.getFrequency(),
                detail.getUsageMethod(), detail.getDays(), detail.getQuantity(),
                detail.getUnitPrice(), detail.getTotalPrice(), detail.getNotes());
    }
    
    @Override
    public int update(PrescriptionDetail detail) {
        String sql = "UPDATE prescription_detail SET prescription_id = ?, medicine_id = ?, " +
                    "medicine_name = ?, medicine_type = ?, specification = ?, dosage = ?, " +
                    "dosage_unit = ?, frequency = ?, usage_method = ?, days = ?, quantity = ?, " +
                    "unit_price = ?, total_price = ?, notes = ? WHERE id = ?";
        return JdbcTemplate.update(sql, detail.getPrescriptionId(), detail.getMedicineId(),
                detail.getMedicineName(), detail.getMedicineType(), detail.getSpecification(),
                detail.getDosage(), detail.getDosageUnit(), detail.getFrequency(),
                detail.getUsageMethod(), detail.getDays(), detail.getQuantity(),
                detail.getUnitPrice(), detail.getTotalPrice(), detail.getNotes(), detail.getId());
    }
    
    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM prescription_detail WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }
    
    @Override
    public PrescriptionDetail findById(Integer id) {
        String sql = "SELECT * FROM prescription_detail WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToPrescriptionDetail, id);
    }
    
    @Override
    public List<PrescriptionDetail> findAll() {
        String sql = "SELECT * FROM prescription_detail";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescriptionDetail);
    }
    
    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM prescription_detail";
        return JdbcTemplate.queryForInt(sql);
    }
    
    @Override
    public List<PrescriptionDetail> findByPage(int offset, int size) {
        String sql = "SELECT * FROM prescription_detail LIMIT ?, ?";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescriptionDetail, offset, size);
    }
    
    @Override
    public List<PrescriptionDetail> findByPrescriptionId(Integer prescriptionId) {
        String sql = "SELECT * FROM prescription_detail WHERE prescription_id = ?";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescriptionDetail, prescriptionId);
    }
    
    @Override
    public List<PrescriptionDetail> findByMedicineId(Integer medicineId) {
        String sql = "SELECT * FROM prescription_detail WHERE medicine_id = ?";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescriptionDetail, medicineId);
    }
    
    @Override
    public List<PrescriptionDetail> findByMedicineType(String medicineType) {
        String sql = "SELECT * FROM prescription_detail WHERE medicine_type = ?";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescriptionDetail, medicineType);
    }
    
    @Override
    public List<PrescriptionDetail> findByPrescriptionIdWithMedicine(Integer prescriptionId) {
        String sql = "SELECT pd.*, m.medicine_code, mc.category_name " +
                    "FROM prescription_detail pd " +
                    "LEFT JOIN medicine m ON pd.medicine_id = m.id " +
                    "LEFT JOIN medicine_category mc ON m.category_id = mc.id " +
                    "WHERE pd.prescription_id = ?";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescriptionDetailWithMedicine, prescriptionId);
    }
    
    @Override
    public int batchInsert(List<PrescriptionDetail> details) {
        if (details == null || details.isEmpty()) {
            return 0;
        }
        
        String sql = "INSERT INTO prescription_detail (prescription_id, medicine_id, medicine_name, " +
                    "medicine_type, specification, dosage, dosage_unit, frequency, usage_method, " +
                    "days, quantity, unit_price, total_price, notes) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        int totalRows = 0;
        for (PrescriptionDetail detail : details) {
            totalRows += JdbcTemplate.update(sql, detail.getPrescriptionId(), detail.getMedicineId(),
                    detail.getMedicineName(), detail.getMedicineType(), detail.getSpecification(),
                    detail.getDosage(), detail.getDosageUnit(), detail.getFrequency(),
                    detail.getUsageMethod(), detail.getDays(), detail.getQuantity(),
                    detail.getUnitPrice(), detail.getTotalPrice(), detail.getNotes());
        }
        return totalRows;
    }
    
    @Override
    public int deleteByPrescriptionId(Integer prescriptionId) {
        String sql = "DELETE FROM prescription_detail WHERE prescription_id = ?";
        return JdbcTemplate.update(sql, prescriptionId);
    }
    
    /**
     * 将ResultSet映射为PrescriptionDetail对象
     */
    private PrescriptionDetail mapRowToPrescriptionDetail(ResultSet rs) throws SQLException {
        PrescriptionDetail detail = new PrescriptionDetail();
        detail.setId(rs.getInt("id"));
        detail.setPrescriptionId(rs.getInt("prescription_id"));
        detail.setMedicineId(rs.getInt("medicine_id"));
        detail.setMedicineName(rs.getString("medicine_name"));
        detail.setMedicineType(rs.getString("medicine_type"));
        detail.setSpecification(rs.getString("specification"));
        detail.setDosage(rs.getBigDecimal("dosage"));
        detail.setDosageUnit(rs.getString("dosage_unit"));
        detail.setFrequency(rs.getString("frequency"));
        detail.setUsageMethod(rs.getString("usage_method"));
        detail.setDays(rs.getInt("days"));
        detail.setQuantity(rs.getBigDecimal("quantity"));
        detail.setUnitPrice(rs.getBigDecimal("unit_price"));
        detail.setTotalPrice(rs.getBigDecimal("total_price"));
        detail.setNotes(rs.getString("notes"));
        detail.setCreateTime(rs.getTimestamp("create_time"));
        detail.setUpdateTime(rs.getTimestamp("update_time"));
        return detail;
    }
    
    /**
     * 将ResultSet映射为PrescriptionDetail对象（包含药品信息）
     */
    private PrescriptionDetail mapRowToPrescriptionDetailWithMedicine(ResultSet rs) throws SQLException {
        PrescriptionDetail detail = mapRowToPrescriptionDetail(rs);
        detail.setMedicineCode(rs.getString("medicine_code"));
        detail.setCategoryName(rs.getString("category_name"));
        return detail;
    }
}
