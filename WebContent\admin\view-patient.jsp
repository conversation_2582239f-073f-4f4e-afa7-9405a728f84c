<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    String patientId = request.getParameter("id");
    if (patientId == null) {
        patientId = "1";
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>患者详情 - 医院管理系统</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }
        .patient-info {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .patient-info h3 {
            margin: 0 0 10px 0;
            font-size: 1.8em;
        }
        .patient-info p {
            margin: 5px 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        .info-section h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e1e5e9;
        }
        .info-item:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }
        .info-label {
            font-weight: 500;
            color: #666;
            min-width: 100px;
        }
        .info-value {
            color: #333;
            flex: 1;
            text-align: right;
            font-weight: 500;
        }
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-right: 15px;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/patients.jsp">返回患者管理</a>
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card">
            <h2>患者详情</h2>

            <div id="loadingDiv" class="loading">正在加载患者信息...</div>

            <div id="patientContent" style="display: none">
                <div class="patient-info">
                    <h3 id="patientName">-</h3>
                    <p id="patientBasicInfo">-</p>
                    <p id="patientNo">患者编号：-</p>
                    <p id="createTime">注册时间：-</p>
                </div>

                <div class="info-grid">
                    <div class="info-section">
                        <h4>基本信息</h4>
                        <div class="info-item">
                            <span class="info-label">姓名</span>
                            <span class="info-value" id="name">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">性别</span>
                            <span class="info-value" id="gender">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">年龄</span>
                            <span class="info-value" id="age">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">身份证号</span>
                            <span class="info-value" id="idCard">-</span>
                        </div>
                    </div>

                    <div class="info-section">
                        <h4>联系信息</h4>
                        <div class="info-item">
                            <span class="info-label">手机号码</span>
                            <span class="info-value" id="phone">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">家庭住址</span>
                            <span class="info-value" id="address">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">紧急联系人</span>
                            <span class="info-value" id="emergencyContact">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">紧急联系电话</span>
                            <span class="info-value" id="emergencyPhone">-</span>
                        </div>
                    </div>

                    <div class="info-section">
                        <h4>系统信息</h4>
                        <div class="info-item">
                            <span class="info-label">患者ID</span>
                            <span class="info-value" id="patientId">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">患者编号</span>
                            <span class="info-value" id="patientNumber">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">注册时间</span>
                            <span class="info-value" id="registerTime">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">最后更新</span>
                            <span class="info-value" id="updateTime">-</span>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px">
                    <a href="${pageContext.request.contextPath}/admin/patients.jsp" class="btn btn-secondary">返回患者列表</a>
                </div>
            </div>

            <div id="errorDiv" style="display: none; text-align: center; padding: 40px; color: #dc3545;">
                <h3>加载失败</h3>
                <p>无法加载患者信息，请检查患者ID是否正确。</p>
                <a href="${pageContext.request.contextPath}/admin/patients.jsp" class="btn btn-secondary">返回患者列表</a>
            </div>
        </div>
    </div>

    <script>
        // 使用JavaScript加载患者数据，避免JSP代码被压缩的问题
        document.addEventListener("DOMContentLoaded", function () {
            const patientId = "<%= patientId %>";
            loadPatientData(patientId);
        });

        function loadPatientData(patientId) {
            // 调用后端API获取真实患者数据
            fetch("${pageContext.request.contextPath}/PatientDetailServlet?id=" + patientId)
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        displayPatientData(data);
                    } else {
                        console.error("获取患者信息失败:", data.error);
                        showError();
                    }
                })
                .catch((error) => {
                    console.error("请求失败:", error);
                    showError();
                });
        }

        function displayPatientData(patient) {
            document.getElementById("loadingDiv").style.display = "none";
            document.getElementById("patientContent").style.display = "block";

            // 更新患者信息
            document.getElementById("patientName").textContent = patient.name;
            document.getElementById("patientBasicInfo").textContent =
                patient.gender + " | " + (patient.age ? patient.age + "岁" : "年龄未知");
            document.getElementById("patientNo").textContent = "患者编号：" + patient.patientNo;
            document.getElementById("createTime").textContent = "注册时间：" + patient.createTime;

            // 更新详细信息
            document.getElementById("name").textContent = patient.name;
            document.getElementById("gender").textContent = patient.gender;
            document.getElementById("age").textContent = patient.age ? patient.age + "岁" : "未填写";
            document.getElementById("idCard").textContent = patient.idCard || "未填写";
            document.getElementById("phone").textContent = patient.phone;
            document.getElementById("address").textContent = patient.address || "未填写";
            document.getElementById("emergencyContact").textContent = patient.emergencyContact || "未填写";
            document.getElementById("emergencyPhone").textContent = patient.emergencyPhone || "未填写";
            document.getElementById("patientId").textContent = patient.id;
            document.getElementById("patientNumber").textContent = patient.patientNo;
            document.getElementById("registerTime").textContent = patient.createTime;
            document.getElementById("updateTime").textContent = patient.updateTime || "未更新";
        }

        function showError() {
            document.getElementById("loadingDiv").style.display = "none";
            document.getElementById("errorDiv").style.display = "block";
        }
    </script>
</body>
</html>
