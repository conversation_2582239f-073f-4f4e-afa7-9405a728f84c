package com.hospital.service;

import com.hospital.entity.Registration;
import java.util.Date;
import java.util.List;

/**
 * 挂号服务接口
 */
public interface RegistrationService {
    
    /**
     患者挂号
     */
    boolean register(Registration registration);
    
    /**
     取消挂号
     */
    boolean cancelRegistration(Integer id, Integer patientId);
    
    /**
     根据ID查询挂号信息
     */
    Registration findById(Integer id);
    
    /**
     根据挂号单号查询挂号信息
     */
    Registration findByRegNo(String regNo);
    
    /**
     根据患者ID查询挂号记录
     */
    List<Registration> findByPatientId(Integer patientId);
    
    /**
     根据医生ID查询挂号记录
     */
    List<Registration> findByDoctorId(Integer doctorId);
    
    /**
     查询所有挂号记录（包含详情）
     */
    List<Registration> findAllWithDetails();
    
    /**
     根据患者ID查询挂号记录（包含详情）
     */
    List<Registration> findByPatientIdWithDetails(Integer patientId);
    
    /**
     根据医生ID查询挂号记录（包含详情）
     */
    List<Registration> findByDoctorIdWithDetails(Integer doctorId);
    
    /**
     更新挂号状态
     */
    boolean updateStatus(Integer id, String status);
    
    /**
     生成挂号单号
     */
    String generateRegNo();
    
    /**
     验证挂号单号是否存在
     */
    boolean isRegNoExists(String regNo);
    
    /**
     统计医生工作量
     */
    int countByDoctorAndDateRange(Integer doctorId, Date startDate, Date endDate);
    
    /**
     分页查询挂号记录
     */
    List<Registration> findByPage(int page, int size);
    
    /**
     统计挂号总数
     */
    int count();

    /**
     统计指定科室的挂号数量
     */
    int countByDeptId(Integer deptId);

    /**
     统计指定科室本月的挂号数量
     */
    int countByDeptIdAndMonth(Integer deptId);


}
