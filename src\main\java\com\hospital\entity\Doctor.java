package com.hospital.entity;

import java.util.Date;

/**
 医生实体类
 该类用于表示医院管理系统中的医生信息，包含医生的基本信息、职业信息、
 所属科室、专业特长等字段，以及系统相关的时间戳等。
 数据库对应表：doctor
 */
public class Doctor {
    private Integer id;
    private String doctorNo;
    private String name;
    private String gender;
    private Integer age;
    private String phone;
    private String email;
    private Integer deptId;
    /**
     医生职称
     取值范围：住院医师、主治医师、副主任医师、主任医师等
     */
    private String title;
    /**
     医生专业特长
     医生的专业领域和擅长治疗的疾病类型
     用于患者选择医生时的参考
     */
    private String speciality;
    private String password;
    private String status;
    private Date createTime;
    private Date updateTime;
    /**
     科室名称
     非数据库字段，用于页面显示时避免多次查询
     通过连表查询或业务逻辑填充
     */
    private String deptName;
    public Doctor() {}

    public Doctor(String doctorNo, String name, String gender, Integer age,
                 String phone, String email, Integer deptId, String title,
                 String speciality, String password) {
        this.doctorNo = doctorNo;
        this.name = name;
        this.gender = gender;
        this.age = age;
        this.phone = phone;
        this.email = email;
        this.deptId = deptId;
        this.title = title;
        this.speciality = speciality;
        this.password = password;
        this.status = "在职"; // 默认状态为在职
    }

    // ==================== Getter和Setter方法 ====================

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDoctorNo() {
        return doctorNo;
    }

    public void setDoctorNo(String doctorNo) {
        this.doctorNo = doctorNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSpeciality() {
        return speciality;
    }

    public void setSpeciality(String speciality) {
        this.speciality = speciality;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    /**
     重写toString方法
     用于调试和日志输出，返回医生对象的字符串表示
     注意：为了安全考虑，密码字段不包含在toString输出中
     */
    @Override
    public String toString() {
        return "Doctor{" +
                "id=" + id +
                ", doctorNo='" + doctorNo + '\'' +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", age=" + age +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", deptId=" + deptId +
                ", title='" + title + '\'' +
                ", speciality='" + speciality + '\'' +
                ", status='" + status + '\'' +
                ", deptName='" + deptName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
