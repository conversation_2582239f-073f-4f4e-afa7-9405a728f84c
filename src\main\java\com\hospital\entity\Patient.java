package com.hospital.entity;

import java.util.Date;

/**
 患者实体类
 该类用于表示医院管理系统中的患者信息，包含患者的基本信息、联系方式、
 紧急联系人信息以及系统相关的时间戳等字段。
 数据库对应表：patient
 */
public class Patient {


    private Integer id;
    private String patientNo;
    private String name;
    private String gender;
    private Integer age;
    private String phone;
    private String idCard;
    private String address;
    private String emergencyContact;
    private String emergencyPhone;
    private String password;
    private Date createTime;
    private Date updateTime;
    public Patient() {}

    public Patient(String patientNo, String name, String gender, Integer age,
                  String phone, String idCard, String address, String emergencyContact,
                  String emergencyPhone, String password) {
        this.patientNo = patientNo;
        this.name = name;
        this.gender = gender;
        this.age = age;
        this.phone = phone;
        this.idCard = idCard;
        this.address = address;
        this.emergencyContact = emergencyContact;
        this.emergencyPhone = emergencyPhone;
        this.password = password;
    }

    // ==================== Getter和Setter方法 ====================

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPatientNo() {
        return patientNo;
    }

    public void setPatientNo(String patientNo) {
        this.patientNo = patientNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getEmergencyContact() {
        return emergencyContact;
    }

    public void setEmergencyContact(String emergencyContact) {
        this.emergencyContact = emergencyContact;
    }

    public String getEmergencyPhone() {
        return emergencyPhone;
    }

    public void setEmergencyPhone(String emergencyPhone) {
        this.emergencyPhone = emergencyPhone;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     重写toString方法
     用于调试和日志输出，返回患者对象的字符串表示
     注意：为了安全考虑，密码字段不包含在toString输出中
     */
    @Override
    public String toString() {
        return "Patient{" +
                "id=" + id +
                ", patientNo='" + patientNo + '\'' +
                ", name='" + name + '\'' +
                ", gender='" + gender + '\'' +
                ", age=" + age +
                ", phone='" + phone + '\'' +
                ", idCard='" + idCard + '\'' +
                ", address='" + address + '\'' +
                ", emergencyContact='" + emergencyContact + '\'' +
                ", emergencyPhone='" + emergencyPhone + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
