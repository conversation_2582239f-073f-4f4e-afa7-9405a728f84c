<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>最小编辑测试</title>
</head>
<body>
    <h1>最小编辑测试页面</h1>
    
    <p>当前时间: <%= new java.util.Date() %></p>
    <p>请求URI: <%= request.getRequestURI() %></p>
    <p>参数ID: <%= request.getParameter("id") %></p>
    
    <%
        // 检查管理员登录
        Admin admin = (Admin) session.getAttribute("admin");
        if (admin == null) {
            out.println("<p style='color: red;'>❌ 管理员未登录</p>");
            out.println("<p><a href='" + request.getContextPath() + "/login.jsp'>点击登录</a></p>");
        } else {
            out.println("<p style='color: green;'>✅ 管理员已登录: " + admin.getUsername() + "</p>");
            
            String idStr = request.getParameter("id");
            if (idStr == null) {
                out.println("<p style='color: red;'>❌ 缺少ID参数</p>");
            } else {
                out.println("<p style='color: green;'>✅ ID参数: " + idStr + "</p>");
                
                try {
                    Integer deptId = Integer.parseInt(idStr);
                    out.println("<p style='color: green;'>✅ ID解析成功: " + deptId + "</p>");
                    
                    // 现在测试服务获取
                    out.println("<p>正在测试服务获取...</p>");
                    
                } catch (Exception e) {
                    out.println("<p style='color: red;'>❌ ID解析失败: " + e.getMessage() + "</p>");
                }
            }
        }
    %>
    
    <hr>
    <p><a href="<%= request.getContextPath() %>/admin/minimal-edit-test.jsp?id=1">测试ID=1</a></p>
    <p><a href="<%= request.getContextPath() %>/admin/departments.jsp">返回科室管理</a></p>
    
</body>
</html>
