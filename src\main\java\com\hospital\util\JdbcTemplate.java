package com.hospital.util;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 简单的JDBC模板类
 */
public class JdbcTemplate {
    
    /**
     执行插入操作并返回生成的主键ID
     */
    public static int insertAndReturnKey(String sql, Object... params) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        boolean shouldCloseConnection = false;

        try {
            // 检查是否在事务中
            if (TransactionManager.isInTransaction()) {
                conn = TransactionManager.getCurrentConnection();
            } else {
                conn = DatabaseUtil.getConnection();
                shouldCloseConnection = true;
            }

            pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);

            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new RuntimeException("Creating record failed, no rows affected.");
            }

            rs = pstmt.getGeneratedKeys();
            if (rs.next()) {
                return rs.getInt(1);
            } else {
                throw new RuntimeException("Creating record failed, no ID obtained.");
            }

        } catch (SQLException e) {
            throw new RuntimeException("Failed to execute insert: " + sql, e);
        } finally {
            closeResources(rs, pstmt, shouldCloseConnection ? conn : null);
        }
    }

    /**
     执行更新操作（INSERT、UPDATE、DELETE）
     */
    public static int update(String sql, Object... params) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        boolean shouldCloseConnection = false;

        try {
            // 检查是否在事务中
            if (TransactionManager.isInTransaction()) {
                conn = TransactionManager.getCurrentConnection();
            } else {
                conn = DatabaseUtil.getConnection();
                shouldCloseConnection = true;
            }

            pstmt = conn.prepareStatement(sql);

            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }

            return pstmt.executeUpdate();

        } catch (SQLException e) {
            throw new RuntimeException("Failed to execute update: " + sql, e);
        } finally {
            closeResources(null, pstmt, shouldCloseConnection ? conn : null);
        }
    }
    
    /**
     执行查询操作，返回单个结果
     */
    public static <T> T queryForObject(String sql, RowMapper<T> rowMapper, Object... params) {
        List<T> results = query(sql, rowMapper, params);
        if (results.isEmpty()) {
            return null;
        }
        if (results.size() > 1) {
            throw new RuntimeException("Expected single result, but found " + results.size());
        }
        return results.get(0);
    }
    
    /**
     执行查询操作，返回结果列表
     */
    public static <T> List<T> queryForList(String sql, RowMapper<T> rowMapper, Object... params) {
        return query(sql, rowMapper, params);
    }

    /**
     执行查询操作，返回结果列表
     */
    public static <T> List<T> query(String sql, RowMapper<T> rowMapper, Object... params) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<T> results = new ArrayList<>();
        boolean shouldCloseConnection = false;

        try {
            // 检查是否在事务中
            if (TransactionManager.isInTransaction()) {
                conn = TransactionManager.getCurrentConnection();
            } else {
                conn = DatabaseUtil.getConnection();
                shouldCloseConnection = true;
            }

            pstmt = conn.prepareStatement(sql);

            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }

            rs = pstmt.executeQuery();

            while (rs.next()) {
                T obj = rowMapper.mapRow(rs);
                results.add(obj);
            }

            return results;

        } catch (SQLException e) {
            throw new RuntimeException("Failed to execute query: " + sql, e);
        } finally {
            closeResources(rs, pstmt, shouldCloseConnection ? conn : null);
        }
    }
    
    /**
     查询整数值
     */
    public static int queryForInt(String sql, Object... params) {
        Integer result = queryForObject(sql, Integer.class, params);
        return result != null ? result : 0;
    }

    /**
     查询单个值
     */
    public static <T> T queryForObject(String sql, Class<T> requiredType, Object... params) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        boolean shouldCloseConnection = false;

        try {
            // 检查是否在事务中
            if (TransactionManager.isInTransaction()) {
                conn = TransactionManager.getCurrentConnection();
            } else {
                conn = DatabaseUtil.getConnection();
                shouldCloseConnection = true;
            }

            pstmt = conn.prepareStatement(sql);

            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }

            rs = pstmt.executeQuery();

            if (rs.next()) {
                Object value = rs.getObject(1);
                return requiredType.cast(value);
            }

            return null;

        } catch (SQLException e) {
            throw new RuntimeException("Failed to execute query: " + sql, e);
        } finally {
            closeResources(rs, pstmt, shouldCloseConnection ? conn : null);
        }
    }
    
    /**
     关闭资源
     */
    private static void closeResources(ResultSet rs, PreparedStatement pstmt, Connection conn) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     行映射器接口
     */
    public interface RowMapper<T> {
        T mapRow(ResultSet rs) throws SQLException;
    }
}
