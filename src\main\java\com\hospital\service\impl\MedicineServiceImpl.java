package com.hospital.service.impl;

import com.hospital.dao.MedicineDao;
import com.hospital.dao.ChineseMedicineAttrDao;
import com.hospital.entity.Medicine;
import com.hospital.entity.ChineseMedicineAttr;
import com.hospital.service.MedicineService;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 药品服务实现类
 */
public class MedicineServiceImpl implements MedicineService {
    
    private MedicineDao medicineDao;
    private ChineseMedicineAttrDao chineseMedicineAttrDao;
    
    public void setMedicineDao(MedicineDao medicineDao) {
        this.medicineDao = medicineDao;
    }
    
    public void setChineseMedicineAttrDao(ChineseMedicineAttrDao chineseMedicineAttrDao) {
        this.chineseMedicineAttrDao = chineseMedicineAttrDao;
    }
    
    @Override
    public boolean addMedicine(Medicine medicine) {
        // 检查药品编码是否已存在
        if (isMedicineCodeExists(medicine.getMedicineCode())) {
            return false;
        }
        
        // 插入药品基本信息
        boolean success = medicineDao.insert(medicine) > 0;
        
        // 如果是中药且有特殊属性，插入中药特殊属性
        if (success && "中药".equals(medicine.getMedicineType()) && medicine.getChineseMedicineAttr() != null) {
            ChineseMedicineAttr attr = medicine.getChineseMedicineAttr();
            attr.setMedicineId(medicine.getId());
            chineseMedicineAttrDao.insert(attr);
        }
        
        return success;
    }
    
    @Override
    public boolean updateMedicine(Medicine medicine) {
        boolean success = medicineDao.update(medicine) > 0;
        
        // 如果是中药，更新中药特殊属性
        if (success && "中药".equals(medicine.getMedicineType()) && medicine.getChineseMedicineAttr() != null) {
            ChineseMedicineAttr attr = medicine.getChineseMedicineAttr();
            attr.setMedicineId(medicine.getId());
            
            // 检查是否已存在中药属性
            ChineseMedicineAttr existingAttr = chineseMedicineAttrDao.findByMedicineId(medicine.getId());
            if (existingAttr != null) {
                attr.setId(existingAttr.getId());
                chineseMedicineAttrDao.update(attr);
            } else {
                chineseMedicineAttrDao.insert(attr);
            }
        }
        
        return success;
    }
    
    @Override
    public boolean deleteMedicine(Integer id) {
        // 先删除中药特殊属性（如果存在）
        chineseMedicineAttrDao.deleteByMedicineId(id);
        
        // 删除药品
        return medicineDao.deleteById(id) > 0;
    }
    
    @Override
    public Medicine findById(Integer id) {
        Medicine medicine = medicineDao.findById(id);
        
        // 如果是中药，查询中药特殊属性
        if (medicine != null && "中药".equals(medicine.getMedicineType())) {
            ChineseMedicineAttr attr = chineseMedicineAttrDao.findByMedicineId(id);
            medicine.setChineseMedicineAttr(attr);
        }
        
        return medicine;
    }
    
    @Override
    public List<Medicine> findAll() {
        return medicineDao.findAll();
    }
    
    @Override
    public Medicine findByMedicineCode(String medicineCode) {
        return medicineDao.findByMedicineCode(medicineCode);
    }
    
    @Override
    public List<Medicine> findByMedicineNameLike(String medicineName) {
        return medicineDao.findByMedicineNameLike(medicineName);
    }
    
    @Override
    public List<Medicine> findByMedicineType(String medicineType) {
        return medicineDao.findByMedicineType(medicineType);
    }
    
    @Override
    public List<Medicine> findByCategoryId(Integer categoryId) {
        return medicineDao.findByCategoryId(categoryId);
    }
    
    @Override
    public List<Medicine> findByStatus(String status) {
        return medicineDao.findByStatus(status);
    }
    
    @Override
    public List<Medicine> findAllWithCategory() {
        return medicineDao.findAllWithCategory();
    }
    
    @Override
    public List<Medicine> findByMedicineTypeWithCategory(String medicineType) {
        return medicineDao.findByMedicineTypeWithCategory(medicineType);
    }
    
    @Override
    public List<Medicine> findByMedicineNameLikeWithCategory(String medicineName) {
        return medicineDao.findByMedicineNameLikeWithCategory(medicineName);
    }
    
    @Override
    public boolean isMedicineCodeExists(String medicineCode) {
        return medicineDao.existsByMedicineCode(medicineCode);
    }
    
    @Override
    public boolean updateStock(Integer medicineId, Integer stock) {
        return medicineDao.updateStock(medicineId, stock) > 0;
    }
    
    @Override
    public boolean reduceStock(Integer medicineId, Integer quantity) {
        return medicineDao.reduceStock(medicineId, quantity) > 0;
    }
    
    @Override
    public List<Medicine> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return medicineDao.findByPage(offset, size);
    }
    
    @Override
    public int count() {
        return medicineDao.count();
    }
    
    @Override
    public String generateMedicineCode(String medicineType) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        
        String prefix = "中药".equals(medicineType) ? "ZY" : "XY";
        
        // 生成药品编码：前缀 + 日期 + 3位随机数
        int random = (int) (Math.random() * 1000);
        String code = String.format("%s%s%03d", prefix, dateStr, random);
        
        // 检查编码是否已存在，如果存在则重新生成
        while (isMedicineCodeExists(code)) {
            random = (int) (Math.random() * 1000);
            code = String.format("%s%s%03d", prefix, dateStr, random);
        }
        
        return code;
    }
}
