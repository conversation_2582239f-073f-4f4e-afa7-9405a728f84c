/*
 Navicat Premium Dump SQL

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80037 (8.0.37)
 Source Host           : localhost:3306
 Source Schema         : hospital_management

 Target Server Type    : MySQL
 Target Server Version : 80037 (8.0.37)
 File Encoding         : 65001

 Date: 26/06/2025 17:27:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `role` enum('超级管理员','普通管理员') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '普通管理员' COMMENT '角色',
  `status` enum('启用','禁用') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '启用' COMMENT '状态',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of admin
-- ----------------------------
INSERT INTO `admin` VALUES (1, 'admin', '123456', '系统管理员', '13800138000', '<EMAIL>', '超级管理员', '启用', '2025-06-12 18:04:21', '2025-06-12 18:04:21');
INSERT INTO `admin` VALUES (2, 'admin1', '123456', '后台管理员', '13547661254', '<EMAIL>', '普通管理员', '启用', '2025-06-22 01:52:56', '2025-06-22 01:53:28');
INSERT INTO `admin` VALUES (3, 'admin2', '123456', '维护管理员', '17820454622', '<EMAIL>', '普通管理员', '启用', '2025-06-22 01:54:39', '2025-06-22 01:54:39');
INSERT INTO `admin` VALUES (4, 'admin3', '123456', '运营管理员', '15815507988', '<EMAIL>', '普通管理员', '启用', '2025-06-22 01:56:56', '2025-06-22 01:56:56');

-- ----------------------------
-- Table structure for department
-- ----------------------------
DROP TABLE IF EXISTS `department`;
CREATE TABLE `department`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '科室名称',
  `dept_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '科室描述',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '科室位置',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '科室电话',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '科室信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of department
-- ----------------------------
INSERT INTO `department` VALUES (1, '内科', '内科疾病诊疗', '1楼101室', '010-12345678', '2025-06-12 18:04:21', '2025-06-14 03:22:54');
INSERT INTO `department` VALUES (2, '外科', '外科手术治疗', '2楼201室', '010-12345679', '2025-06-12 18:04:21', '2025-06-12 18:04:21');
INSERT INTO `department` VALUES (3, '儿科', '儿童疾病诊疗', '3楼301室', '010-12345680', '2025-06-12 18:04:21', '2025-06-12 18:04:21');
INSERT INTO `department` VALUES (4, '妇产科', '妇科产科诊疗', '4楼401室', '010-12345681', '2025-06-12 18:04:21', '2025-06-12 18:04:21');
INSERT INTO `department` VALUES (5, '骨科', '骨科疾病治疗', '5楼501室', '010-12345679', '2025-06-12 18:04:21', '2025-06-22 02:03:31');
INSERT INTO `department` VALUES (6, '心外科', '心外科手术治疗', '2楼202室', '010-12345679', '2025-06-22 02:00:20', '2025-06-22 02:00:35');
INSERT INTO `department` VALUES (7, '胸外科', '胸外科手术治疗', '2楼203室', '010-12345679', '2025-06-22 02:01:17', '2025-06-22 02:01:57');
INSERT INTO `department` VALUES (8, '泌尿外科', '泌尿外科手术治疗', '2楼204室', '010-12345679', '2025-06-22 02:02:52', '2025-06-22 02:03:19');
INSERT INTO `department` VALUES (9, '神经科', '神经科手术治疗', '2楼205室', '010-12345679', '2025-06-22 02:04:10', '2025-06-22 02:04:51');
INSERT INTO `department` VALUES (10, '心内科', '心内科疾病诊疗', '1楼102室', '010-12345678', '2025-06-22 02:05:35', '2025-06-22 02:05:58');
INSERT INTO `department` VALUES (11, '消化内科', '消化内科疾病诊疗', '1楼103室', '010-12345678', '2025-06-22 02:06:18', '2025-06-22 02:06:56');
INSERT INTO `department` VALUES (12, '神经内科', '神经内科疾病诊疗', '1楼104室', '010-12345678', '2025-06-22 02:07:28', '2025-06-22 02:07:39');
INSERT INTO `department` VALUES (13, '肾内科', '肾内科疾病诊疗', '1楼105室', '010-12345678', '2025-06-22 02:08:20', '2025-06-22 02:08:29');
INSERT INTO `department` VALUES (14, '内分泌科', '内分泌科疾病诊疗', '1楼106室', '010-12345678', '2025-06-22 02:09:02', '2025-06-22 02:10:05');
INSERT INTO `department` VALUES (15, '呼吸内科', '呼吸内科疾病诊疗', '1楼107室', '010-12345678', '2025-06-22 02:10:42', '2025-06-22 02:10:51');
INSERT INTO `department` VALUES (16, '血液内科', '血液内科疾病诊疗', '1楼108室', '010-12345678', '2025-06-22 02:11:26', '2025-06-22 02:11:40');
INSERT INTO `department` VALUES (17, '康复科', '康复科诊疗', '6楼601室', '010-12345699', '2025-06-22 02:13:44', '2025-06-22 02:13:44');
INSERT INTO `department` VALUES (18, '中医科', '中医诊疗', '6楼602室', '010-12345677', '2025-06-22 02:15:10', '2025-06-22 02:18:26');
INSERT INTO `department` VALUES (19, '疼痛科', '疼痛科疾病诊疗', '6楼603室', '010-12345676', '2025-06-22 02:16:01', '2025-06-22 02:18:37');
INSERT INTO `department` VALUES (20, '耳鼻喉科', '耳鼻喉科疾病诊疗', '7楼701室', '010-12345656', '2025-06-22 02:18:16', '2025-06-22 02:18:57');
INSERT INTO `department` VALUES (21, '眼科', '眼科诊疗', '7楼702室', '010-12345656', '2025-06-22 02:19:09', '2025-06-22 02:19:45');
INSERT INTO `department` VALUES (22, '口腔科', '口腔科诊疗', '7楼703室', '010-12345656', '2025-06-22 02:20:18', '2025-06-22 02:20:18');
INSERT INTO `department` VALUES (23, '皮肤科', '皮肤科诊疗', '7楼704室', '010-12345656', '2025-06-22 02:21:25', '2025-06-22 02:21:25');
INSERT INTO `department` VALUES (24, '急诊医学科', '急诊治疗', '1楼109室', '010-12345674', '2025-06-22 02:22:32', '2025-06-22 02:24:34');
INSERT INTO `department` VALUES (25, '放射科', '放射科检查诊疗', '8楼801室', '010-12345666', '2025-06-22 02:24:24', '2025-06-22 02:24:24');
INSERT INTO `department` VALUES (26, '超声科', '超声科检查诊疗', '8楼802室', '010-12345666', '2025-06-22 02:24:59', '2025-06-22 02:25:29');
INSERT INTO `department` VALUES (27, '检验科', '检验科检测诊疗', '8楼803室', '010-12345666', '2025-06-22 02:28:01', '2025-06-22 02:28:01');
INSERT INTO `department` VALUES (28, '麻醉科', '麻醉诊疗', '8楼804室', '010-12345666', '2025-06-22 02:28:58', '2025-06-22 02:28:58');
INSERT INTO `department` VALUES (29, 'ICU', '抢救病危疾病诊疗', '8楼805室', '010-12345666', '2025-06-22 02:29:47', '2025-06-22 02:31:03');

-- ----------------------------
-- Table structure for doctor
-- ----------------------------
DROP TABLE IF EXISTS `doctor`;
CREATE TABLE `doctor`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `doctor_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '医生工号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '医生姓名',
  `gender` enum('男','女') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '性别',
  `age` int NULL DEFAULT NULL COMMENT '年龄',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `dept_id` int NOT NULL COMMENT '所属科室ID',
  `title` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '职称',
  `speciality` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '专业特长',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录密码',
  `status` enum('在职','离职') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '在职' COMMENT '状态',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `doctor_no`(`doctor_no` ASC) USING BTREE,
  INDEX `dept_id`(`dept_id` ASC) USING BTREE,
  CONSTRAINT `doctor_ibfk_1` FOREIGN KEY (`dept_id`) REFERENCES `department` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '医生信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of doctor
-- ----------------------------
INSERT INTO `doctor` VALUES (1, 'D001', '张医生', '男', 45, '13800138001', '<EMAIL>', 1, '主任医师', '心血管疾病', '123456', '在职', '2025-06-12 18:04:21', '2025-06-22 02:36:27');
INSERT INTO `doctor` VALUES (2, 'D002', '李医生', '女', 38, '13800138002', '<EMAIL>', 2, '副主任医师', '普外科手术', '123456', '在职', '2025-06-12 18:04:21', '2025-06-12 18:04:21');
INSERT INTO `doctor` VALUES (3, 'D003', '王医生', '男', 42, '13800138003', '<EMAIL>', 3, '主任医师', '儿童呼吸科', '123456', '在职', '2025-06-12 18:04:21', '2025-06-22 02:42:49');
INSERT INTO `doctor` VALUES (4, 'D004', '赵医生', '女', 40, '13800138004', '<EMAIL>', 4, '主任医师', '妇科肿瘤', '123456', '在职', '2025-06-12 18:04:21', '2025-06-12 18:04:21');
INSERT INTO `doctor` VALUES (5, 'D005', '刘医生', '男', 35, '13800138005', '<EMAIL>', 5, '主任医师', '骨折治疗', '123456', '在职', '2025-06-12 18:04:21', '2025-06-22 02:42:54');
INSERT INTO `doctor` VALUES (6, 'D006', '吴医生', '男', 26, '13800138006', '<EMAIL>', 5, '主任医师', '骨科专家', '123456', '在职', '2025-06-14 18:17:19', '2025-06-22 21:18:12');
INSERT INTO `doctor` VALUES (7, 'D007', '关医生', '女', 38, '13800138007', '<EMAIL>', 6, '主任医师', '心外科手术专家', '123456', '在职', '2025-06-22 02:35:20', '2025-06-22 21:18:17');
INSERT INTO `doctor` VALUES (8, 'D008', '谷医生', '男', 36, '13800138008', '<EMAIL>', 6, '副主任医师', '心外科专家', '123456', '在职', '2025-06-22 02:38:42', '2025-06-22 21:18:23');
INSERT INTO `doctor` VALUES (9, 'D009', '袁医生', '男', 41, '13800138009', '<EMAIL>', 7, '主任医师', '胸外科手术专家', '123456', '在职', '2025-06-22 02:41:11', '2025-06-22 21:18:37');
INSERT INTO `doctor` VALUES (10, 'D010', '林医生', '男', 37, '13800138010', '<EMAIL>', 7, '副主任医师', '胸外科专家', '123456', '在职', '2025-06-22 02:44:52', '2025-06-22 21:18:42');
INSERT INTO `doctor` VALUES (11, 'D011', '何医生', '女', 37, '13800138011', '<EMAIL>', 7, '副主任医师', '胸外科专家', '123456', '在职', '2025-06-22 02:47:35', '2025-06-22 21:18:45');
INSERT INTO `doctor` VALUES (12, 'D012', '苏医生', '男', 44, '13800138012', '<EMAIL>', 8, '主任医师', '泌尿外科手术专家', '123456', '在职', '2025-06-22 02:49:31', '2025-06-22 21:18:53');
INSERT INTO `doctor` VALUES (13, 'D013', '叶医生', '女', 41, '13800138013', '<EMAIL>', 8, '副主任医师', '泌尿外科专家', '123456', '在职', '2025-06-22 02:51:17', '2025-06-22 21:18:59');
INSERT INTO `doctor` VALUES (14, 'D014', '艾医生', '男', 49, '13800138014', '<EMAIL>', 9, '主任医师', '神经科专家', '123456', '在职', '2025-06-22 02:54:18', '2025-06-22 21:19:02');
INSERT INTO `doctor` VALUES (15, 'D015', '徐医生', '女', 35, '13800138015', '<EMAIL>', 9, '副主任医师', '神经科专家', '123456', '在职', '2025-06-22 02:55:52', '2025-06-22 21:19:08');
INSERT INTO `doctor` VALUES (16, 'D016', '凌医生', '男', 35, '13800138016', '<EMAIL>', 9, '副主任医师', '神经科专家', '123456', '在职', '2025-06-22 02:57:22', '2025-06-22 21:19:12');
INSERT INTO `doctor` VALUES (17, 'D017', '秦医生', '女', 43, '13800138017', '<EMAIL>', 10, '主任医师', '心内科专家', '123456', '在职', '2025-06-22 02:59:27', '2025-06-22 21:19:16');
INSERT INTO `doctor` VALUES (18, 'D018', '云医生', '女', 39, '13800138018', '<EMAIL>', 10, '副主任医师', '心内科专家', '123456', '在职', '2025-06-22 03:01:09', '2025-06-22 21:19:19');
INSERT INTO `doctor` VALUES (19, 'D019', '韩医生', '男', 50, '13800138019', '<EMAIL>', 11, '主任医师', '消化内科专家', '123456', '在职', '2025-06-22 03:03:21', '2025-06-22 21:19:24');
INSERT INTO `doctor` VALUES (20, 'D020', '梁医生', '男', 42, '13800138020', '<EMAIL>', 11, '副主任医师', '消化内科专家', '123456', '在职', '2025-06-22 03:05:34', '2025-06-22 21:19:30');
INSERT INTO `doctor` VALUES (21, 'D021', '谭医生', '男', 46, '13800138021', '<EMAIL>', 12, '主任医师', '神经内科专家', '123456', '在职', '2025-06-22 03:07:14', '2025-06-22 21:19:37');
INSERT INTO `doctor` VALUES (22, 'D022', '罗医生', '男', 41, '13800138022', '<EMAIL>', 12, '副主任医师', '神经内科专家', '123456', '在职', '2025-06-22 03:09:13', '2025-06-22 21:19:43');
INSERT INTO `doctor` VALUES (23, 'D023', '杨医生', '女', 43, '13800138023', '<EMAIL>', 13, '主任医师', '肾内科专家', '123456', '在职', '2025-06-22 03:11:43', '2025-06-22 21:19:50');
INSERT INTO `doctor` VALUES (24, 'D024', '宋医生', '男', 42, '13800138024', '<EMAIL>', 13, '副主任医师', '肾内科专家', '123456', '在职', '2025-06-22 03:21:47', '2025-06-22 21:20:03');
INSERT INTO `doctor` VALUES (25, 'D025', '胡医生', '女', 39, '13800138025', '<EMAIL>', 14, '主任医师', '内分泌科专家', '123456', '在职', '2025-06-22 21:01:23', '2025-06-22 21:20:07');
INSERT INTO `doctor` VALUES (26, 'D026', '陈医生', '女', 37, '13800138026', '<EMAIL>', 14, '副主任医师', '内分泌科专家', '123456', '在职', '2025-06-22 21:03:20', '2025-06-22 21:20:11');
INSERT INTO `doctor` VALUES (27, 'D027', '石医生', '女', 40, '13800138027', '<EMAIL>', 15, '主任医师', '呼吸内科专家', '123456', '在职', '2025-06-22 21:04:44', '2025-06-22 21:20:21');
INSERT INTO `doctor` VALUES (28, 'D028', '臧医生', '女', 39, '13800138028', '<EMAIL>', 15, '副主任医师', '呼吸内科专家', '123456', '在职', '2025-06-22 21:06:17', '2025-06-22 21:20:25');
INSERT INTO `doctor` VALUES (29, 'D029', '代医生', '男', 44, '13800138029', '<EMAIL>', 16, '主任医师', '血液内科专家', '123456', '在职', '2025-06-22 21:08:12', '2025-06-22 21:20:31');
INSERT INTO `doctor` VALUES (30, 'D030', '向医生', '男', 40, '13800138030', '<EMAIL>', 16, '副主任医师', '血液内科专家', '123456', '在职', '2025-06-22 21:17:48', '2025-06-22 21:20:36');
INSERT INTO `doctor` VALUES (31, 'D031', '余医生', '女', 45, '13800138031', '<EMAIL>', 17, '主任医师', '康复科专家', '123456', '在职', '2025-06-22 21:22:31', '2025-06-22 21:23:15');
INSERT INTO `doctor` VALUES (32, 'D032', '田医生', '男', 43, '13800138032', '<EMAIL>', 18, '主任医师', '中医科专家', '123456', '在职', '2025-06-22 21:24:10', '2025-06-22 21:24:33');
INSERT INTO `doctor` VALUES (33, 'D033', '白医生', '女', 38, '13800138033', '<EMAIL>', 19, '主任医师', '疼痛科专家', '123456', '在职', '2025-06-22 21:26:10', '2025-06-22 21:26:51');
INSERT INTO `doctor` VALUES (34, 'D034', '赵医生', '女', 38, '13800138034', '<EMAIL>', 19, '副主任医师', '疼痛科专家', '123456', '在职', '2025-06-22 21:28:17', '2025-06-22 21:28:40');
INSERT INTO `doctor` VALUES (35, 'D035', '马医生', '男', 44, '13800138035', '<EMAIL>', 20, '主任医师', '耳鼻喉科专家', '123456', '在职', '2025-06-22 21:30:09', '2025-06-22 21:30:40');
INSERT INTO `doctor` VALUES (36, 'D036', '习医生', '男', 42, '13800138036', '<EMAIL>', 20, '副主任医师', '耳鼻喉科专家', '123456', '在职', '2025-06-22 21:32:27', '2025-06-22 21:33:03');
INSERT INTO `doctor` VALUES (37, 'D037', '慕医生', '女', 40, '13800138037', '<EMAIL>', 21, '主任医师', '眼科专家', '123456', '在职', '2025-06-22 21:38:22', '2025-06-22 21:38:45');
INSERT INTO `doctor` VALUES (38, 'D038', '裴医生', '男', 37, '13800138038', '<EMAIL>', 21, '副主任医师', '眼科专家', '123456', '在职', '2025-06-22 21:39:44', '2025-06-22 21:40:11');
INSERT INTO `doctor` VALUES (39, 'D039', '曲医生', '男', 39, '13800138039', '<EMAIL>', 22, '主任医师', '口腔科专家', '123456', '在职', '2025-06-22 21:41:09', '2025-06-22 21:41:34');
INSERT INTO `doctor` VALUES (40, 'D040', '任医生', '女', 40, '13800138040', '<EMAIL>', 23, '主任医师', '皮肤科专家', '123456', '在职', '2025-06-22 21:43:06', '2025-06-22 21:43:58');
INSERT INTO `doctor` VALUES (41, 'D041', '容医生', '女', 37, '13800138041', '<EMAIL>', 23, '副主任医师', '皮肤科专家', '123456', '在职', '2025-06-22 21:44:59', '2025-06-22 21:45:41');
INSERT INTO `doctor` VALUES (42, 'D042', '图医生', '男', 39, '13800138042', '<EMAIL>', 24, '主任医师', '急诊医学科专家', '123456', '在职', '2025-06-22 21:46:43', '2025-06-22 21:47:22');
INSERT INTO `doctor` VALUES (43, 'D043', '唐医生', '男', 38, '13800138043', '<EMAIL>', 24, '副主任医师', '急诊医学科专家', '123456', '在职', '2025-06-22 21:48:13', '2025-06-22 21:48:44');
INSERT INTO `doctor` VALUES (44, 'D044', '莫医生', '女', 40, '13800138044', '<EMAIL>', 25, '主任医师', '放射科专家', '123456', '在职', '2025-06-22 21:49:22', '2025-06-22 21:49:56');
INSERT INTO `doctor` VALUES (45, 'D045', '元医生', '男', 39, '13800138045', '<EMAIL>', 25, '副主任医师', '放射科专家', '123456', '在职', '2025-06-22 21:50:56', '2025-06-22 21:51:06');
INSERT INTO `doctor` VALUES (46, 'D046', '应医生', '男', 39, '13800138046', '<EMAIL>', 25, '副主任医师', '放射科专家', '123456', '在职', '2025-06-22 21:52:13', '2025-06-22 21:52:26');
INSERT INTO `doctor` VALUES (47, 'D047', '欧医生', '男', 48, '13800138047', '<EMAIL>', 26, '主任医师', '超声科专家', '123456', '在职', '2025-06-22 21:53:40', '2025-06-22 21:53:49');
INSERT INTO `doctor` VALUES (48, 'D048', '安医生', '女', 43, '13800138048', '<EMAIL>', 26, '副主任医师', '超声科专家', '123456', '在职', '2025-06-22 21:54:42', '2025-06-22 21:55:11');
INSERT INTO `doctor` VALUES (49, 'D049', '撒医生', '男', 34, '13800138049', '<EMAIL>', 27, '主任医师', '检验科专家', '123456', '在职', '2025-06-22 21:56:25', '2025-06-22 21:56:33');
INSERT INTO `doctor` VALUES (50, 'D050', '孙医生', '女', 33, '13800138050', '<EMAIL>', 27, '副主任医师', '检验科专家', '123456', '在职', '2025-06-22 21:57:37', '2025-06-22 21:57:48');
INSERT INTO `doctor` VALUES (51, 'D051', '段医生', '女', 37, '13800138051', '<EMAIL>', 28, '主任医师', '麻醉科专家', '123456', '在职', '2025-06-22 21:59:27', '2025-06-22 21:59:36');
INSERT INTO `doctor` VALUES (52, 'D052', '丁医生', '男', 36, '13800138052', '<EMAIL>', 28, '副主任医师', '麻醉科专家', '123456', '在职', '2025-06-22 22:00:50', '2025-06-22 22:01:03');
INSERT INTO `doctor` VALUES (53, 'D053', '杜医生', '男', 52, '13800138053', '<EMAIL>', 29, '主任医师', 'ICU抢救专家', '123456', '在职', '2025-06-22 22:02:05', '2025-06-22 22:02:16');
INSERT INTO `doctor` VALUES (54, 'D054', '封医生', '女', 47, '13800138054', '<EMAIL>', 29, '副主任医师', 'ICU抢救专家', '123456', '在职', '2025-06-22 22:03:24', '2025-06-22 22:03:40');
INSERT INTO `doctor` VALUES (55, 'D055', '黄医生', '男', 44, '13800138055', '<EMAIL>', 29, '副主任医师', 'ICU抢救专家', '123456', '在职', '2025-06-22 22:04:45', '2025-06-22 22:04:53');

-- ----------------------------
-- Table structure for medical_record
-- ----------------------------
DROP TABLE IF EXISTS `medical_record`;
CREATE TABLE `medical_record`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `reg_id` int NOT NULL COMMENT '挂号ID',
  `patient_id` int NOT NULL COMMENT '患者ID',
  `doctor_id` int NOT NULL COMMENT '医生ID',
  `visit_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '就诊时间',
  `chief_complaint` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '主诉',
  `present_illness` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '现病史',
  `physical_exam` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '体格检查',
  `diagnosis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '诊断',
  `treatment_plan` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '治疗方案',
  `prescription` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '处方',
  `advice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '医嘱',
  `next_visit_date` date NULL DEFAULT NULL COMMENT '下次复诊日期',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `reg_id`(`reg_id` ASC) USING BTREE,
  INDEX `patient_id`(`patient_id` ASC) USING BTREE,
  INDEX `doctor_id`(`doctor_id` ASC) USING BTREE,
  CONSTRAINT `medical_record_ibfk_1` FOREIGN KEY (`reg_id`) REFERENCES `registration` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `medical_record_ibfk_2` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `medical_record_ibfk_3` FOREIGN KEY (`doctor_id`) REFERENCES `doctor` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '就诊记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of medical_record
-- ----------------------------
INSERT INTO `medical_record` VALUES (1, 2, 1, 2, '2025-06-14 09:00:00', '胃溃疡', '胃溃疡', '不正常', '胃病', '手术', '', '一周内只能吃流食', '2025-06-19', '2025-06-12 19:37:57', '2025-06-26 01:42:28');
INSERT INTO `medical_record` VALUES (2, 3, 2, 1, '2025-06-27 10:00:00', '高血压', '高血压', '不正常', '高血压', '药物', '降压药', '不要动怒', '2025-06-28', '2025-06-14 16:21:26', '2025-06-26 02:01:30');
INSERT INTO `medical_record` VALUES (3, 4, 3, 4, '2025-06-21 15:21:23', '月经不调', '月经不调', '正常', '经期疼痛', '药物', '布洛芬', '不能吃辣，不可以喝奶茶', '2025-06-22', '2025-06-14 19:21:23', '2025-06-26 02:01:36');
INSERT INTO `medical_record` VALUES (4, 6, 4, 5, '2025-06-15 08:33:16', '骨折', '骨折', '正常', '骨折', '手术', '', '不能剧烈运动', '2025-06-29', '2025-06-15 01:33:16', '2025-06-26 01:42:48');
INSERT INTO `medical_record` VALUES (5, 7, 5, 6, '2025-06-22 11:15:02', '骨裂', '骨裂', '正常', '骨裂', '手术', NULL, '不能剧烈运动', '2025-06-29', '2025-06-26 00:15:02', '2025-06-26 02:01:53');
INSERT INTO `medical_record` VALUES (6, 8, 6, 7, '2025-06-23 14:15:48', '先天性心脏病', '先天性心脏病', '不正常', '先天性心脏病', '手术', NULL, '留院观察', '2025-06-30', '2025-06-26 00:15:48', '2025-06-26 02:02:14');
INSERT INTO `medical_record` VALUES (7, 9, 7, 8, '2025-06-24 09:30:32', '心脏瓣膜病', '心脏瓣膜病', '不正常', '心脏瓣膜病', '手术', NULL, '留院观察', '2025-06-26', '2025-06-26 00:16:32', '2025-06-26 02:02:24');
INSERT INTO `medical_record` VALUES (8, 10, 8, 9, '2025-06-23 08:17:37', '胸部肿瘤', '胸部肿瘤', '不正常', '胸部肿瘤', '手术', NULL, '留院观察', '2025-06-29', '2025-06-26 00:17:37', '2025-06-26 02:02:32');
INSERT INTO `medical_record` VALUES (9, 11, 9, 10, '2025-06-24 10:20:14', '胸部外伤', '胸部外伤', '不正常', '胸部外伤', '手术', NULL, '留院观察', '2025-06-28', '2025-06-26 00:20:14', '2025-06-26 02:02:39');
INSERT INTO `medical_record` VALUES (10, 12, 10, 12, '2025-06-25 16:22:04', '膀胱结石', '膀胱结石', '不正常', '膀胱结石', '手术', NULL, '留院观察', '2025-06-30', '2025-06-26 00:22:04', '2025-06-26 02:02:46');
INSERT INTO `medical_record` VALUES (11, 13, 11, 14, '2025-06-22 11:22:58', '颅脑损伤', '颅脑损伤', '不正常', '颅脑损伤', '手术', NULL, '留院观察', '2025-06-30', '2025-06-26 00:22:58', '2025-06-26 02:02:54');
INSERT INTO `medical_record` VALUES (12, 14, 12, 15, '2025-06-17 15:23:51', '脑肿瘤', '脑肿瘤', '不正常', '脑肿瘤', '手术', NULL, '留院观察', '2025-06-26', '2025-06-26 00:23:51', '2025-06-26 02:03:01');
INSERT INTO `medical_record` VALUES (13, 15, 13, 17, '2025-06-20 09:24:12', '冠心病', '冠心病', '不正常', '冠心病', '手术', NULL, '留院观察', '2025-06-26', '2025-06-26 00:24:12', '2025-06-26 02:03:08');
INSERT INTO `medical_record` VALUES (14, 16, 14, 18, '2025-06-22 10:24:35', '心律失常', '心律失常', '正常', '心律失常', '药物', 'β受体阻滞剂', '不要焦虑', '2025-06-25', '2025-06-26 00:24:35', '2025-06-26 02:03:16');
INSERT INTO `medical_record` VALUES (15, 18, 15, 19, '2025-06-24 09:25:43', '胃炎', '胃炎', '不正常', '炎症', '药物', '奥美拉唑肠溶胶囊', '清淡饮食', '2025-06-26', '2025-06-26 00:25:43', '2025-06-26 02:03:23');
INSERT INTO `medical_record` VALUES (16, 19, 16, 20, '2025-06-21 10:26:47', '阑尾炎', '阑尾炎', '正常', '炎症', '手术', NULL, '留院观察', '2025-06-27', '2025-06-26 00:26:47', '2025-06-26 02:03:31');
INSERT INTO `medical_record` VALUES (17, 20, 17, 21, '2025-06-26 16:27:33', '偏头痛', '偏头痛', '正常', '偏头痛', '药物', '布洛芬', '少熬夜', '2025-06-29', '2025-06-26 00:27:33', '2025-06-26 02:03:40');
INSERT INTO `medical_record` VALUES (18, 21, 18, 23, '2025-06-24 14:28:08', '糖尿病', '糖尿病', '不正常', '糖尿病', '手术、药物', '糖苷酶抑制剂', '留院观察', '2025-06-28', '2025-06-26 00:28:08', '2025-06-26 02:03:48');
INSERT INTO `medical_record` VALUES (19, 22, 19, 24, '2025-06-25 11:28:24', '肾结石', '肾结石', '不正常', '肾结石', '手术', NULL, '留院观察', '2025-06-30', '2025-06-26 00:28:24', '2025-06-26 02:03:56');
INSERT INTO `medical_record` VALUES (20, 23, 20, 25, '2025-06-26 08:28:41', '痛风', '痛风', '不正常', '痛风', '药物', '非布司他', '少吃海鲜', '2025-06-29', '2025-06-26 00:28:41', '2025-06-26 02:04:15');
INSERT INTO `medical_record` VALUES (21, 24, 21, 26, '2025-06-27 14:30:05', '骨质疏松', '骨质疏松', '正常', '骨质疏松', '药物', '钙片', '补钙', '2025-06-30', '2025-06-26 00:29:05', '2025-06-26 02:04:32');
INSERT INTO `medical_record` VALUES (22, 25, 22, 27, '2025-06-25 08:29:24', '支气管哮喘', '支气管哮喘', '不正常', '支气管哮喘', '药物', '地塞米松', NULL, '2025-06-26', '2025-06-26 00:29:24', '2025-06-26 02:04:45');
INSERT INTO `medical_record` VALUES (23, 26, 23, 28, '2025-06-26 10:29:43', '肺炎', '肺炎', '不正常', '炎症', '手术', NULL, '留院观察', '2025-06-27', '2025-06-26 00:29:43', '2025-06-26 02:04:53');
INSERT INTO `medical_record` VALUES (24, 27, 24, 29, '2025-06-28 00:00:00', '贫血', '贫血', '不正常', '贫血', '药物', '叶酸', '多吃含铁食物', '2025-06-29', '2025-06-26 00:30:08', '2025-06-26 02:05:00');
INSERT INTO `medical_record` VALUES (25, 28, 25, 31, '2025-06-27 16:30:41', '骨折', '骨折', '正常', '骨折', '手术', NULL, '留院观察', '2025-06-28', '2025-06-26 00:30:41', '2025-06-26 02:05:04');
INSERT INTO `medical_record` VALUES (26, 29, 26, 32, '2025-06-26 14:31:39', '失眠', '失眠', '正常', '失眠', '药物', '脑心舒', '别想太多', '2025-06-27', '2025-06-26 00:31:39', '2025-06-26 02:05:23');
INSERT INTO `medical_record` VALUES (27, 30, 27, 33, '2025-06-22 16:32:10', '肩周炎', '肩周炎', '正常', '炎症', '手术', NULL, '少背包', '2025-06-26', '2025-06-26 00:32:10', '2025-06-26 02:05:32');
INSERT INTO `medical_record` VALUES (28, 31, 28, 34, '2025-06-24 17:32:28', '颈椎病', '颈椎病', '正常', '颈椎病', '手术', NULL, '坐姿睡姿矫正', '2025-06-26', '2025-06-26 00:32:28', '2025-06-26 02:05:38');
INSERT INTO `medical_record` VALUES (29, 32, 29, 35, '2025-06-23 09:32:51', '中耳炎', '中耳炎', '不正常', '炎症', '药物', '消炎药', NULL, '2025-06-25', '2025-06-26 00:32:51', '2025-06-26 02:05:46');
INSERT INTO `medical_record` VALUES (30, 33, 30, 36, '2025-06-24 11:33:09', '鼻炎', '鼻炎', '正常', '炎症', '药物', '鼻炎胶囊', NULL, '2025-06-27', '2025-06-26 00:33:09', '2025-06-26 02:05:53');
INSERT INTO `medical_record` VALUES (31, 34, 31, 37, '2025-06-24 14:33:27', '结膜炎', '结膜炎', '正常', '炎症', '手术', NULL, '留院观察', '2025-06-27', '2025-06-26 00:33:27', '2025-06-26 02:06:00');
INSERT INTO `medical_record` VALUES (32, 35, 32, 38, '2025-06-26 09:34:17', '白内障', '白内障', '不正常', '白内障', '手术', NULL, '留院观察', '2025-06-27', '2025-06-26 00:34:17', '2025-06-26 02:06:11');
INSERT INTO `medical_record` VALUES (33, 36, 33, 39, '2025-06-25 10:34:37', '牙周炎', '牙周炎', '不正常', '炎症', '药物', '消炎药', NULL, '2025-06-28', '2025-06-26 00:34:37', '2025-06-26 02:06:20');
INSERT INTO `medical_record` VALUES (34, 37, 34, 40, '2025-06-27 08:34:59', '红斑', '红斑', '不正常', '红斑', '手术', NULL, '留院观察', '2025-06-28', '2025-06-26 00:34:59', '2025-06-26 02:06:29');
INSERT INTO `medical_record` VALUES (35, 38, 35, 41, '2025-06-26 09:35:20', '湿疹', '湿疹', '正常', '湿疹', '药物', '炉甘石洗剂', NULL, '2025-06-30', '2025-06-26 00:35:20', '2025-06-26 02:06:46');
INSERT INTO `medical_record` VALUES (36, 39, 36, 42, '2025-06-28 00:00:00', '感冒', '感冒', '正常', '感冒', '药物', '999感冒颗粒', NULL, '2025-06-29', '2025-06-26 00:39:18', '2025-06-26 02:07:01');
INSERT INTO `medical_record` VALUES (37, 40, 37, 43, '2025-06-29 00:00:00', '食物过敏', '食物过敏', '不正常', '芒果过敏', '药物', '氯雷他定', NULL, '2025-06-30', '2025-06-26 00:39:37', '2025-06-26 02:07:08');
INSERT INTO `medical_record` VALUES (38, 41, 38, 44, '2025-06-26 16:40:00', '气胸', '气胸', '不正常', '气胸', '手术', NULL, '留院观察', '2025-06-27', '2025-06-26 00:40:00', '2025-06-26 02:07:15');
INSERT INTO `medical_record` VALUES (39, 42, 39, 45, '2025-06-30 00:00:00', '肺炎', '肺炎', '不正常', '炎症', '手术', NULL, '留院观察', '2025-07-01', '2025-06-26 00:40:19', '2025-06-26 02:07:24');
INSERT INTO `medical_record` VALUES (40, 43, 40, 3, '2025-06-27 15:40:40', '水痘', '水痘', '正常', '水痘', '药物', '阿昔洛韦乳膏', NULL, '2025-06-28', '2025-06-26 00:40:40', '2025-06-26 02:07:35');
INSERT INTO `medical_record` VALUES (41, 44, 41, 3, '2025-06-28 00:00:00', '腹泻', '腹泻', '正常', '腹泻', '药物', '藿香正气水', '多喝淡盐水', '2025-06-29', '2025-06-26 00:41:00', '2025-06-26 02:07:47');
INSERT INTO `medical_record` VALUES (42, 45, 42, 46, '2025-06-29 00:00:00', '胃溃疡', '胃溃疡', '不正常', '胃病', '手术', NULL, '留院观察', '2025-06-30', '2025-06-26 00:41:25', '2025-06-26 02:07:52');
INSERT INTO `medical_record` VALUES (43, 46, 43, 47, '2025-06-25 15:42:57', '急性胆囊炎', '急性胆囊炎', '不正常', '炎症', '手术', NULL, '留院观察', '2025-06-26', '2025-06-26 00:42:57', '2025-06-26 02:08:04');
INSERT INTO `medical_record` VALUES (44, 47, 44, 48, '2025-06-28 00:00:00', '前列腺增生', '前列腺增生', '不正常', '前列腺增生', '手术', NULL, '留院观察', '2025-06-29', '2025-06-26 00:43:39', '2025-06-26 02:08:09');
INSERT INTO `medical_record` VALUES (45, 48, 45, 49, '2025-06-26 10:44:00', '白血病', '白血病', '不正常', '白血病', '手术', NULL, '留院观察', '2025-06-27', '2025-06-26 00:44:00', '2025-06-26 02:08:14');
INSERT INTO `medical_record` VALUES (46, 49, 46, 50, '2025-06-26 15:44:21', '过敏', '过敏', '不正常', '花粉过敏', '药物', '氯雷他定', NULL, '2025-06-27', '2025-06-26 00:44:21', '2025-06-26 02:08:25');
INSERT INTO `medical_record` VALUES (47, 50, 47, 51, '2025-06-25 08:44:41', '癌痛', '癌痛', '不正常', '癌症', '手术', NULL, '留院观察', '2025-06-26', '2025-06-26 00:44:41', '2025-06-26 02:08:29');
INSERT INTO `medical_record` VALUES (48, 51, 48, 52, '2025-06-27 10:00:57', '胃痛', '胃痛', '不正常', '胃病', '手术', '无痛胃肠镜', '留院观察', '2025-06-28', '2025-06-26 00:44:57', '2025-06-26 02:08:35');
INSERT INTO `medical_record` VALUES (49, 52, 49, 53, '2025-06-30 00:00:00', '脑死亡', '脑死亡', '不正常', '脑死亡', '手术', NULL, '留院观察', '2025-07-01', '2025-06-26 00:45:12', '2025-06-26 02:08:39');
INSERT INTO `medical_record` VALUES (50, 53, 50, 54, '2025-06-28 00:00:00', '呼吸衰竭', '呼吸衰竭', '不正常', '呼吸衰竭', '手术', NULL, '留院观察', '2025-06-29', '2025-06-26 00:46:24', '2025-06-26 02:08:49');

-- ----------------------------
-- Table structure for patient
-- ----------------------------
DROP TABLE IF EXISTS `patient`;
CREATE TABLE `patient`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `patient_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '患者编号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '患者姓名',
  `gender` enum('男','女') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '性别',
  `age` int NULL DEFAULT NULL COMMENT '年龄',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号',
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '家庭住址',
  `emergency_contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '紧急联系电话',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录密码',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `patient_no`(`patient_no` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '患者信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of patient
-- ----------------------------
INSERT INTO `patient` VALUES (1, 'P001', '张三', '男', 30, '13900139001', '110101199001011234', '北京市朝阳区', '李四', '13900139002', '123456', '2025-06-12 18:04:21', '2025-06-14 03:23:27');
INSERT INTO `patient` VALUES (2, 'P002', '李梅', '女', 25, '13900139003', '110101199501011234', '北京市海淀区', '王五', '13900139004', '123456', '2025-06-12 18:04:21', '2025-06-12 18:04:21');
INSERT INTO `patient` VALUES (3, 'P003', '张三丰', '女', 18, '13511112221', '445381200304545322', '广州市天河区', '刘德华', '17765664561', '123456', '2025-06-14 18:21:44', '2025-06-23 00:55:27');
INSERT INTO `patient` VALUES (4, 'P004', '周杰伦', '男', 23, '17767665464', '445381003873766789', '广州天河龙洞', '李勋', '17767665454', '123456', '2025-06-14 19:19:07', '2025-06-22 22:14:11');
INSERT INTO `patient` VALUES (5, 'P005', '氯化钡', '男', 20, '16607663242', '445381003873727789', '广州天河龙洞', '魏晨', '19988776766', '123456', '2025-06-15 01:32:17', '2025-06-22 22:14:30');
INSERT INTO `patient` VALUES (6, 'P006', '梁皓波', '男', 23, '13713643578', '440782366326575854', '肇庆市端州区', '梁韵', '13543754487', '123456', '2025-06-22 22:16:31', '2025-06-22 22:16:31');
INSERT INTO `patient` VALUES (7, 'P007', '林俊杰', '男', 45, '17543732993', '229856721264567308', '上海市陆家嘴', '周杰伦', '17767665464', '123456', '2025-06-22 22:18:11', '2025-06-22 23:02:30');
INSERT INTO `patient` VALUES (8, 'P008', '凌云', '女', 25, '12366577643', '446799087555658699', '茂名市高州市', '黄晓', '13464565773', '123456', '2025-06-22 22:26:53', '2025-06-22 22:26:53');
INSERT INTO `patient` VALUES (9, 'P009', '贝多芬', '男', 56, '17632435678', '440153467623673897', '广州市白云区', '牛顿', '13475646984', '123456', '2025-06-22 22:28:53', '2025-06-22 22:28:53');
INSERT INTO `patient` VALUES (10, 'P010', '谢娜', '女', 44, '13424573235', '440262678264781156', '广州市海珠区', '张杰', '13623436778', '123456', '2025-06-22 22:42:48', '2025-06-22 22:42:48');
INSERT INTO `patient` VALUES (11, 'P011', '张若昀', '男', 42, '13729842164', '110345865151578992', '北京市朝阳区', '唐艺昕', '15647966621', '123456', '2025-06-22 22:45:23', '2025-06-22 23:02:41');
INSERT INTO `patient` VALUES (12, 'P012', '周晓枫', '男', 32, '13643456566', '440798668524312324', '湛江市吴川市', '田西西', '15634826582', '123456', '2025-06-22 22:47:36', '2025-06-22 22:47:36');
INSERT INTO `patient` VALUES (13, 'P013', '李倩倩', '男', 34, '17825646235', '44015274518654375X', '茂名市化州市', '林奇', '15378465772', '123456', '2025-06-22 22:49:31', '2025-06-22 22:49:31');
INSERT INTO `patient` VALUES (14, 'P014', '武艺', '男', 38, '13568378492', '440778253361725451', '广州市从化区', '沈月', '13646856822', '123456', '2025-06-22 22:50:56', '2025-06-22 22:50:56');
INSERT INTO `patient` VALUES (15, 'P015', '任国超', '男', 30, '16234186653', '440125326811445623', '广州市白云区', '白沫元', '12468561551', '123456', '2025-06-22 22:53:11', '2025-06-22 22:53:11');
INSERT INTO `patient` VALUES (16, 'P016', '梁玉', '女', 21, '13734642786', '440782165323581113', '广州市荔湾区', '时仪', '18312368265', '123456', '2025-06-22 22:55:19', '2025-06-22 22:55:19');
INSERT INTO `patient` VALUES (17, 'P017', '李蓉', '女', 27, '13456824544', '440512364133361537', '广州市海珠区', '裴文宣', '13457364345', '123456', '2025-06-22 22:56:52', '2025-06-22 22:56:52');
INSERT INTO `patient` VALUES (18, 'P018', '沈惜凡', '女', 28, '13643286455', '440582645522357658', '广州市天河区', '何苏叶', '13245365856', '123456', '2025-06-22 23:00:08', '2025-06-22 23:00:08');
INSERT INTO `patient` VALUES (19, 'P019', '净渊', '男', 37, '13584584343', '440136294334523433', '广州市花都区', '星月', '13648351513', '123456', '2025-06-22 23:02:25', '2025-06-22 23:02:25');
INSERT INTO `patient` VALUES (20, 'P020', '周生辰', '男', 25, '15423645682', '440125834664154647', '广州市天河区', '时宜', '17632146654', '123456', '2025-06-22 23:04:28', '2025-06-22 23:04:28');
INSERT INTO `patient` VALUES (21, 'P021', '颜末', '女', 23, '12375424654', '440513526341474854', '广州市白云区', '陆之昂', '16482545327', '123456', '2025-06-22 23:05:45', '2025-06-22 23:05:45');
INSERT INTO `patient` VALUES (22, 'P022', '姜雪宁', '女', 21, '14534357253', '440142732642541545', '广州市白云区', '谢危', '13574354353', '123456', '2025-06-22 23:07:38', '2025-06-22 23:07:38');
INSERT INTO `patient` VALUES (23, 'P023', '任如意', '女', 26, '14655245235', '44013745383644254X', '广州市花都区', '宁远舟', '13652548375', '123456', '2025-06-22 23:09:20', '2025-06-22 23:09:20');
INSERT INTO `patient` VALUES (24, 'P024', '孟子义', '女', 29, '14632656428', '440152263221523445', '广州市黄埔区', '李昀锐', '16438245455', '123456', '2025-06-22 23:10:56', '2025-06-22 23:10:56');
INSERT INTO `patient` VALUES (25, 'P025', '魏若来', '男', 22, '13854284648', '440792653275234333', '广州市天河区', '沈近真', '15342542834', '123456', '2025-06-22 23:12:31', '2025-06-22 23:15:53');
INSERT INTO `patient` VALUES (26, 'P026', '苏辞书', '女', 45, '13573484364', '44025116834276364X', '广州市番禺区', '沈图南', '16546382454', '123456', '2025-06-22 23:14:05', '2025-06-22 23:14:05');
INSERT INTO `patient` VALUES (27, 'P027', '赵远舟', '男', 33, '13413536333', '440851266434534851', '广州市天河区', '文潇', '15634532362', '123456', '2025-06-22 23:15:45', '2025-06-22 23:15:45');
INSERT INTO `patient` VALUES (28, 'P028', '江成屹', '男', 36, '14635634336', '440512321443863685', '广州市番禺区', '陆嫣', '13632542642', '123456', '2025-06-22 23:18:01', '2025-06-22 23:18:01');
INSERT INTO `patient` VALUES (29, 'P029', '茯苓', '女', 24, '13648845376', '440218622348775336', '广州市天河区', '重昭', '12354436554', '123456', '2025-06-22 23:20:15', '2025-06-22 23:20:15');
INSERT INTO `patient` VALUES (30, 'P030', '李彧', '男', 67, '14545788954', '440124647657458734', '广州市从化区', '梁雪峰', '14365845682', '123456', '2025-06-22 23:22:12', '2025-06-22 23:22:12');
INSERT INTO `patient` VALUES (31, 'P031', '宋冉', '女', 31, '16438262467', '440678712621245326', '深圳市龙岗区', '李瓒', '14382688555', '123456', '2025-06-22 23:24:34', '2025-06-22 23:24:34');
INSERT INTO `patient` VALUES (32, 'P032', '沐轻歌', '女', 35, '13665442386', '449362351464371677', '惠州市惠东区', '苏易水', '13673599434', '123456', '2025-06-22 23:26:43', '2025-06-22 23:26:43');
INSERT INTO `patient` VALUES (33, 'P033', '苏言成', '男', 26, '14365459485', '440219736345621783', '深圳市龙华区', '白筱朵', '13254234684', '123456', '2025-06-22 23:29:53', '2025-06-22 23:29:53');
INSERT INTO `patient` VALUES (34, 'P034', '魏邵', '男', 33, '13628462834', '440123823324527676', '深圳市福田区', '小乔', '13571274444', '123456', '2025-06-22 23:32:09', '2025-06-22 23:32:09');
INSERT INTO `patient` VALUES (35, 'P035', '张陆让', '男', 23, '13264535736', '440235248334735681', '深圳市南山区', '苏在在', '16325425748', '123456', '2025-06-22 23:34:15', '2025-06-22 23:34:15');
INSERT INTO `patient` VALUES (36, 'P036', '唐嫣', '女', 36, '16845635485', '440219274362647255', '深圳市龙华区', '罗晋', '14834235347', '123456', '2025-06-22 23:35:51', '2025-06-22 23:35:51');
INSERT INTO `patient` VALUES (37, 'P037', '邓青云', '男', 47, '12184648653', '440987916321542167', '深圳市宝安区', '黎曲锦', '13246384363', '123456', '2025-06-22 23:39:09', '2025-06-22 23:39:09');
INSERT INTO `patient` VALUES (38, 'P038', '王雨桐', '女', 21, '13368365832', '440126823274638734', '深圳市龙岗区', '秦宇', '12483648423', '123456', '2025-06-22 23:40:53', '2025-06-22 23:40:53');
INSERT INTO `patient` VALUES (39, 'P039', '唐璐璐', '女', 37, '13463838264', '440912762483345286', '深圳市宝安区', '安云', '13643825645', '123456', '2025-06-22 23:43:46', '2025-06-22 23:43:46');
INSERT INTO `patient` VALUES (40, 'P040', '张之锦', '女', 4, NULL, '440137264328468234', '深圳市福田区', '张余玄', '13624384364', '123456', '2025-06-22 23:47:13', '2025-06-22 23:47:13');
INSERT INTO `patient` VALUES (41, 'P041', '江翻翻', '男', 2, NULL, '440136234573454136', '深圳市南山区', '林霖', '16438584456', '123456', '2025-06-22 23:49:36', '2025-06-22 23:49:36');
INSERT INTO `patient` VALUES (42, 'P042', '裴旻希', '女', 15, '13465478878', '440257363845355666', '深圳市龙岗区', '裴颂', '13463584546', '123456', '2025-06-22 23:55:14', '2025-06-22 23:55:14');
INSERT INTO `patient` VALUES (43, 'P043', '阮航宇', '男', 29, '13658275275', '440163872174364864', '深圳市龙华区', '李青月', '16284378473', '123456', '2025-06-23 00:11:30', '2025-06-23 00:11:30');
INSERT INTO `patient` VALUES (44, 'P044', '周星城', '男', 23, '13863684367', '440326435474936886', '惠州市惠东区', '苏玉婷', '16358579456', '123456', '2025-06-23 00:14:42', '2025-06-23 00:14:42');
INSERT INTO `patient` VALUES (45, 'P045', '张凌赫', '男', 28, '13643634847', '440793686435835232', '惠州市西湖区', '周柯宇', '13583632636', '123456', '2025-06-23 00:17:12', '2025-06-23 00:17:12');
INSERT INTO `patient` VALUES (46, 'P046', '宁溪', '女', 31, '15346364846', '440638634865588244', '惠州市西湖区', '陆枭', '17836376722', '123456', '2025-06-23 00:20:23', '2025-06-23 00:20:23');
INSERT INTO `patient` VALUES (47, 'P047', '向晚晚', '女', 22, '13656438493', '440687435263256472', '广州市黄埔区', '黄锡昱', '16343365876', '123456', '2025-06-23 00:22:26', '2025-06-23 00:22:26');
INSERT INTO `patient` VALUES (48, 'P048', '刘卫国', '男', 78, '14583638354', '440725146853864437', '广州市花都区', '刘泰光', '15846382565', '123456', '2025-06-23 00:24:11', '2025-06-23 00:24:11');
INSERT INTO `patient` VALUES (49, 'P049', '罗南因', '男', 46, '16435993743', '440121463385233563', '广州市从化区', '柯心怡', '13734368633', '123456', '2025-06-23 00:34:33', '2025-06-23 00:34:33');
INSERT INTO `patient` VALUES (50, 'P050', '华泽然', '女', 38, '14635578464', '440157274825625767', '广州市白云区', '欧阳书阁', '16885463468', '123456', '2025-06-23 00:38:11', '2025-06-23 00:38:11');

-- ----------------------------
-- Table structure for registration
-- ----------------------------
DROP TABLE IF EXISTS `registration`;
CREATE TABLE `registration`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `reg_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '挂号单号',
  `patient_id` int NOT NULL COMMENT '患者ID',
  `doctor_id` int NOT NULL COMMENT '医生ID',
  `dept_id` int NOT NULL COMMENT '科室ID',
  `reg_date` date NOT NULL COMMENT '挂号日期',
  `reg_time` time NOT NULL COMMENT '挂号时间段',
  `reg_fee` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '挂号费',
  `status` enum('已挂号','已就诊','已取消') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '已挂号' COMMENT '状态',
  `symptoms` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '主要症状',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `reg_no`(`reg_no` ASC) USING BTREE,
  INDEX `patient_id`(`patient_id` ASC) USING BTREE,
  INDEX `doctor_id`(`doctor_id` ASC) USING BTREE,
  INDEX `dept_id`(`dept_id` ASC) USING BTREE,
  CONSTRAINT `registration_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patient` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `registration_ibfk_2` FOREIGN KEY (`doctor_id`) REFERENCES `doctor` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `registration_ibfk_3` FOREIGN KEY (`dept_id`) REFERENCES `department` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 55 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '挂号信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of registration
-- ----------------------------
INSERT INTO `registration` VALUES (1, 'R20250612192931207', 1, 1, 1, '2025-06-13', '09:00:00', 10.00, '已取消', '胃溃疡', '2025-06-12 19:29:31', '2025-06-23 01:07:49');
INSERT INTO `registration` VALUES (2, 'R20250614160404555', 1, 2, 2, '2025-06-14', '09:00:00', 10.00, '已就诊', '胃溃疡', '2025-06-14 16:04:04', '2025-06-23 00:51:14');
INSERT INTO `registration` VALUES (3, 'R20250614173842114', 2, 1, 1, '2025-06-27', '09:00:00', 10.00, '已就诊', '高血压', '2025-06-14 17:38:43', '2025-06-26 02:09:09');
INSERT INTO `registration` VALUES (4, 'R20250614173904074', 3, 4, 4, '2025-06-21', '15:00:00', 10.00, '已就诊', '月经不调', '2025-06-14 17:39:04', '2025-06-23 00:55:33');
INSERT INTO `registration` VALUES (5, 'R20250614191944889', 4, 1, 1, '2025-06-14', '10:00:00', 10.00, '已取消', '骨折', '2025-06-14 19:19:44', '2025-06-23 01:07:55');
INSERT INTO `registration` VALUES (6, 'R20250614235814820', 4, 5, 5, '2025-06-15', '08:00:00', 10.00, '已就诊', '骨折', '2025-06-14 23:58:14', '2025-06-26 02:09:14');
INSERT INTO `registration` VALUES (7, 'R20250615013238662', 5, 6, 5, '2025-06-22', '11:00:00', 10.00, '已就诊', '骨裂', '2025-06-15 01:32:38', '2025-06-23 00:57:20');
INSERT INTO `registration` VALUES (8, 'R20250623123261523', 6, 7, 6, '2025-06-23', '14:00:00', 10.00, '已就诊', '先天性心脏病', '2025-06-23 01:00:20', '2025-06-26 02:09:16');
INSERT INTO `registration` VALUES (9, 'R20250623131642722', 7, 8, 6, '2025-06-24', '09:30:00', 10.00, '已就诊', '心脏瓣膜病', '2025-06-23 01:01:52', '2025-06-26 02:09:21');
INSERT INTO `registration` VALUES (10, 'R20250623274133424', 8, 9, 7, '2025-06-23', '08:00:00', 10.00, '已就诊', '胸部肿瘤', '2025-06-23 01:05:00', '2025-06-23 01:05:00');
INSERT INTO `registration` VALUES (11, 'R20250623268245456', 9, 10, 7, '2025-06-24', '10:00:00', 10.00, '已就诊', '胸部外伤', '2025-06-23 01:06:03', '2025-06-26 02:09:27');
INSERT INTO `registration` VALUES (12, 'R20250623568598465', 10, 12, 8, '2025-06-25', '16:00:00', 10.00, '已就诊', '膀胱结石', '2025-06-23 01:07:36', '2025-06-26 02:09:31');
INSERT INTO `registration` VALUES (13, 'R20250623437639753', 11, 14, 9, '2025-06-22', '11:00:00', 10.00, '已就诊', '颅脑损伤', '2025-06-23 01:12:10', '2025-06-23 01:35:01');
INSERT INTO `registration` VALUES (14, 'R20250623209300348', 12, 15, 9, '2025-06-17', '15:00:00', 10.00, '已就诊', '脑肿瘤', '2025-06-23 01:13:59', '2025-06-23 01:35:18');
INSERT INTO `registration` VALUES (15, 'R20250623273959300', 13, 17, 10, '2025-06-20', '09:00:00', 10.00, '已就诊', '冠心病', '2025-06-23 01:16:34', '2025-06-26 02:09:39');
INSERT INTO `registration` VALUES (16, 'R20250623234755469', 14, 18, 10, '2025-06-22', '10:00:00', 10.00, '已就诊', '心律失常', '2025-06-23 01:19:38', '2025-06-26 02:09:43');
INSERT INTO `registration` VALUES (17, 'R20250623497638790', 15, 10, 7, '2025-06-24', '09:00:00', 10.00, '已取消', '胃炎', '2025-06-23 01:23:04', '2025-06-23 01:23:04');
INSERT INTO `registration` VALUES (18, 'R20250623497638791', 15, 19, 11, '2025-06-24', '09:00:00', 10.00, '已就诊', '胃炎', '2025-06-23 01:25:09', '2025-06-26 02:09:46');
INSERT INTO `registration` VALUES (19, 'R20250623216753459', 16, 20, 11, '2025-06-21', '10:00:00', 10.00, '已就诊', '阑尾炎', '2025-06-23 01:26:47', '2025-06-26 02:09:49');
INSERT INTO `registration` VALUES (20, 'R20250623180465973', 17, 21, 12, '2025-06-26', '16:00:00', 10.00, '已就诊', '偏头痛', '2025-06-23 01:34:06', '2025-06-26 02:09:52');
INSERT INTO `registration` VALUES (21, 'R20250623466434566', 18, 23, 13, '2025-06-24', '14:00:00', 10.00, '已就诊', '糖尿病', '2025-06-24 00:24:10', '2025-06-26 02:09:55');
INSERT INTO `registration` VALUES (22, 'R20250624135852898', 19, 24, 13, '2025-06-25', '11:00:00', 10.00, '已就诊', '肾结石', '2025-06-24 00:27:35', '2025-06-26 02:09:57');
INSERT INTO `registration` VALUES (23, 'R20250623439650873', 20, 25, 14, '2025-06-26', '08:00:00', 10.00, '已就诊', '痛风', '2025-06-24 00:29:46', '2025-06-26 02:10:00');
INSERT INTO `registration` VALUES (24, 'R20250623127453786', 21, 26, 14, '2025-06-27', '14:30:00', 10.00, '已就诊', '骨质疏松', '2025-06-24 00:31:12', '2025-06-26 02:10:04');
INSERT INTO `registration` VALUES (25, 'R20250623317854078', 22, 27, 15, '2025-06-25', '08:00:00', 10.00, '已就诊', '支气管哮喘', '2025-06-24 00:32:55', '2025-06-26 02:10:06');
INSERT INTO `registration` VALUES (26, 'R20250623297439062', 23, 28, 15, '2025-06-26', '10:00:00', 10.00, '已就诊', '肺炎', '2025-06-24 00:33:51', '2025-06-26 02:10:14');
INSERT INTO `registration` VALUES (27, 'R20250623274920717', 24, 29, 16, '2025-06-28', '08:30:00', 10.00, '已就诊', '贫血', '2025-06-24 00:37:39', '2025-06-26 02:10:15');
INSERT INTO `registration` VALUES (28, 'R20250623149824049', 25, 31, 17, '2025-06-27', '16:00:00', 10.00, '已就诊', '骨折', '2025-06-24 00:59:05', '2025-06-26 02:10:23');
INSERT INTO `registration` VALUES (29, 'R20250623238773733', 26, 32, 18, '2025-06-26', '14:00:00', 10.00, '已就诊', '失眠', '2025-06-24 01:01:50', '2025-06-26 02:10:26');
INSERT INTO `registration` VALUES (30, 'R20250623218364892', 27, 33, 19, '2025-06-22', '16:00:00', 10.00, '已就诊', '肩周炎', '2025-06-24 02:02:26', '2025-06-26 02:10:29');
INSERT INTO `registration` VALUES (31, 'R20250623446341840', 28, 34, 19, '2025-06-24', '17:00:00', 10.00, '已就诊', '颈椎病', '2025-06-24 19:28:45', '2025-06-26 02:10:32');
INSERT INTO `registration` VALUES (32, 'R20250624147736974', 29, 35, 20, '2025-06-23', '09:30:00', 10.00, '已就诊', '中耳炎', '2025-06-24 20:59:14', '2025-06-26 02:10:35');
INSERT INTO `registration` VALUES (33, 'R20250624329731994', 30, 36, 20, '2025-06-24', '11:00:00', 10.00, '已就诊', '鼻炎', '2025-06-24 21:01:02', '2025-06-26 02:10:38');
INSERT INTO `registration` VALUES (34, 'R20250624345678643', 31, 37, 21, '2025-06-24', '14:00:00', 10.00, '已就诊', '结膜炎', '2025-06-24 21:05:38', '2025-06-26 02:10:41');
INSERT INTO `registration` VALUES (35, 'R20250625436582465', 32, 38, 21, '2025-06-26', '09:00:00', 10.00, '已就诊', '白内障', '2025-06-25 23:32:05', '2025-06-26 02:10:44');
INSERT INTO `registration` VALUES (36, 'R20250625321467191', 33, 39, 22, '2025-06-25', '10:00:00', 10.00, '已就诊', '牙周炎', '2025-06-25 23:33:55', '2025-06-26 02:10:47');
INSERT INTO `registration` VALUES (37, 'R20250625635677854', 34, 40, 23, '2025-06-27', '08:00:00', 10.00, '已就诊', '红斑', '2025-06-25 23:35:30', '2025-06-26 02:10:50');
INSERT INTO `registration` VALUES (38, 'R20250625234375644', 35, 41, 23, '2025-06-26', '09:30:00', 10.00, '已就诊', '湿疹', '2025-06-25 23:37:31', '2025-06-26 02:10:53');
INSERT INTO `registration` VALUES (39, 'R20250625135464867', 36, 42, 24, '2025-06-28', '11:00:00', 10.00, '已就诊', '感冒', '2025-06-25 23:38:23', '2025-06-26 02:10:56');
INSERT INTO `registration` VALUES (40, 'R20250625362244465', 37, 43, 24, '2025-06-29', '14:00:00', 10.00, '已就诊', '食物过敏', '2025-06-25 23:39:42', '2025-06-26 02:10:59');
INSERT INTO `registration` VALUES (41, 'R20250625123624436', 38, 44, 25, '2025-06-26', '16:40:00', 10.00, '已就诊', '气胸', '2025-06-25 23:40:49', '2025-06-26 02:11:03');
INSERT INTO `registration` VALUES (42, 'R20250625154894053', 39, 45, 25, '2025-06-30', '10:00:00', 10.00, '已就诊', '肺炎', '2025-06-25 23:41:40', '2025-06-26 02:11:06');
INSERT INTO `registration` VALUES (43, 'R20250625163355548', 40, 3, 3, '2025-06-27', '15:00:00', 10.00, '已就诊', '水痘', '2025-06-25 23:43:02', '2025-06-26 02:11:09');
INSERT INTO `registration` VALUES (44, 'R20250625165746694', 41, 3, 3, '2025-06-28', '10:00:00', 10.00, '已就诊', '腹泻', '2025-06-25 23:43:46', '2025-06-26 02:11:12');
INSERT INTO `registration` VALUES (45, 'R20250625316347842', 42, 46, 25, '2025-06-29', '14:00:00', 10.00, '已就诊', '胃溃疡', '2025-06-25 23:44:36', '2025-06-26 02:11:14');
INSERT INTO `registration` VALUES (46, 'R20250625175645356', 43, 47, 26, '2025-06-25', '15:00:00', 10.00, '已就诊', '急性胆囊炎', '2025-06-25 23:45:30', '2025-06-26 02:11:17');
INSERT INTO `registration` VALUES (47, 'R20250625476506570', 44, 48, 26, '2025-06-28', '10:30:00', 10.00, '已就诊', '前列腺增生', '2025-06-25 23:46:44', '2025-06-26 02:11:20');
INSERT INTO `registration` VALUES (48, 'R20250625364726521', 45, 49, 27, '2025-06-26', '10:00:00', 10.00, '已就诊', '白血病', '2025-06-25 23:47:36', '2025-06-26 02:11:24');
INSERT INTO `registration` VALUES (49, 'R20250625374563836', 46, 50, 27, '2025-06-26', '15:00:00', 10.00, '已就诊', '过敏', '2025-06-25 23:48:44', '2025-06-26 02:11:27');
INSERT INTO `registration` VALUES (50, 'R20250625343686524', 47, 51, 28, '2025-06-25', '08:00:00', 10.00, '已就诊', '癌痛', '2025-06-25 23:50:01', '2025-06-26 02:11:31');
INSERT INTO `registration` VALUES (51, 'R20250625164354642', 48, 52, 28, '2025-06-27', '09:50:00', 10.00, '已就诊', '无痛胃肠镜', '2025-06-25 23:50:53', '2025-06-26 02:11:37');
INSERT INTO `registration` VALUES (52, 'R20250625566869376', 49, 53, 29, '2025-06-30', '16:00:00', 10.00, '已就诊', '脑死亡', '2025-06-25 23:51:57', '2025-06-26 02:11:40');
INSERT INTO `registration` VALUES (53, 'R20250625466785345', 50, 54, 29, '2025-06-28', '10:00:00', 10.00, '已就诊', '呼吸衰竭', '2025-06-25 23:52:45', '2025-06-26 02:11:43');

-- ----------------------------
-- Table structure for schedule
-- ----------------------------
DROP TABLE IF EXISTS `schedule`;
CREATE TABLE `schedule`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `doctor_id` int NOT NULL COMMENT '医生ID',
  `schedule_date` date NOT NULL COMMENT '排班日期',
  `time_slot` enum('morning','afternoon','evening') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '时间段',
  `schedule_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '排班类型：门诊,手术,急诊,查房,会诊,休息',
  `status` enum('正常','请假','调班') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '正常' COMMENT '状态',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_schedule`(`doctor_id` ASC, `schedule_date` ASC, `time_slot` ASC) USING BTREE,
  CONSTRAINT `schedule_ibfk_1` FOREIGN KEY (`doctor_id`) REFERENCES `doctor` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 75 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '医生排班表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of schedule
-- ----------------------------
INSERT INTO `schedule` VALUES (1, 1, '2025-06-09', 'morning', '门诊', '正常', NULL, '2025-06-12 18:04:48', '2025-06-12 18:04:48');
INSERT INTO `schedule` VALUES (2, 1, '2025-06-09', 'afternoon', '门诊', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:17');
INSERT INTO `schedule` VALUES (3, 2, '2025-06-10', 'morning', '查房', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:21');
INSERT INTO `schedule` VALUES (4, 2, '2025-06-10', 'afternoon', '手术', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:24');
INSERT INTO `schedule` VALUES (5, 3, '2025-06-10', 'evening', '急诊', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:27');
INSERT INTO `schedule` VALUES (6, 3, '2025-06-11', 'morning', '门诊', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:30');
INSERT INTO `schedule` VALUES (7, 4, '2025-06-11', 'afternoon', '门诊', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:32');
INSERT INTO `schedule` VALUES (8, 4, '2025-06-12', 'morning', '查房', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:35');
INSERT INTO `schedule` VALUES (9, 5, '2025-06-12', 'afternoon', '手术', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:37');
INSERT INTO `schedule` VALUES (10, 5, '2025-06-12', 'evening', '急诊', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:42');
INSERT INTO `schedule` VALUES (11, 6, '2025-06-13', 'morning', '门诊', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:45');
INSERT INTO `schedule` VALUES (12, 6, '2025-06-13', 'afternoon', '门诊', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:48');
INSERT INTO `schedule` VALUES (13, 7, '2025-06-14', 'morning', '查房', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:51');
INSERT INTO `schedule` VALUES (14, 7, '2025-06-14', 'afternoon', '会诊', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:54');
INSERT INTO `schedule` VALUES (15, 8, '2025-06-15', 'morning', '休息', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:15:57');
INSERT INTO `schedule` VALUES (16, 8, '2025-06-15', 'afternoon', '休息', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:16:00');
INSERT INTO `schedule` VALUES (17, 9, '2025-06-15', 'evening', '休息', '正常', NULL, '2025-06-12 18:04:48', '2025-06-26 02:16:09');
INSERT INTO `schedule` VALUES (18, 9, '2025-06-16', 'morning', '门诊', '正常', NULL, '2025-06-26 02:16:45', '2025-06-26 02:16:45');
INSERT INTO `schedule` VALUES (19, 10, '2025-06-16', 'afternoon', '查房', '正常', NULL, '2025-06-26 02:17:15', '2025-06-26 02:17:15');
INSERT INTO `schedule` VALUES (20, 10, '2025-06-16', 'evening', '休息', '正常', NULL, '2025-06-26 02:17:44', '2025-06-26 02:17:44');
INSERT INTO `schedule` VALUES (21, 11, '2025-06-17', 'morning', '门诊', '正常', NULL, '2025-06-26 02:18:06', '2025-06-26 02:18:06');
INSERT INTO `schedule` VALUES (22, 11, '2025-06-17', 'evening', '查房', '正常', NULL, '2025-06-26 02:18:28', '2025-06-26 02:18:28');
INSERT INTO `schedule` VALUES (23, 12, '2025-06-18', 'morning', '查房', '正常', NULL, '2025-06-26 02:18:58', '2025-06-26 02:18:58');
INSERT INTO `schedule` VALUES (24, 12, '2025-06-18', 'afternoon', '门诊', '正常', NULL, '2025-06-26 02:19:21', '2025-06-26 02:19:21');
INSERT INTO `schedule` VALUES (25, 13, '2025-06-18', 'evening', '门诊', '正常', NULL, '2025-06-26 02:19:53', '2025-06-26 02:19:53');
INSERT INTO `schedule` VALUES (26, 13, '2025-06-19', 'morning', '查房', '正常', NULL, '2025-06-26 02:20:18', '2025-06-26 02:20:18');
INSERT INTO `schedule` VALUES (27, 14, '2025-06-19', 'afternoon', '急诊', '正常', NULL, '2025-06-26 02:20:47', '2025-06-26 02:20:47');
INSERT INTO `schedule` VALUES (28, 14, '2025-06-19', 'evening', '休息', '正常', NULL, '2025-06-26 02:21:07', '2025-06-26 02:21:07');
INSERT INTO `schedule` VALUES (29, 15, '2025-06-20', 'morning', '门诊', '正常', NULL, '2025-06-26 02:21:29', '2025-06-26 02:21:29');
INSERT INTO `schedule` VALUES (30, 15, '2025-06-20', 'afternoon', '查房', '正常', NULL, '2025-06-26 02:21:48', '2025-06-26 02:21:48');
INSERT INTO `schedule` VALUES (31, 16, '2025-06-20', 'evening', '急诊', '正常', NULL, '2025-06-26 02:22:17', '2025-06-26 02:22:17');
INSERT INTO `schedule` VALUES (32, 16, '2025-06-21', 'morning', '休息', '正常', NULL, '2025-06-26 02:22:43', '2025-06-26 02:22:43');
INSERT INTO `schedule` VALUES (33, 17, '2025-06-21', 'afternoon', '急诊', '正常', NULL, '2025-06-26 02:23:04', '2025-06-26 02:23:04');
INSERT INTO `schedule` VALUES (34, 17, '2025-06-21', 'evening', '查房', '正常', NULL, '2025-06-26 02:23:27', '2025-06-26 02:23:27');
INSERT INTO `schedule` VALUES (35, 18, '2025-06-22', 'morning', '门诊', '正常', NULL, '2025-06-26 02:23:48', '2025-06-26 02:23:48');
INSERT INTO `schedule` VALUES (36, 18, '2025-06-22', 'afternoon', '休息', '正常', NULL, '2025-06-26 02:24:09', '2025-06-26 02:24:09');
INSERT INTO `schedule` VALUES (37, 19, '2025-06-22', 'evening', '急诊', '正常', NULL, '2025-06-26 02:24:41', '2025-06-26 02:24:41');
INSERT INTO `schedule` VALUES (38, 19, '2025-06-23', 'morning', '休息', '正常', NULL, '2025-06-26 02:25:06', '2025-06-26 02:25:06');
INSERT INTO `schedule` VALUES (39, 20, '2025-06-23', 'afternoon', '门诊', '正常', NULL, '2025-06-26 02:25:36', '2025-06-26 02:25:36');
INSERT INTO `schedule` VALUES (40, 21, '2025-06-23', 'evening', '急诊', '正常', NULL, '2025-06-26 02:25:58', '2025-06-26 02:25:58');
INSERT INTO `schedule` VALUES (41, 22, '2025-06-24', 'morning', '门诊', '正常', NULL, '2025-06-26 02:26:17', '2025-06-26 02:26:17');
INSERT INTO `schedule` VALUES (42, 23, '2025-06-24', 'afternoon', '查房', '正常', NULL, '2025-06-26 02:26:49', '2025-06-26 02:26:49');
INSERT INTO `schedule` VALUES (43, 24, '2025-06-24', 'evening', '急诊', '正常', NULL, '2025-06-26 02:27:12', '2025-06-26 02:27:12');
INSERT INTO `schedule` VALUES (44, 25, '2025-06-25', 'morning', '查房', '正常', NULL, '2025-06-26 02:27:34', '2025-06-26 02:27:34');
INSERT INTO `schedule` VALUES (45, 26, '2025-06-25', 'afternoon', '门诊', '正常', NULL, '2025-06-26 02:27:55', '2025-06-26 02:27:55');
INSERT INTO `schedule` VALUES (46, 27, '2025-06-25', 'evening', '急诊', '正常', NULL, '2025-06-26 02:28:13', '2025-06-26 02:28:13');
INSERT INTO `schedule` VALUES (47, 28, '2025-06-26', 'morning', '门诊', '正常', NULL, '2025-06-26 02:28:36', '2025-06-26 02:28:36');
INSERT INTO `schedule` VALUES (48, 29, '2025-06-26', 'afternoon', '急诊', '正常', NULL, '2025-06-26 02:28:57', '2025-06-26 02:28:57');
INSERT INTO `schedule` VALUES (49, 30, '2025-06-26', 'evening', '查房', '正常', NULL, '2025-06-26 02:29:17', '2025-06-26 02:29:17');
INSERT INTO `schedule` VALUES (50, 31, '2025-06-27', 'morning', '急诊', '正常', NULL, '2025-06-26 02:29:37', '2025-06-26 02:29:37');
INSERT INTO `schedule` VALUES (51, 32, '2025-06-27', 'afternoon', '查房', '正常', NULL, '2025-06-26 02:30:02', '2025-06-26 02:30:02');
INSERT INTO `schedule` VALUES (52, 33, '2025-06-27', 'evening', '门诊', '正常', NULL, '2025-06-26 02:30:22', '2025-06-26 02:30:22');
INSERT INTO `schedule` VALUES (53, 34, '2025-06-28', 'morning', '门诊', '正常', NULL, '2025-06-26 02:30:41', '2025-06-26 02:30:41');
INSERT INTO `schedule` VALUES (54, 35, '2025-06-28', 'afternoon', '急诊', '正常', NULL, '2025-06-26 02:31:10', '2025-06-26 02:31:10');
INSERT INTO `schedule` VALUES (55, 36, '2025-06-28', 'evening', '查房', '正常', NULL, '2025-06-26 02:31:31', '2025-06-26 02:31:31');
INSERT INTO `schedule` VALUES (56, 37, '2025-06-29', 'morning', '查房', '正常', NULL, '2025-06-26 02:31:51', '2025-06-26 02:31:51');
INSERT INTO `schedule` VALUES (57, 38, '2025-06-29', 'afternoon', '门诊', '正常', NULL, '2025-06-26 02:32:13', '2025-06-26 02:32:13');
INSERT INTO `schedule` VALUES (58, 39, '2025-06-29', 'evening', '急诊', '正常', NULL, '2025-06-26 02:32:36', '2025-06-26 02:32:36');
INSERT INTO `schedule` VALUES (59, 40, '2025-06-29', 'morning', '门诊', '正常', NULL, '2025-06-26 02:33:11', '2025-06-26 02:33:11');
INSERT INTO `schedule` VALUES (60, 41, '2025-06-29', 'afternoon', '急诊', '正常', NULL, '2025-06-26 02:33:34', '2025-06-26 02:33:34');
INSERT INTO `schedule` VALUES (61, 42, '2025-06-29', 'evening', '查房', '正常', NULL, '2025-06-26 02:33:56', '2025-06-26 02:33:56');
INSERT INTO `schedule` VALUES (62, 43, '2025-06-29', 'morning', '急诊', '正常', NULL, '2025-06-26 02:34:18', '2025-06-26 02:34:18');
INSERT INTO `schedule` VALUES (63, 44, '2025-06-29', 'afternoon', '急诊', '正常', NULL, '2025-06-26 02:34:36', '2025-06-26 02:34:36');
INSERT INTO `schedule` VALUES (64, 45, '2025-06-29', 'evening', '急诊', '正常', NULL, '2025-06-26 02:34:55', '2025-06-26 02:34:55');
INSERT INTO `schedule` VALUES (65, 46, '2025-06-29', 'morning', '查房', '正常', NULL, '2025-06-26 02:35:17', '2025-06-26 02:35:17');
INSERT INTO `schedule` VALUES (66, 47, '2025-06-29', 'afternoon', '门诊', '正常', NULL, '2025-06-26 02:35:39', '2025-06-26 02:35:39');
INSERT INTO `schedule` VALUES (67, 48, '2025-06-29', 'evening', '查房', '正常', NULL, '2025-06-26 02:36:00', '2025-06-26 02:36:00');
INSERT INTO `schedule` VALUES (68, 49, '2025-06-30', 'morning', '门诊', '正常', NULL, '2025-06-26 02:36:20', '2025-06-26 02:36:20');
INSERT INTO `schedule` VALUES (69, 50, '2025-06-30', 'morning', '门诊', '正常', NULL, '2025-06-26 02:36:48', '2025-06-26 02:36:48');
INSERT INTO `schedule` VALUES (70, 51, '2025-06-30', 'afternoon', '急诊', '正常', NULL, '2025-06-26 02:37:11', '2025-06-26 02:37:11');
INSERT INTO `schedule` VALUES (71, 52, '2025-06-30', 'afternoon', '门诊', '正常', NULL, '2025-06-26 02:37:35', '2025-06-26 02:37:35');
INSERT INTO `schedule` VALUES (72, 53, '2025-06-30', 'evening', '急诊', '正常', NULL, '2025-06-26 02:38:01', '2025-06-26 02:38:01');
INSERT INTO `schedule` VALUES (73, 54, '2025-06-30', 'evening', '急诊', '正常', NULL, '2025-06-26 02:38:20', '2025-06-26 02:38:20');
INSERT INTO `schedule` VALUES (74, 55, '2025-06-30', 'evening', '查房', '正常', NULL, '2025-06-26 02:38:40', '2025-06-26 02:38:40');

SET FOREIGN_KEY_CHECKS = 1;
