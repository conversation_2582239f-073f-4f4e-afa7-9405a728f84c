<%@ page contentType="text/html;charset=UTF-8" language="java" %> <%@ page
import="com.hospital.entity.Doctor" %> <%@ page
import="java.text.SimpleDateFormat" %> <% Doctor doctor = (Doctor)
session.getAttribute("doctor"); if (doctor == null) {
response.sendRedirect(request.getContextPath() + "/login.jsp"); return; }
SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd"); %>
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>个人信息 - 医院挂号就诊管理系统</title>
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 0;
        padding: 0;
        background: #f5f5f5;
      }

      .header {
        background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
        color: white;
        padding: 20px 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        font-size: 1.5em;
        font-weight: bold;
      }

      .nav-links a {
        color: white;
        text-decoration: none;
        margin-left: 20px;
        padding: 8px 16px;
        border-radius: 5px;
        transition: background 0.3s ease;
      }

      .nav-links a:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .container {
        max-width: 1000px;
        margin: 30px auto;
        padding: 0 20px;
      }

      .card {
        background: white;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }

      .card h2 {
        color: #333;
        margin: 0 0 20px 0;
        border-bottom: 2px solid #667eea;
        padding-bottom: 10px;
      }

      .profile-header {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        padding: 25px;
        background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
        border-radius: 10px;
        color: white;
      }

      .profile-avatar {
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5em;
        font-weight: bold;
        margin-right: 25px;
      }

      .profile-info h3 {
        margin: 0 0 10px 0;
        font-size: 1.8em;
      }

      .profile-info p {
        margin: 5px 0;
        opacity: 0.9;
        font-size: 1.1em;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
      }

      .info-section {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 10px;
        border-left: 4px solid #667eea;
      }

      .info-section h4 {
        margin: 0 0 20px 0;
        color: #333;
        font-size: 1.2em;
        display: flex;
        align-items: center;
      }

      .info-section h4::before {
        margin-right: 10px;
        font-size: 1.2em;
      }

      .basic-info h4::before {
        content: "👤";
      }

      .work-info h4::before {
        content: "💼";
      }

      .contact-info h4::before {
        content: "📞";
      }

      .professional-info h4::before {
        content: "🎓";
      }

      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e1e5e9;
      }

      .info-item:last-child {
        margin-bottom: 0;
        border-bottom: none;
      }

      .info-label {
        font-weight: 500;
        color: #666;
        min-width: 120px;
      }

      .info-value {
        color: #333;
        flex: 1;
        text-align: right;
        font-weight: 500;
      }

      .speciality-section {
        grid-column: 1 / -1;
      }

      .speciality-text {
        background: white;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e1e5e9;
        line-height: 1.6;
        color: #555;
      }

      .btn {
        padding: 12px 30px;
        border: none;
        border-radius: 8px;
        font-size: 1.1em;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
      }

      .btn-primary {
        background: #667eea;
        color: white;
      }

      .btn-primary:hover {
        background: #5a6fd8;
      }

      .btn-secondary {
        background: #6c757d;
        color: white;
      }

      .btn-secondary:hover {
        background: #5a6268;
      }

      .alert {
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
      }

      .alert-info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="header-content">
        <div class="logo">🏥 医院管理系统</div>
        <div class="nav-links">
          <a href="${pageContext.request.contextPath}/doctor/index.jsp"
            >返回首页</a
          >
          <a href="${pageContext.request.contextPath}/LogoutServlet"
            >退出登录</a
          >
        </div>
      </div>
    </div>

    <div class="container">
      <div class="card">
        <h2>个人信息</h2>

        <div class="alert alert-info">
          <strong>提示：</strong> 如需修改个人信息，请联系系统管理员。
        </div>

        <div class="profile-header">
          <div class="profile-avatar">
            <%= doctor.getName().substring(0, 1) %>
          </div>
          <div class="profile-info">
            <h3><%= doctor.getName() %> 医生</h3>
            <p>
              <%= doctor.getTitle() != null ? doctor.getTitle() : "医师" %> |
              <%= doctor.getDeptName() != null ? doctor.getDeptName() :
              "未分配科室" %>
            </p>
            <p>
              工号：<%= doctor.getDoctorNo() %> | 状态：<%= doctor.getStatus()
              %>
            </p>
            <p>
              入职时间：<%= doctor.getCreateTime() != null ?
              dateFormat.format(doctor.getCreateTime()) : "未知" %>
            </p>
          </div>
        </div>

        <div class="info-grid">
          <div class="info-section basic-info">
            <h4>基本信息</h4>
            <div class="info-item">
              <span class="info-label">姓名</span>
              <span class="info-value"><%= doctor.getName() %></span>
            </div>
            <div class="info-item">
              <span class="info-label">性别</span>
              <span class="info-value"><%= doctor.getGender() %></span>
            </div>
            <div class="info-item">
              <span class="info-label">年龄</span>
              <span class="info-value"
                ><%= doctor.getAge() != null ? doctor.getAge() + "岁" : "未填写"
                %></span
              >
            </div>
            <div class="info-item">
              <span class="info-label">工号</span>
              <span class="info-value"><%= doctor.getDoctorNo() %></span>
            </div>
          </div>

          <div class="info-section work-info">
            <h4>工作信息</h4>
            <div class="info-item">
              <span class="info-label">工号</span>
              <span class="info-value"><%= doctor.getDoctorNo() %></span>
            </div>
            <div class="info-item">
              <span class="info-label">所属科室</span>
              <span class="info-value"
                ><%= doctor.getDeptName() != null ? doctor.getDeptName() :
                "未分配" %></span
              >
            </div>
            <div class="info-item">
              <span class="info-label">职称</span>
              <span class="info-value"
                ><%= doctor.getTitle() != null ? doctor.getTitle() : "未设置"
                %></span
              >
            </div>
            <div class="info-item">
              <span class="info-label">工作状态</span>
              <span class="info-value"><%= doctor.getStatus() %></span>
            </div>
          </div>

          <div class="info-section contact-info">
            <h4>联系信息</h4>
            <div class="info-item">
              <span class="info-label">手机号码</span>
              <span class="info-value"
                ><%= doctor.getPhone() != null ? doctor.getPhone() : "未填写"
                %></span
              >
            </div>
            <div class="info-item">
              <span class="info-label">电子邮箱</span>
              <span class="info-value"
                ><%= doctor.getEmail() != null ? doctor.getEmail() : "未填写"
                %></span
              >
            </div>
            <div class="info-item">
              <span class="info-label">状态</span>
              <span class="info-value"><%= doctor.getStatus() %></span>
            </div>
          </div>

          <div class="info-section professional-info">
            <h4>专业信息</h4>
            <div class="info-item">
              <span class="info-label">学历</span>
              <span class="info-value">医学博士</span>
            </div>
            <div class="info-item">
              <span class="info-label">执业证号</span>
              <span class="info-value">110101199001011234</span>
            </div>
            <div class="info-item">
              <span class="info-label">入职时间</span>
              <span class="info-value"
                ><%= doctor.getCreateTime() != null ?
                dateFormat.format(doctor.getCreateTime()) : "未知" %></span
              >
            </div>
            <div class="info-item">
              <span class="info-label">最后更新</span>
              <span class="info-value"
                ><%= doctor.getUpdateTime() != null ?
                dateFormat.format(doctor.getUpdateTime()) : "未更新" %></span
              >
            </div>
          </div>

          <% if (doctor.getSpeciality() != null &&
          !doctor.getSpeciality().trim().isEmpty()) { %>
          <div class="info-section speciality-section">
            <h4>专业特长</h4>
            <div class="speciality-text"><%= doctor.getSpeciality() %></div>
          </div>
          <% } %>
        </div>

        <div style="text-align: center; margin-top: 30px">
          <a
            href="${pageContext.request.contextPath}/doctor/registrations.jsp"
            class="btn btn-primary"
            >患者管理</a
          >
          <a
            href="${pageContext.request.contextPath}/doctor/medical-records.jsp"
            class="btn btn-secondary"
            >就诊记录</a
          >
        </div>
      </div>
    </div>
  </body>
</html>
