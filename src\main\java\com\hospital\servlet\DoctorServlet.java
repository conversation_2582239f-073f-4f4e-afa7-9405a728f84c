package com.hospital.servlet;

import com.hospital.entity.Admin;
import com.hospital.entity.Doctor;
import com.hospital.service.DoctorService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * 医生管理控制器
 */
@WebServlet("/DoctorServlet")
public class DoctorServlet extends HttpServlet {
    
    private DoctorService doctorService;
    
    @Override
    public void init() throws ServletException {
        super.init();
        BeanFactory beanFactory = BeanFactory.getInstance();
        doctorService = beanFactory.getApplicationContext().getBean(DoctorService.class);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        
        HttpSession session = request.getSession();
        Admin admin = (Admin) session.getAttribute("admin");
        
        if (admin == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            handleAddDoctor(request, response);
        } else {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "无效的操作");
        }
    }
    
    /**
     * 处理添加医生
     */
    private void handleAddDoctor(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        try {
            // 获取表单参数
            String name = request.getParameter("name");
            String doctorNo = request.getParameter("doctorNo");
            String gender = request.getParameter("gender");
            String ageStr = request.getParameter("age");
            String phone = request.getParameter("phone");
            String email = request.getParameter("email");
            String deptIdStr = request.getParameter("deptId");
            String title = request.getParameter("title");
            String speciality = request.getParameter("speciality");
            String password = request.getParameter("password");
            
            // 验证必填字段
            if (name == null || name.trim().isEmpty()) {
                response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("姓名不能为空", "UTF-8"));
                return;
            }
            
            if (doctorNo == null || doctorNo.trim().isEmpty()) {
                response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("工号不能为空", "UTF-8"));
                return;
            }
            
            if (gender == null || gender.trim().isEmpty()) {
                response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("性别不能为空", "UTF-8"));
                return;
            }
            
            if (phone == null || phone.trim().isEmpty()) {
                response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("手机号码不能为空", "UTF-8"));
                return;
            }
            
            if (deptIdStr == null || deptIdStr.trim().isEmpty()) {
                response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("所属科室不能为空", "UTF-8"));
                return;
            }
            
            if (password == null || password.trim().isEmpty()) {
                response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("登录密码不能为空", "UTF-8"));
                return;
            }
            
            // 检查工号是否已存在
            Doctor existingDoctor = doctorService.findByDoctorNo(doctorNo.trim());
            if (existingDoctor != null) {
                response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("工号已存在，请使用其他工号", "UTF-8"));
                return;
            }
            
            // 创建医生对象
            Doctor doctor = new Doctor();
            doctor.setName(name.trim());
            doctor.setDoctorNo(doctorNo.trim());
            doctor.setGender(gender.trim());
            doctor.setPhone(phone.trim());
            doctor.setPassword(password.trim());
            
            // 处理可选字段
            if (ageStr != null && !ageStr.trim().isEmpty()) {
                try {
                    doctor.setAge(Integer.parseInt(ageStr.trim()));
                } catch (NumberFormatException e) {
                    response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("年龄格式不正确", "UTF-8"));
                    return;
                }
            }
            
            if (email != null && !email.trim().isEmpty()) {
                doctor.setEmail(email.trim());
            }
            
            try {
                doctor.setDeptId(Integer.parseInt(deptIdStr.trim()));
            } catch (NumberFormatException e) {
                response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("科室ID格式不正确", "UTF-8"));
                return;
            }
            
            if (title != null && !title.trim().isEmpty()) {
                doctor.setTitle(title.trim());
            }
            
            if (speciality != null && !speciality.trim().isEmpty()) {
                doctor.setSpeciality(speciality.trim());
            }
            
            // 设置默认状态
            doctor.setStatus("在职");
            
            // 保存医生
            boolean success = doctorService.addDoctor(doctor);
            
            if (success) {
                response.sendRedirect(request.getContextPath() + "/admin/doctors.jsp?success=" + URLEncoder.encode("医生添加成功", "UTF-8"));
            } else {
                response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("添加失败，请重试", "UTF-8"));
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect(request.getContextPath() + "/admin/add-doctor.jsp?error=" + URLEncoder.encode("系统错误：" + e.getMessage(), "UTF-8"));
        }
    }
}
