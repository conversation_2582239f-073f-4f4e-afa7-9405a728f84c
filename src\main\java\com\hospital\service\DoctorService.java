package com.hospital.service;

import com.hospital.entity.Doctor;
import java.util.List;

/**
 * 医生服务接口
 * 定义了医生相关的业务逻辑操作，包括医生管理、登录验证、信息查询等功能。
 * 该接口封装了医生业务的核心操作，为控制层提供统一的服务接口。
 * 主要功能模块：
 * 1. 医生账户管理：登录验证、密码修改
 * 2. 医生信息管理：增加、修改、删除、查询
 * 3. 科室关联管理：按科室查询医生、统计科室医生数量
 * 4. 医生检索：分页查询、条件查询、统计
 * 5. 业务验证：工号唯一性验证
 */
public interface DoctorService {

    /**
     医生登录
     验证医生的登录凭据，包括医生工号和密码的匹配性。
     登录成功后返回完整的医生信息对象，包含科室信息。
     */
    Doctor login(String doctorNo, String password);

    /**
     根据ID查询医生
     通过医生的主键ID查询医生详细信息，包含关联的科室信息。
     */
    Doctor findById(Integer id);

    /**
     根据医生工号查询医生
     通过医生工号查询医生详细信息，医生工号具有唯一性。
     */
    Doctor findByDoctorNo(String doctorNo);

    /**
     根据科室ID查询医生列表
     查询指定科室下的所有医生，包括在职和离职的医生。
     */
    List<Doctor> findByDeptId(Integer deptId);

    /**
     根据科室ID查询在职医生
     查询指定科室下状态为"在职"的医生列表，用于患者挂号时选择医生。
     */
    List<Doctor> findActiveDoctorsByDeptId(Integer deptId);

    /**
     查询所有医生（包含科室信息）
     获取系统中所有医生的信息列表，通过连表查询包含科室名称。
     */
    List<Doctor> findAllWithDept();

    /**
     根据姓名模糊查询医生
     支持医生姓名的模糊匹配查询，用于管理员搜索医生信息。
     */
    List<Doctor> findByNameLike(String name);
    
    /**
     添加医生
     添加新的医生到系统中，包括生成医生工号、密码加密等操作。
     */
    boolean addDoctor(Doctor doctor);

    /**
     更新医生信息
     更新医生的基本信息，如姓名、年龄、联系方式、职称、专业特长等。
     */
    boolean updateDoctor(Doctor doctor);

    /**
     删除医生
     从系统中删除指定的医生记录
     */
    boolean deleteDoctor(Integer id);

    /**
     修改密码
     修改医生的登录密码，需要验证旧密码的正确性。
     */
    boolean changePassword(Integer id, String oldPassword, String newPassword);

    /**
     * 查询所有医生获取系统中所有医生的信息列表。
     */
    List<Doctor> findAll();

    /**
     分页查询医生
     按页码和每页大小分页查询医生信息，用于处理大量医生数据的场景。
     */
    List<Doctor> findByPage(int page, int size);

    /**
     统计医生总数
     统计系统中所有医生的总数量，常用于分页查询时计算总页数。
     */
    int count();

    /**
     验证医生工号是否存在
     检查指定的医生工号是否已经在系统中存在，用于避免重复添加。
     */
    boolean isDoctorNoExists(String doctorNo);

    /**
     统计指定科室的医生数量
     统计指定科室下的医生总数，用于科室管理和统计分析。
     */
    int countByDeptId(Integer deptId);
}
