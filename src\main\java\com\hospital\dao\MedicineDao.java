package com.hospital.dao;

import com.hospital.entity.Medicine;
import java.util.List;

/**
 * 药品DAO接口
 * 提供药品的数据访问方法
 */
public interface MedicineDao extends BaseDao<Medicine> {
    
    /**
     * 根据药品编码查询药品
     */
    Medicine findByMedicineCode(String medicineCode);
    
    /**
     * 根据药品名称模糊查询
     */
    List<Medicine> findByMedicineNameLike(String medicineName);
    
    /**
     * 根据药品类型查询药品列表
     * @param medicineType 药品类型（中药、西药）
     */
    List<Medicine> findByMedicineType(String medicineType);
    
    /**
     * 根据分类ID查询药品列表
     */
    List<Medicine> findByCategoryId(Integer categoryId);
    
    /**
     * 根据状态查询药品列表
     * @param status 状态（在用、停用）
     */
    List<Medicine> findByStatus(String status);
    
    /**
     * 查询所有药品（包含分类信息）
     */
    List<Medicine> findAllWithCategory();
    
    /**
     * 根据药品类型查询药品（包含分类信息）
     */
    List<Medicine> findByMedicineTypeWithCategory(String medicineType);
    
    /**
     * 根据药品名称模糊查询（包含分类信息）
     */
    List<Medicine> findByMedicineNameLikeWithCategory(String medicineName);
    
    /**
     * 检查药品编码是否已存在
     */
    boolean existsByMedicineCode(String medicineCode);
    
    /**
     * 更新药品库存
     */
    int updateStock(Integer medicineId, Integer stock);
    
    /**
     * 减少药品库存
     */
    int reduceStock(Integer medicineId, Integer quantity);
}
