package com.hospital.listener;

import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

/**
 应用初始化监听器
 该监听器负责医院管理系统的启动和关闭过程管理
 在Web应用启动时初始化系统组件，在应用关闭时清理系统资源。
 主要职责：
 1. 应用启动时初始化IoC容器和Bean工厂
 2. 初始化数据库连接池和其他系统资源
 3. 将核心组件注册到ServletContext中供全局使用
 4. 应用关闭时清理资源，防止内存泄漏
 5. 提供启动和关闭过程的日志记录
 配置方式：
 在web.xml中配置为<listener>或使用@WebListener注解
 */
public class ApplicationInitListener implements ServletContextListener {

    /**
     应用上下文初始化方法
     在Web应用启动时被容器调用，负责初始化系统的核心组件。
     初始化过程包括IoC容器创建、Bean工厂初始化、依赖注入配置等。
     初始化步骤：
     1. 创建并初始化Bean工厂
     2. 初始化所有Bean实例和依赖关系
     3. 将Bean工厂注册到ServletContext中
     4. 记录初始化结果
     */
    @Override
    public void contextInitialized(ServletContextEvent sce) {
        System.out.println("正在初始化医院管理系统...");

        try {
            // 初始化Bean工厂，这会触发所有Bean的创建和依赖注入
            BeanFactory beanFactory = BeanFactory.getInstance();

            // 将Bean工厂存储到ServletContext中，供Servlet和Filter等组件使用
            sce.getServletContext().setAttribute("beanFactory", beanFactory);

            System.out.println("医院管理系统初始化完成！");

        } catch (Exception e) {
            // 记录初始化失败的详细信息
            System.err.println("医院管理系统初始化失败：" + e.getMessage());
            e.printStackTrace();
            // 抛出运行时异常，阻止应用启动
            throw new RuntimeException("应用初始化失败", e);
        }
    }

    /**
     应用上下文销毁方法
     在Web应用关闭时被容器调用，负责清理系统资源和执行关闭逻辑。
     确保所有资源得到正确释放，防止内存泄漏和资源占用。
     清理步骤：
     1. 从ServletContext中获取Bean工厂
     2. 执行资源清理逻辑（如关闭数据库连接池）
     3. 清理缓存和临时数据
     4. 记录销毁结果
     */
    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        System.out.println("正在销毁医院管理系统...");

        try {
            // 从ServletContext中获取Bean工厂
            BeanFactory beanFactory = (BeanFactory) sce.getServletContext().getAttribute("beanFactory");
            if (beanFactory != null) {
                // 执行资源清理逻辑
                System.out.println("清理应用资源完成");
            }

        } catch (Exception e) {
            // 记录销毁过程中的错误，但不阻止应用关闭
            System.err.println("应用销毁过程中发生错误：" + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("医院管理系统已销毁");
    }
}
