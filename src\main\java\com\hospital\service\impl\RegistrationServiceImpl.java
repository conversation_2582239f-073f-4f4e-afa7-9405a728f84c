package com.hospital.service.impl;

import com.hospital.dao.RegistrationDao;
import com.hospital.entity.Registration;
import com.hospital.service.RegistrationService;
import com.hospital.util.TransactionManager;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 挂号服务实现类
 */
public class RegistrationServiceImpl implements RegistrationService {

    private RegistrationDao registrationDao;

    public void setRegistrationDao(RegistrationDao registrationDao) {
        this.registrationDao = registrationDao;
    }
    
    @Override
    public boolean register(Registration registration) {
        // 生成挂号单号
        if (registration.getRegNo() == null || registration.getRegNo().isEmpty()) {
            registration.setRegNo(generateRegNo());
        }
        
        // 检查挂号单号是否已存在
        if (isRegNoExists(registration.getRegNo())) {
            registration.setRegNo(generateRegNo());
        }
        
        // 设置默认状态
        if (registration.getStatus() == null) {
            registration.setStatus("已挂号");
        }
        
        return registrationDao.insert(registration) > 0;
    }
    
    @Override
    public boolean cancelRegistration(Integer id, Integer patientId) {
        Registration registration = registrationDao.findById(id);
        if (registration == null || !registration.getPatientId().equals(patientId)) {
            return false;
        }
        
        // 只有"已挂号"状态的记录才能取消
        if (!"已挂号".equals(registration.getStatus())) {
            return false;
        }
        
        return registrationDao.updateStatus(id, "已取消") > 0;
    }
    
    @Override
    public Registration findById(Integer id) {
        return registrationDao.findById(id);
    }
    
    @Override
    public Registration findByRegNo(String regNo) {
        return registrationDao.findByRegNo(regNo);
    }
    
    @Override
    public List<Registration> findByPatientId(Integer patientId) {
        return registrationDao.findByPatientId(patientId);
    }
    
    @Override
    public List<Registration> findByDoctorId(Integer doctorId) {
        return registrationDao.findByDoctorId(doctorId);
    }
    
    @Override
    public List<Registration> findAllWithDetails() {
        return registrationDao.findAllWithDetails();
    }
    
    @Override
    public List<Registration> findByPatientIdWithDetails(Integer patientId) {
        return registrationDao.findByPatientIdWithDetails(patientId);
    }
    
    @Override
    public List<Registration> findByDoctorIdWithDetails(Integer doctorId) {
        return registrationDao.findByDoctorIdWithDetails(doctorId);
    }
    
    @Override
    public boolean updateStatus(Integer id, String status) {
        return registrationDao.updateStatus(id, status) > 0;
    }
    
    @Override
    public String generateRegNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateTimeStr = sdf.format(new Date());
        
        // 生成挂号单号：R + 日期时间 + 3位随机数
        int random = (int) (Math.random() * 1000);
        return String.format("R%s%03d", dateTimeStr, random);
    }
    
    @Override
    public boolean isRegNoExists(String regNo) {
        return registrationDao.findByRegNo(regNo) != null;
    }
    
    @Override
    public int countByDoctorAndDateRange(Integer doctorId, Date startDate, Date endDate) {
        return registrationDao.countByDoctorAndDateRange(doctorId, startDate, endDate);
    }
    
    @Override
    public List<Registration> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return registrationDao.findByPage(offset, size);
    }
    
    @Override
    public int count() {
        return registrationDao.count();
    }

    @Override
    public int countByDeptId(Integer deptId) {
        return registrationDao.countByDeptId(deptId);
    }

    @Override
    public int countByDeptIdAndMonth(Integer deptId) {
        return registrationDao.countByDeptIdAndMonth(deptId);
    }


}
