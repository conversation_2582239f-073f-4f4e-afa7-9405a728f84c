<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.Department" %>
<%@ page import="com.hospital.service.DepartmentService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    // 测试参数
    String testId = request.getParameter("id");
    if (testId == null) {
        testId = "1"; // 默认测试ID
    }
    
    try {
        // 测试服务获取
        DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
        Department department = departmentService.findById(Integer.parseInt(testId));
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>编辑科室功能测试 - 医院管理系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .nav-links { margin: 20px 0; }
        .nav-links a { display: inline-block; margin-right: 15px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
        .nav-links a:hover { background: #5a6fd8; }
        .dept-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🏥 编辑科室功能测试页面</h1>
            <p>欢迎，<strong><%= admin.getUsername() %></strong> 管理员！</p>
            
            <div class="success">
                <h2>✅ 测试结果：编辑科室功能正常！</h2>
                <p>JSP页面已成功使用JspServiceUtil获取服务，Spring依赖已完全移除！</p>
            </div>
            
            <div class="info">
                <h3>📋 测试信息</h3>
                <ul>
                    <li><strong>当前路径</strong>: <%= request.getRequestURI() %></li>
                    <li><strong>Context Path</strong>: <%= request.getContextPath() %></li>
                    <li><strong>测试科室ID</strong>: <%= testId %></li>
                    <li><strong>服务器信息</strong>: <%= application.getServerInfo() %></li>
                </ul>
            </div>
            
            <% if (department != null) { %>
            <div class="dept-info">
                <h3>🏥 科室信息测试</h3>
                <p><strong>科室ID</strong>: <%= department.getId() %></p>
                <p><strong>科室名称</strong>: <%= department.getDeptName() %></p>
                <p><strong>科室位置</strong>: <%= department.getLocation() != null ? department.getLocation() : "未设置" %></p>
                <p><strong>科室描述</strong>: <%= department.getDescription() != null ? department.getDescription() : "无描述" %></p>
                <p><strong>创建时间</strong>: <%= department.getCreateTime() != null ? department.getCreateTime() : "未知" %></p>
            </div>
            <% } else { %>
            <div class="info">
                <h3>⚠️ 科室信息</h3>
                <p>未找到ID为 <%= testId %> 的科室，可能该科室不存在。</p>
            </div>
            <% } %>
            
            <div class="nav-links">
                <h3>🚀 功能导航</h3>
                <% if (department != null) { %>
                <a href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=<%= department.getId() %>">正式编辑科室页面</a>
                <% } %>
                <a href="${pageContext.request.contextPath}/admin/departments.jsp">科室管理</a>
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
            </div>
            
            <div class="info">
                <h3>🔧 技术信息</h3>
                <ul>
                    <li><strong>服务获取方式</strong>: JspServiceUtil.getDepartmentService(application)</li>
                    <li><strong>Spring依赖</strong>: 已完全移除</li>
                    <li><strong>原生IoC容器</strong>: BeanFactory + ApplicationContext</li>
                    <li><strong>数据库连接</strong>: Druid连接池</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>📝 修复说明</h3>
                <p>已修复的问题：</p>
                <ul>
                    <li>✅ 替换了Spring的WebApplicationContextUtils</li>
                    <li>✅ 使用JspServiceUtil获取DepartmentService</li>
                    <li>✅ 科室信息正常加载和显示</li>
                    <li>✅ 编辑表单可以正常工作</li>
                </ul>
            </div>
        </div>
    </div>
    
<%
    } catch (Exception e) {
%>
    <div class="container">
        <div class="card">
            <div class="error">
                <h2>❌ 测试失败</h2>
                <p>错误信息: <%= e.getMessage() %></p>
                <pre><%= e.toString() %></pre>
            </div>
        </div>
    </div>
<%
    }
%>
</body>
</html>
