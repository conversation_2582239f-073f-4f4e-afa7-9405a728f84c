package com.hospital.servlet;

import com.hospital.entity.Patient;
import com.hospital.entity.Registration;
import com.hospital.service.RegistrationService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLEncoder;

/**
 取消挂号控制器（患者端）
 */
@WebServlet("/CancelRegistrationServlet")
public class CancelRegistrationServlet extends HttpServlet {
    
    private RegistrationService registrationService;
    
    @Override
    public void init() throws ServletException {
        BeanFactory beanFactory = BeanFactory.getInstance();
        registrationService = beanFactory.getApplicationContext().getBean(RegistrationService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        
        HttpSession session = request.getSession();
        Patient patient = (Patient) session.getAttribute("patient");
        
        if (patient == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取挂号记录ID
        String idStr = request.getParameter("id");
        if (idStr == null || idStr.trim().isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/patient/my-registrations.jsp?error=" + URLEncoder.encode("挂号记录ID不能为空", "UTF-8"));
            return;
        }
        
        try {
            Integer registrationId = Integer.parseInt(idStr);
            
            // 验证挂号记录是否属于当前患者
            Registration registration = registrationService.findById(registrationId);
            if (registration == null) {
                response.sendRedirect(request.getContextPath() + "/patient/my-registrations.jsp?error=" + URLEncoder.encode("挂号记录不存在", "UTF-8"));
                return;
            }
            
            if (!registration.getPatientId().equals(patient.getId())) {
                response.sendRedirect(request.getContextPath() + "/patient/my-registrations.jsp?error=" + URLEncoder.encode("无权限取消此挂号记录", "UTF-8"));
                return;
            }
            
            // 检查是否已经取消
            if ("已取消".equals(registration.getStatus())) {
                response.sendRedirect(request.getContextPath() + "/patient/my-registrations.jsp?error=" + URLEncoder.encode("该挂号已经取消", "UTF-8"));
                return;
            }
            
            // 执行取消操作（更新状态为已取消）
            boolean success = registrationService.updateStatus(registrationId, "已取消");
            
            if (success) {
                response.sendRedirect(request.getContextPath() + "/patient/my-registrations.jsp?success=" + URLEncoder.encode("挂号取消成功", "UTF-8"));
            } else {
                response.sendRedirect(request.getContextPath() + "/patient/my-registrations.jsp?error=" + URLEncoder.encode("取消失败，请重试", "UTF-8"));
            }
            
        } catch (NumberFormatException e) {
            response.sendRedirect(request.getContextPath() + "/patient/my-registrations.jsp?error=" + URLEncoder.encode("无效的挂号记录ID", "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect(request.getContextPath() + "/patient/my-registrations.jsp?error=" + URLEncoder.encode("系统错误：" + e.getMessage(), "UTF-8"));
        }
    }
}
