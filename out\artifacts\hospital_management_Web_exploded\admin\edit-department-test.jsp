<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    String idStr = request.getParameter("id");
    Integer deptId = null;
    try {
        deptId = Integer.parseInt(idStr);
    } catch (Exception e) {
        deptId = 0;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>编辑科室测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .nav-links a { display: inline-block; margin-right: 15px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🏥 编辑科室测试页面</h1>
            
            <div class="success">
                <h2>✅ 测试结果：页面可以正常访问！</h2>
                <p>这个测试页面证明路径是正确的，edit-department.jsp应该也能正常工作。</p>
            </div>
            
            <div class="info">
                <h3>📋 请求信息</h3>
                <p><strong>管理员用户名:</strong> <%= admin.getUsername() %></p>
                <p><strong>科室ID参数:</strong> <%= idStr != null ? idStr : "未提供" %></p>
                <p><strong>解析后的ID:</strong> <%= deptId %></p>
                <p><strong>请求URI:</strong> <%= request.getRequestURI() %></p>
                <p><strong>Context Path:</strong> <%= request.getContextPath() %></p>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
            </div>
            
            <div class="info">
                <h3>🔧 问题诊断</h3>
                <p>如果这个页面可以访问，但edit-department.jsp不能访问，可能的原因：</p>
                <ul>
                    <li>Web服务器缓存问题 - 需要重启Tomcat</li>
                    <li>文件编译问题 - JSP文件可能有语法错误</li>
                    <li>类路径问题 - 缺少必要的类或依赖</li>
                    <li>权限问题 - 文件权限不正确</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>🚀 解决方案</h3>
                <ol>
                    <li><strong>重启Tomcat服务器</strong> - 清除缓存和重新加载</li>
                    <li><strong>检查文件权限</strong> - 确保文件可读</li>
                    <li><strong>查看服务器日志</strong> - 检查是否有编译错误</li>
                    <li><strong>使用备用文件</strong> - 尝试使用其他编辑页面</li>
                </ol>
            </div>
            
            <div class="nav-links">
                <h3>🔗 测试链接</h3>
                <a href="${pageContext.request.contextPath}/admin/edit-department.jsp?id=<%= deptId %>">尝试访问edit-department.jsp</a>
                <a href="${pageContext.request.contextPath}/admin/edit-department-simple.jsp?id=<%= deptId %>">尝试简化版本</a>
                <a href="${pageContext.request.contextPath}/admin/departments.jsp">返回科室管理</a>
                <a href="${pageContext.request.contextPath}/admin/database-diagnostic.jsp">数据库诊断</a>
            </div>
            
            <div class="info">
                <h3>📋 文件状态检查</h3>
                <%
                    String editDeptPath = application.getRealPath("/admin/edit-department.jsp");
                    java.io.File editDeptFile = new java.io.File(editDeptPath);
                %>
                <p><strong>edit-department.jsp 路径:</strong> <%= editDeptPath %></p>
                <p><strong>文件存在:</strong> <%= editDeptFile.exists() ? "✅ 是" : "❌ 否" %></p>
                <p><strong>文件可读:</strong> <%= editDeptFile.canRead() ? "✅ 是" : "❌ 否" %></p>
                <p><strong>文件大小:</strong> <%= editDeptFile.length() %> 字节</p>
                <p><strong>最后修改:</strong> <%= new java.util.Date(editDeptFile.lastModified()) %></p>
            </div>
            
            <div class="info">
                <h3>💡 立即行动建议</h3>
                <p>基于当前情况，建议按以下顺序操作：</p>
                <ol>
                    <li><strong>重启Tomcat</strong> - 这是最常见的解决方案</li>
                    <li><strong>清除浏览器缓存</strong> - Ctrl+F5强制刷新</li>
                    <li><strong>检查服务器日志</strong> - 查看catalina.out或localhost日志</li>
                    <li><strong>使用备用页面</strong> - 如果主页面仍有问题</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
