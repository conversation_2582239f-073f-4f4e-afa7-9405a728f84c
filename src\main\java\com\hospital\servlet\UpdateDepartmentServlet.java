package com.hospital.servlet;

import com.hospital.entity.Admin;
import com.hospital.entity.Department;
import com.hospital.service.DepartmentService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * 更新科室信息控制器
 */
@WebServlet("/UpdateDepartmentServlet")
public class UpdateDepartmentServlet extends HttpServlet {
    
    private DepartmentService departmentService;
    
    @Override
    public void init() throws ServletException {
        BeanFactory beanFactory = BeanFactory.getInstance();
        departmentService = beanFactory.getApplicationContext().getBean(DepartmentService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        
        HttpSession session = request.getSession();
        Admin admin = (Admin) session.getAttribute("admin");
        
        if (admin == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取表单参数
        String idStr = request.getParameter("id");
        String deptName = request.getParameter("deptName");
        String location = request.getParameter("location");
        String phone = request.getParameter("phone");
        String deptDesc = request.getParameter("deptDesc");
        
        // 参数验证
        if (idStr == null || idStr.trim().isEmpty() ||
            deptName == null || deptName.trim().isEmpty()) {
            
            response.sendRedirect(request.getContextPath() + "/admin/edit-department.jsp?id=" + idStr + "&error=" + URLEncoder.encode("请填写科室名称", "UTF-8"));
            return;
        }
        
        try {
            Integer deptId = Integer.parseInt(idStr);
            
            // 获取现有科室信息
            Department department = departmentService.findById(deptId);
            if (department == null) {
                response.sendRedirect(request.getContextPath() + "/admin/departments.jsp?error=" + URLEncoder.encode("科室不存在", "UTF-8"));
                return;
            }
            
            // 更新科室信息
            department.setDeptName(deptName.trim());
            
            // 处理可选字段
            if (location != null && !location.trim().isEmpty()) {
                department.setLocation(location.trim());
            } else {
                department.setLocation(null);
            }
            
            if (phone != null && !phone.trim().isEmpty()) {
                department.setPhone(phone.trim());
            } else {
                department.setPhone(null);
            }
            
            if (deptDesc != null && !deptDesc.trim().isEmpty()) {
                department.setDeptDesc(deptDesc.trim());
            } else {
                department.setDeptDesc(null);
            }
            
            // 保存更新
            boolean success = departmentService.updateDepartment(department);
            
            if (success) {
                response.sendRedirect(request.getContextPath() + "/admin/view-department.jsp?id=" + deptId + "&success=" + URLEncoder.encode("科室信息更新成功", "UTF-8"));
            } else {
                response.sendRedirect(request.getContextPath() + "/admin/edit-department.jsp?id=" + deptId + "&error=" + URLEncoder.encode("更新失败，请重试", "UTF-8"));
            }
            
        } catch (NumberFormatException e) {
            response.sendRedirect(request.getContextPath() + "/admin/edit-department.jsp?id=" + idStr + "&error=" + URLEncoder.encode("参数格式错误", "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect(request.getContextPath() + "/admin/edit-department.jsp?id=" + idStr + "&error=" + URLEncoder.encode("系统错误：" + e.getMessage(), "UTF-8"));
        }
    }
}
