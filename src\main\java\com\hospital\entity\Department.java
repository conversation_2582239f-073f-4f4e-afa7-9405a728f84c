package com.hospital.entity;

import java.util.Date;

/**
 科室实体类
 该类用于表示医院管理系统中的科室信息，包含科室的基本信息、
 位置、联系方式等字段，以及系统相关的时间戳等。
 数据库对应表：department
 */
public class Department {
    private Integer id;
    private String deptName;
    private String deptDesc;
    private String location;
    private String phone;
    private Date createTime;
    private Date updateTime;

    public Department() {}

    public Department(String deptName, String deptDesc, String location, String phone) {
        this.deptName = deptName;
        this.deptDesc = deptDesc;
        this.location = location;
        this.phone = phone;
    }

    // ==================== Getter和Setter方法 ====================

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptDesc() {
        return deptDesc;
    }

    public void setDeptDesc(String deptDesc) {
        this.deptDesc = deptDesc;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     重写toString方法
     用于调试和日志输出，返回科室对象的字符串表示
     */
    @Override
    public String toString() {
        return "Department{" +
                "id=" + id +
                ", deptName='" + deptName + '\'' +
                ", deptDesc='" + deptDesc + '\'' +
                ", location='" + location + '\'' +
                ", phone='" + phone + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
