package com.hospital.servlet;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 退出登录控制器
 */
@WebServlet("/LogoutServlet")
public class LogoutServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // 获取session
        HttpSession session = request.getSession(false);
        
        if (session != null) {
            // 清除session中的用户信息
            session.removeAttribute("patient");
            session.removeAttribute("doctor");
            session.removeAttribute("admin");
            session.removeAttribute("userType");
            
            // 销毁session
            session.invalidate();
        }
        
        // 重定向到首页
        response.sendRedirect(request.getContextPath() + "/index.jsp");
    }
}
