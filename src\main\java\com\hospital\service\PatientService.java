package com.hospital.service;

import com.hospital.entity.Patient;
import java.util.List;

/**
 * 患者服务接口
 * 定义了患者相关的业务逻辑操作，包括患者注册、登录、信息管理等功能。
 * 该接口封装了患者业务的核心操作，为控制层提供统一的服务接口。
 * 主要功能模块：
 * 1. 患者账户管理：注册、登录、密码修改
 * 2. 患者信息管理：查询、更新、删除
 * 3. 患者数据检索：分页查询、条件查询、统计
 * 4. 业务验证：编号生成、唯一性验证
 */
public interface PatientService {

    /**
     患者注册
     处理新患者的注册流程，包括数据验证、编号生成、密码加密等操作。
     */
    boolean register(Patient patient);

    /**
     患者登录
     验证患者的登录凭据，包括患者编号和密码的匹配性。
     */
    Patient login(String patientNo, String password);

    /**
     根据ID查询患者
     通过患者的主键ID查询患者详细信息。
     */
    Patient findById(Integer id);

    /**
     根据患者编号查询患者
     通过患者编号查询患者详细信息，患者编号具有唯一性。
     */
    Patient findByPatientNo(String patientNo);

    /**
     更新患者信息
     更新患者的基本信息，如姓名、年龄、联系方式等。
     */
    boolean updatePatient(Patient patient);

    /**
     修改密码
     修改患者的登录密码，需要验证旧密码的正确性。
     */
    boolean changePassword(Integer id, String oldPassword, String newPassword);
    
    /**
     查询所有患者
     */
    List<Patient> findAll();

    /**
     分页查询患者
     */
    List<Patient> findByPage(int page, int size);

    /**
     根据姓名模糊查询患者
     */
    List<Patient> findByNameLike(String name);

    /**
     统计患者总数
     */
    int count();

    /**
     生成患者编号
     */
    String generatePatientNo();

    /**
     验证患者编号是否存在
     */
    boolean isPatientNoExists(String patientNo);

    /**
     验证身份证号是否存在
     */
    boolean isIdCardExists(String idCard);
}
