package com.hospital.dao;

import com.hospital.entity.Doctor;
import java.util.List;

/**
 医生DAO接口
 */
public interface Doctor<PERSON><PERSON> extends BaseDao<Doctor> {

    /**
     根据医生工号查询医生
     */
    Doctor findByDoctorNo(String doctorNo);
    
    /**
     根据科室ID查询医生列表
     */
    List<Doctor> findByDeptId(Integer deptId);
    
    /**
     根据姓名模糊查询医生
     */
    List<Doctor> findByNameLike(String name);
    
    /**
     医生登录验证
     */
    Doctor login(String doctorNo, String password);
    
    /**
     修改密码
     */
    int updatePassword(Integer id, String newPassword);
    
    /**
     查询医生列表（包含科室名称）
     */
    List<Doctor> findAllWithDept();
    
    /**
     根据科室ID查询在职医生
     */
    List<Doctor> findActiveDoctorsByDeptId(Integer deptId);

    /**
     统计指定科室的医生数量
     */
    int countByDeptId(Integer deptId);
}
