/* 智慧医疗管理系统 - 专业医疗主题样式 */
/* Smart Hospital Management System - Professional Medical Theme */

/* ===== 医疗专业色彩系统 ===== */
:root {
  /* 主色调 - 医疗蓝色系 */
  --primary-color: #1976D2;        /* 医疗蓝 - 专业、信任 */
  --primary-light: #42A5F5;        /* 浅医疗蓝 */
  --primary-dark: #0D47A1;         /* 深医疗蓝 */
  --primary-50: #E3F2FD;           /* 极浅蓝 */
  --primary-100: #BBDEFB;          /* 很浅蓝 */

  /* 辅助色 - 医疗绿色系 */
  --secondary-color: #00BCD4;      /* 青色 - 清洁、健康 */
  --secondary-light: #4DD0E1;      /* 浅青色 */
  --secondary-dark: #0097A7;       /* 深青色 */
  --secondary-50: #E0F7FA;         /* 极浅青 */
  --secondary-100: #B2EBF2;        /* 很浅青 */

  /* 医疗专用色 */
  --medical-green: #4CAF50;        /* 医疗绿 - 健康、生命 */
  --medical-teal: #009688;         /* 医疗青绿 - 专业、安全 */
  --medical-blue: #2196F3;         /* 医疗蓝 - 科技、现代 */

  /* 背景色系 */
  --background-color: #FAFAFA;     /* 主背景 - 温和灰白 */
  --background-light: #FFFFFF;     /* 浅背景 - 纯白 */
  --background-dark: #F5F5F5;      /* 深背景 - 浅灰 */
  --surface-color: #FFFFFF;        /* 表面色 */
  --surface-elevated: #FFFFFF;     /* 提升表面 */

  /* 文字色系 */
  --text-primary: #212121;         /* 主文字 - 深灰 */
  --text-secondary: #757575;       /* 次要文字 - 中灰 */
  --text-muted: #9E9E9E;          /* 弱化文字 - 浅灰 */
  --text-disabled: #BDBDBD;        /* 禁用文字 */
  --text-on-primary: #FFFFFF;      /* 主色上的文字 */
  --text-on-secondary: #FFFFFF;    /* 辅助色上的文字 */

  /* 边框色系 */
  --border-color: #E0E0E0;         /* 主边框 */
  --border-light: #F5F5F5;         /* 浅边框 */
  --border-dark: #BDBDBD;          /* 深边框 */
  --divider-color: #E0E0E0;        /* 分割线 */

  /* 状态色系 */
  --success-color: #4CAF50;        /* 成功 - 绿色 */
  --success-light: #81C784;        /* 浅成功色 */
  --success-dark: #388E3C;         /* 深成功色 */
  --warning-color: #FF9800;        /* 警告 - 橙色 */
  --warning-light: #FFB74D;        /* 浅警告色 */
  --warning-dark: #F57C00;         /* 深警告色 */
  --error-color: #F44336;          /* 错误 - 红色 */
  --error-light: #E57373;          /* 浅错误色 */
  --error-dark: #D32F2F;           /* 深错误色 */
  --info-color: #2196F3;           /* 信息 - 蓝色 */
  --info-light: #64B5F6;           /* 浅信息色 */
  --info-dark: #1976D2;            /* 深信息色 */

  /* 阴影系统 - 医疗专业阴影 */
  --shadow-1: 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-2: 0 2px 6px rgba(0, 0, 0, 0.08);
  --shadow-3: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-4: 0 8px 24px rgba(0, 0, 0, 0.08);
  --shadow-5: 0 16px 32px rgba(0, 0, 0, 0.08);
  --shadow-medical: 0 2px 8px rgba(25, 118, 210, 0.15);
  --shadow-success: 0 2px 8px rgba(76, 175, 80, 0.15);
  --shadow-warning: 0 2px 8px rgba(255, 152, 0, 0.15);
  --shadow-error: 0 2px 8px rgba(244, 67, 54, 0.15);

  /* 圆角系统 */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-full: 50%;

  /* 间距系统 */
  --spacing-0: 0;
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-8: 32px;
  --spacing-10: 40px;
  --spacing-12: 48px;
  --spacing-16: 64px;
  --spacing-20: 80px;

  /* 字体系统 */
  --font-family-primary: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;

  /* 字体大小 */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  /* 字体粗细 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 过渡动画 */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.25s ease-out;
  --transition-slow: 0.35s ease-out;

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===== 基础重置和全局样式 ===== */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--background-color);
  min-height: 100vh;
  overflow-x: hidden;
}

/* 选择文本样式 */
::selection {
  background-color: var(--primary-100);
  color: var(--primary-dark);
}

::-moz-selection {
  background-color: var(--primary-100);
  color: var(--primary-dark);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-dark);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-dark);
  border-radius: var(--radius-sm);
  transition: background var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* 焦点样式 */
:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== 布局组件 ===== */

/* 容器系统 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  width: 100%;
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-4);
}

/* 网格系统 */
.grid {
  display: grid;
  gap: var(--spacing-4);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* Flexbox工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

/* 卡片组件 */
.card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-2);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-3);
  transform: translateY(-2px);
}

.card-elevated {
  box-shadow: var(--shadow-3);
}

.card-elevated:hover {
  box-shadow: var(--shadow-4);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--border-color);
  background: var(--background-light);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--border-color);
  background: var(--background-light);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--line-height-tight);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: var(--spacing-1) 0 0 0;
  line-height: var(--line-height-normal);
}

.card-text {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

/* ===== 按钮组件系统 ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  user-select: none;
  transition: all var(--transition-normal);
  min-height: 40px;
  gap: var(--spacing-2);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2);
}

.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-3);
}

.btn:active:not(:disabled) {
  transform: translateY(0);
}

/* 按钮变体 */
.btn-primary {
  background: var(--primary-color);
  color: var(--text-on-primary);
  box-shadow: var(--shadow-medical);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-dark);
  box-shadow: var(--shadow-4);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--text-on-secondary);
  box-shadow: var(--shadow-2);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--secondary-dark);
}

.btn-success {
  background: var(--success-color);
  color: white;
  box-shadow: var(--shadow-success);
}

.btn-success:hover:not(:disabled) {
  background: var(--success-dark);
}

.btn-warning {
  background: var(--warning-color);
  color: white;
  box-shadow: var(--shadow-warning);
}

.btn-warning:hover:not(:disabled) {
  background: var(--warning-dark);
}

.btn-error {
  background: var(--error-color);
  color: white;
  box-shadow: var(--shadow-error);
}

.btn-error:hover:not(:disabled) {
  background: var(--error-dark);
}

.btn-info {
  background: var(--info-color);
  color: white;
}

.btn-info:hover:not(:disabled) {
  background: var(--info-dark);
}

/* 轮廓按钮 */
.btn-outline {
  background: transparent;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--text-on-primary);
}

.btn-outline-secondary {
  background: transparent;
  border-color: var(--secondary-color);
  color: var(--secondary-color);
}

.btn-outline-secondary:hover:not(:disabled) {
  background: var(--secondary-color);
  color: var(--text-on-secondary);
}

/* 文本按钮 */
.btn-text {
  background: transparent;
  color: var(--primary-color);
  box-shadow: none;
}

.btn-text:hover:not(:disabled) {
  background: var(--primary-50);
  transform: none;
}

/* 按钮尺寸 */
.btn-xs {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  min-height: 28px;
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  min-height: 32px;
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
  min-height: 48px;
}

.btn-xl {
  padding: var(--spacing-5) var(--spacing-8);
  font-size: var(--font-size-xl);
  min-height: 56px;
}

/* 按钮形状 */
.btn-rounded {
  border-radius: var(--radius-full);
}

.btn-square {
  border-radius: 0;
}

/* 按钮宽度 */
.btn-block {
  width: 100%;
}

.btn-icon {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: var(--radius-full);
}

.btn-icon-sm {
  width: 32px;
  height: 32px;
}

.btn-icon-lg {
  width: 48px;
  height: 48px;
}

/* ===== 表单组件系统 ===== */
.form-group {
  margin-bottom: var(--spacing-5);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.form-label.required::after {
  content: ' *';
  color: var(--error-color);
}

.form-control {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background: var(--surface-color);
  transition: all var(--transition-normal);
  appearance: none;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-control:disabled {
  background: var(--background-dark);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.form-control::placeholder {
  color: var(--text-muted);
}

.form-control.is-invalid {
  border-color: var(--error-color);
}

.form-control.is-invalid:focus {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.form-control.is-valid {
  border-color: var(--success-color);
}

.form-control.is-valid:focus {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* 表单控件尺寸 */
.form-control-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

.form-control-lg {
  padding: var(--spacing-4) var(--spacing-5);
  font-size: var(--font-size-lg);
}

/* 文本域 */
.form-textarea {
  min-height: 100px;
  resize: vertical;
}

/* 选择框 */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--spacing-10);
}

/* 复选框和单选框 */
.form-check {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-3);
}

.form-check-input {
  width: 18px;
  height: 18px;
  margin-right: var(--spacing-3);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--surface-color);
  cursor: pointer;
}

.form-check-input[type="radio"] {
  border-radius: var(--radius-full);
}

.form-check-input:checked {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.form-check-label {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  cursor: pointer;
  user-select: none;
}

/* 表单反馈 */
.form-feedback {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-tight);
}

.form-feedback.invalid {
  color: var(--error-color);
}

.form-feedback.valid {
  color: var(--success-color);
}

/* 输入组 */
.input-group {
  display: flex;
  align-items: stretch;
}

.input-group .form-control {
  border-radius: 0;
  border-right: none;
}

.input-group .form-control:first-child {
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

.input-group .form-control:last-child {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  border-right: 1px solid var(--border-color);
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--background-dark);
  border: 1px solid var(--border-color);
  border-left: none;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.input-group-text:first-child {
  border-left: 1px solid var(--border-color);
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

.input-group-text:last-child {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}

/* ===== 表格组件系统 ===== */
.table-container {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-2);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--surface-color);
  margin: 0;
}

.table th,
.table td {
  padding: var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.table th {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--text-on-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table td {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.table tbody tr {
  transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
  background: var(--primary-50);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* 表格变体 */
.table-striped tbody tr:nth-child(even) {
  background: var(--background-dark);
}

.table-striped tbody tr:nth-child(even):hover {
  background: var(--primary-50);
}

.table-bordered {
  border: 1px solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
  border: 1px solid var(--border-color);
}

.table-sm th,
.table-sm td {
  padding: var(--spacing-2) var(--spacing-3);
}

.table-lg th,
.table-lg td {
  padding: var(--spacing-5) var(--spacing-6);
}

/* 表格响应式 */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* 表格状态 */
.table .text-success {
  color: var(--success-color);
  font-weight: var(--font-weight-medium);
}

.table .text-warning {
  color: var(--warning-color);
  font-weight: var(--font-weight-medium);
}

.table .text-error {
  color: var(--error-color);
  font-weight: var(--font-weight-medium);
}

.table .text-info {
  color: var(--info-color);
  font-weight: var(--font-weight-medium);
}

/* ===== 导航组件系统 ===== */
.navbar {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-on-primary);
  padding: var(--spacing-4) 0;
  box-shadow: var(--shadow-medical);
  position: relative;
  z-index: var(--z-sticky);
}

.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cdefs%3E%3Cpattern id='grid' width='10' height='10' patternUnits='userSpaceOnUse'%3E%3Cpath d='M 10 0 L 0 0 0 10' fill='none' stroke='rgba(255,255,255,0.05)' stroke-width='0.5'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23grid)'/%3E%3C/svg%3E");
  pointer-events: none;
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-on-primary);
  text-decoration: none;
  transition: opacity var(--transition-normal);
}

.navbar-brand:hover {
  opacity: 0.9;
}

.navbar-brand-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-nav-item {
  position: relative;
}

.navbar-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.navbar-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-slow);
}

.navbar-link:hover::before {
  left: 100%;
}

.navbar-link:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-on-primary);
  transform: translateY(-1px);
}

.navbar-link.active {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-on-primary);
}

/* 用户信息区域 */
.navbar-user {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

.navbar-user-avatar {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.navbar-user-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.navbar-user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-on-primary);
  line-height: 1;
}

.navbar-user-role {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.8);
  line-height: 1;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--text-on-primary);
  padding: var(--spacing-8) 0;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cdefs%3E%3Cpattern id='grid' width='10' height='10' patternUnits='userSpaceOnUse'%3E%3Cpath d='M 10 0 L 0 0 0 10' fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='0.5'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23grid)'/%3E%3C/svg%3E");
  pointer-events: none;
}

.page-header-content {
  position: relative;
  z-index: 1;
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-2) 0;
  line-height: var(--line-height-tight);
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: var(--line-height-normal);
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin: var(--spacing-4) 0 0 0;
  padding: 0;
  list-style: none;
  font-size: var(--font-size-sm);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  color: rgba(255, 255, 255, 0.6);
  font-weight: var(--font-weight-normal);
}

.breadcrumb-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.breadcrumb-link:hover {
  color: var(--text-on-primary);
}

.breadcrumb-item.active {
  color: var(--text-on-primary);
  font-weight: var(--font-weight-medium);
}

/* ===== 状态标签组件 ===== */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  line-height: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  border: 1px solid transparent;
}

.badge-primary {
  background: var(--primary-100);
  color: var(--primary-dark);
  border-color: var(--primary-color);
}

.badge-secondary {
  background: var(--secondary-100);
  color: var(--secondary-dark);
  border-color: var(--secondary-color);
}

.badge-success {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-dark);
  border-color: var(--success-color);
}

.badge-warning {
  background: rgba(255, 152, 0, 0.1);
  color: var(--warning-dark);
  border-color: var(--warning-color);
}

.badge-error {
  background: rgba(244, 67, 54, 0.1);
  color: var(--error-dark);
  border-color: var(--error-color);
}

.badge-info {
  background: rgba(33, 150, 243, 0.1);
  color: var(--info-dark);
  border-color: var(--info-color);
}

.badge-light {
  background: var(--background-dark);
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.badge-dark {
  background: var(--text-primary);
  color: var(--text-on-primary);
}

/* 实心标签 */
.badge-solid.badge-primary {
  background: var(--primary-color);
  color: var(--text-on-primary);
}

.badge-solid.badge-secondary {
  background: var(--secondary-color);
  color: var(--text-on-secondary);
}

.badge-solid.badge-success {
  background: var(--success-color);
  color: white;
}

.badge-solid.badge-warning {
  background: var(--warning-color);
  color: white;
}

.badge-solid.badge-error {
  background: var(--error-color);
  color: white;
}

.badge-solid.badge-info {
  background: var(--info-color);
  color: white;
}

/* 标签尺寸 */
.badge-sm {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: 10px;
}

.badge-lg {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
}

/* 带图标的标签 */
.badge-icon {
  gap: var(--spacing-1);
}

.badge-icon::before {
  font-size: 0.8em;
}

/* ===== 警告框组件 ===== */
.alert {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-5);
  border: 1px solid;
  position: relative;
  overflow: hidden;
}

.alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: currentColor;
}

.alert-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-1) 0;
  line-height: var(--line-height-tight);
}

.alert-message {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  margin: 0;
}

.alert-close {
  flex-shrink: 0;
  background: none;
  border: none;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background var(--transition-normal);
  color: currentColor;
  opacity: 0.7;
}

.alert-close:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

/* 警告框变体 */
.alert-success {
  background: rgba(76, 175, 80, 0.05);
  border-color: var(--success-color);
  color: var(--success-dark);
}

.alert-success .alert-icon {
  background: var(--success-color);
  color: white;
}

.alert-warning {
  background: rgba(255, 152, 0, 0.05);
  border-color: var(--warning-color);
  color: var(--warning-dark);
}

.alert-warning .alert-icon {
  background: var(--warning-color);
  color: white;
}

.alert-error {
  background: rgba(244, 67, 54, 0.05);
  border-color: var(--error-color);
  color: var(--error-dark);
}

.alert-error .alert-icon {
  background: var(--error-color);
  color: white;
}

.alert-info {
  background: rgba(33, 150, 243, 0.05);
  border-color: var(--info-color);
  color: var(--info-dark);
}

.alert-info .alert-icon {
  background: var(--info-color);
  color: white;
}

.alert-primary {
  background: var(--primary-50);
  border-color: var(--primary-color);
  color: var(--primary-dark);
}

.alert-primary .alert-icon {
  background: var(--primary-color);
  color: white;
}

/* 简单警告框（无图标） */
.alert-simple {
  padding: var(--spacing-3) var(--spacing-4);
}

.alert-simple::before {
  display: none;
}

.alert-simple .alert-icon {
  display: none;
}

/* ===== 工具类系统 ===== */

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 文本颜色 */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }
.text-muted { color: var(--text-muted); }
.text-disabled { color: var(--text-disabled); }
.text-white { color: white; }
.text-dark { color: var(--text-primary); }

/* 文本大小 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

/* 文本粗细 */
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* 背景颜色 */
.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-info { background-color: var(--info-color); }
.bg-light { background-color: var(--background-light); }
.bg-dark { background-color: var(--text-primary); }
.bg-surface { background-color: var(--surface-color); }
.bg-transparent { background-color: transparent; }

/* 边距 - Margin */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-5 { margin: var(--spacing-5); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }
.m-10 { margin: var(--spacing-10); }
.m-12 { margin: var(--spacing-12); }

.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: var(--spacing-1); margin-right: var(--spacing-1); }
.mx-2 { margin-left: var(--spacing-2); margin-right: var(--spacing-2); }
.mx-3 { margin-left: var(--spacing-3); margin-right: var(--spacing-3); }
.mx-4 { margin-left: var(--spacing-4); margin-right: var(--spacing-4); }
.mx-auto { margin-left: auto; margin-right: auto; }

.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: var(--spacing-1); margin-bottom: var(--spacing-1); }
.my-2 { margin-top: var(--spacing-2); margin-bottom: var(--spacing-2); }
.my-3 { margin-top: var(--spacing-3); margin-bottom: var(--spacing-3); }
.my-4 { margin-top: var(--spacing-4); margin-bottom: var(--spacing-4); }
.my-5 { margin-top: var(--spacing-5); margin-bottom: var(--spacing-5); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-5 { margin-top: var(--spacing-5); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-5 { margin-bottom: var(--spacing-5); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-1); }
.ml-2 { margin-left: var(--spacing-2); }
.ml-3 { margin-left: var(--spacing-3); }
.ml-4 { margin-left: var(--spacing-4); }
.ml-auto { margin-left: auto; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-1); }
.mr-2 { margin-right: var(--spacing-2); }
.mr-3 { margin-right: var(--spacing-3); }
.mr-4 { margin-right: var(--spacing-4); }
.mr-auto { margin-right: auto; }

/* 内边距 - Padding */
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--spacing-1); padding-right: var(--spacing-1); }
.px-2 { padding-left: var(--spacing-2); padding-right: var(--spacing-2); }
.px-3 { padding-left: var(--spacing-3); padding-right: var(--spacing-3); }
.px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }
.px-5 { padding-left: var(--spacing-5); padding-right: var(--spacing-5); }
.px-6 { padding-left: var(--spacing-6); padding-right: var(--spacing-6); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--spacing-1); padding-bottom: var(--spacing-1); }
.py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
.py-3 { padding-top: var(--spacing-3); padding-bottom: var(--spacing-3); }
.py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }
.py-5 { padding-top: var(--spacing-5); padding-bottom: var(--spacing-5); }
.py-6 { padding-top: var(--spacing-6); padding-bottom: var(--spacing-6); }

/* 显示属性 */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.hidden { display: none; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.inline-grid { display: inline-grid; }

/* 位置属性 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 溢出 */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* 圆角 */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 阴影 */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-1); }
.shadow { box-shadow: var(--shadow-2); }
.shadow-md { box-shadow: var(--shadow-3); }
.shadow-lg { box-shadow: var(--shadow-4); }
.shadow-xl { box-shadow: var(--shadow-5); }

/* 宽度 */
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-1\/4 { width: 25%; }
.w-3\/4 { width: 75%; }

/* 高度 */
.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }

/* 最大宽度 */
.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-full { max-width: 100%; }
.max-w-none { max-width: none; }

/* ===== 医疗专用组件 ===== */

/* 医疗卡片 */
.medical-card {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-medical);
  border: 1px solid var(--primary-100);
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
}

.medical-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.medical-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-4);
}

.medical-card-header {
  padding: var(--spacing-6);
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
  border-bottom: 1px solid var(--border-color);
}

.medical-card-body {
  padding: var(--spacing-6);
}

.medical-card-footer {
  padding: var(--spacing-4) var(--spacing-6);
  background: var(--background-light);
  border-top: 1px solid var(--border-color);
}

/* 统计卡片 */
.stat-card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  text-align: center;
  box-shadow: var(--shadow-2);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-3);
}

.stat-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-4);
  font-size: var(--font-size-2xl);
  color: white;
  box-shadow: var(--shadow-medical);
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 功能菜单卡片 */
.menu-card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-8);
  text-align: center;
  box-shadow: var(--shadow-2);
  border: 1px solid var(--border-color);
  text-decoration: none;
  color: inherit;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: block;
}

.menu-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.menu-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-4);
  text-decoration: none;
  color: inherit;
}

.menu-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-4);
  font-size: var(--font-size-3xl);
  color: white;
  box-shadow: var(--shadow-medical);
  transition: all var(--transition-normal);
}

.menu-card:hover .menu-icon {
  transform: scale(1.1);
}

.menu-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-2) 0;
  line-height: var(--line-height-tight);
}

.menu-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* 医疗状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: currentColor;
}

.status-available {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-dark);
}

.status-busy {
  background: rgba(255, 152, 0, 0.1);
  color: var(--warning-dark);
}

.status-offline {
  background: rgba(158, 158, 158, 0.1);
  color: var(--text-muted);
}

.status-emergency {
  background: rgba(244, 67, 54, 0.1);
  color: var(--error-dark);
}

/* 医疗进度条 */
.medical-progress {
  background: var(--background-dark);
  border-radius: var(--radius-full);
  height: 8px;
  overflow: hidden;
  position: relative;
}

.medical-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
  position: relative;
}

.medical-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ===== 响应式设计系统 ===== */

/* 移动设备优先的响应式断点 */
@media (max-width: 640px) {
  /* 小屏幕手机 */
  .container {
    padding: 0 var(--spacing-3);
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: 1fr;
  }

  .navbar-content {
    flex-direction: column;
    gap: var(--spacing-3);
    text-align: center;
  }

  .navbar-user {
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
  }

  .page-header {
    padding: var(--spacing-6) 0;
  }

  .page-title {
    font-size: var(--font-size-2xl);
  }

  .card-body,
  .card-header,
  .card-footer {
    padding: var(--spacing-4);
  }

  .medical-card-body,
  .medical-card-header {
    padding: var(--spacing-4);
  }

  .menu-card {
    padding: var(--spacing-6);
  }

  .menu-icon {
    width: 64px;
    height: 64px;
    font-size: var(--font-size-2xl);
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: var(--font-size-xl);
  }

  .stat-number {
    font-size: var(--font-size-2xl);
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .btn-group {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .form-row {
    flex-direction: column;
  }

  .modal {
    margin: var(--spacing-3);
    width: calc(100% - 2 * var(--spacing-3));
    max-height: calc(100vh - 2 * var(--spacing-3));
  }

  .alert {
    flex-direction: column;
    text-align: center;
  }

  .breadcrumb {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  /* 平板设备 */
  .container {
    padding: 0 var(--spacing-4);
  }

  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(2, 1fr);
  }

  .navbar-content {
    gap: var(--spacing-4);
  }

  .page-header {
    padding: var(--spacing-8) 0;
  }

  .menu-card {
    padding: var(--spacing-6);
  }

  .btn-group {
    flex-wrap: wrap;
    gap: var(--spacing-2);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* 小型桌面设备 */
  .container {
    padding: 0 var(--spacing-5);
  }

  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1025px) {
  /* 大型桌面设备 */
  .container {
    padding: 0 var(--spacing-6);
  }
}

/* 打印样式 */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  .navbar,
  .btn,
  .alert-close,
  .modal-close {
    display: none !important;
  }

  .page-header {
    background: none !important;
    color: black !important;
    border-bottom: 2px solid black;
  }

  .card,
  .medical-card,
  .table-container {
    border: 1px solid black !important;
    box-shadow: none !important;
  }

  .table th {
    background: #f0f0f0 !important;
    color: black !important;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  .page-break {
    page-break-before: always;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-muted: #333333;
    --background-color: #ffffff;
  }

  .btn {
    border: 2px solid currentColor;
  }

  .card,
  .medical-card {
    border: 2px solid var(--border-color);
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 深色模式支持（预留） */
@media (prefers-color-scheme: dark) {
  /* 深色模式变量可以在这里定义 */
  /* 目前保持医疗主题的浅色方案 */
}

/* ===== 动画和过渡效果 ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 动画工具类 */
.animate-fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.animate-slide-in-up {
  animation: slideInUp var(--transition-normal) ease-out;
}

.animate-slide-in-down {
  animation: slideInDown var(--transition-normal) ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 加载状态 */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--primary-color);
  border-top-color: transparent;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

/* 医疗主题特殊效果 */
.medical-glow {
  box-shadow: 0 0 20px rgba(25, 118, 210, 0.3);
}

.medical-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* 无障碍支持 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 结束标记 */
/* 智慧医疗管理系统样式文件结束 */
