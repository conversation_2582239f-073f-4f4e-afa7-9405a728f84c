<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    String doctorId = request.getParameter("id");
    if (doctorId == null) {
        doctorId = "1";
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>医生详情 - 医院管理系统</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }
        .doctor-info {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .doctor-info h3 {
            margin: 0 0 10px 0;
            font-size: 1.8em;
        }
        .doctor-info p {
            margin: 5px 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        .info-section h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e1e5e9;
        }
        .info-item:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }
        .info-label {
            font-weight: 500;
            color: #666;
            min-width: 100px;
        }
        .info-value {
            color: #333;
            flex: 1;
            text-align: right;
            font-weight: 500;
        }
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-right: 15px;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background: #e0a800;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .speciality-section {
            grid-column: 1 / -1;
            background: #fff3e0;
        }
        .speciality-text {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            line-height: 1.6;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/doctors.jsp">返回医生管理</a>
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card">
            <h2>医生详情</h2>

            <div id="loadingDiv" class="loading">正在加载医生信息...</div>

            <div id="doctorContent" style="display: none">
                <div class="doctor-info">
                    <h3 id="doctorName">-</h3>
                    <p id="doctorBasicInfo">-</p>
                    <p id="doctorNo">工号：</p>
                    <p id="createTime">入职时间：</p>
                </div>

                <div class="info-grid">
                    <div class="info-section">
                        <h4>基本信息</h4>
                        <div class="info-item">
                            <span class="info-label">姓名</span>
                            <span class="info-value" id="name">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">性别</span>
                            <span class="info-value" id="gender">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">年龄</span>
                            <span class="info-value" id="age">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">手机号码</span>
                            <span class="info-value" id="phone">-</span>
                        </div>
                    </div>

                    <div class="info-section">
                        <h4>工作信息</h4>
                        <div class="info-item">
                            <span class="info-label">工号</span>
                            <span class="info-value" id="doctorNumber">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">所属科室</span>
                            <span class="info-value" id="deptName">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">职称</span>
                            <span class="info-value" id="title">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">工作状态</span>
                            <span class="info-value" id="status">-</span>
                        </div>
                    </div>

                    <div class="info-section">
                        <h4>联系信息</h4>
                        <div class="info-item">
                            <span class="info-label">电子邮箱</span>
                            <span class="info-value" id="email">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">入职时间</span>
                            <span class="info-value" id="joinTime">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">最后更新</span>
                            <span class="info-value" id="updateTime">-</span>
                        </div>
                    </div>

                    <div class="info-section">
                        <h4>系统信息</h4>
                        <div class="info-item">
                            <span class="info-label">医生ID</span>
                            <span class="info-value" id="doctorId">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">科室ID</span>
                            <span class="info-value" id="deptId">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">账户状态</span>
                            <span class="info-value" id="accountStatus">-</span>
                        </div>
                    </div>

                    <div class="info-section speciality-section" id="specialitySection" style="display: none">
                        <h4>专业特长</h4>
                        <div class="speciality-text" id="specialityText">-</div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px">
                    <a href="${pageContext.request.contextPath}/admin/edit-doctor.jsp?id=<%= doctorId %>" class="btn btn-warning">编辑医生信息</a>
                    <a href="${pageContext.request.contextPath}/admin/doctors.jsp" class="btn btn-secondary">返回医生列表</a>
                </div>
            </div>

            <div id="errorDiv" style="display: none; text-align: center; padding: 40px; color: #dc3545;">
                <h3>加载失败</h3>
                <p>无法加载医生信息，请检查医生ID是否正确。</p>
                <a href="${pageContext.request.contextPath}/admin/doctors.jsp" class="btn btn-secondary">返回医生列表</a>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const doctorId = "<%= doctorId %>";
            loadDoctorData(doctorId);
        });

        function loadDoctorData(doctorId) {
            // 调用后端API获取真实医生数据
            fetch("${pageContext.request.contextPath}/DoctorDetailServlet?id=" + doctorId)
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        displayDoctorData(data);
                    } else {
                        console.error("获取医生信息失败:", data.error);
                        showError();
                    }
                })
                .catch((error) => {
                    console.error("请求失败:", error);
                    showError();
                });
        }

        function displayDoctorData(doctor) {
            document.getElementById("loadingDiv").style.display = "none";
            document.getElementById("doctorContent").style.display = "block";

            // 更新医生信息
            document.getElementById("doctorName").textContent = doctor.name + " 医生";
            document.getElementById("doctorBasicInfo").textContent =
                doctor.gender + " | " + (doctor.age ? doctor.age + "岁" : "年龄未知") + " | " + doctor.title;
            document.getElementById("doctorNo").textContent = "工号：" + doctor.doctorNo;
            document.getElementById("createTime").textContent = "入职时间：" + doctor.createTime;

            // 更新详细信息
            document.getElementById("name").textContent = doctor.name;
            document.getElementById("gender").textContent = doctor.gender;
            document.getElementById("age").textContent = doctor.age ? doctor.age + "岁" : "未填写";
            document.getElementById("phone").textContent = doctor.phone || "未填写";
            document.getElementById("doctorNumber").textContent = doctor.doctorNo;
            document.getElementById("deptName").textContent = doctor.deptName || "未分配";
            document.getElementById("title").textContent = doctor.title || "未设置";
            document.getElementById("status").textContent = doctor.status;
            document.getElementById("email").textContent = doctor.email || "未填写";
            document.getElementById("joinTime").textContent = doctor.createTime;
            document.getElementById("updateTime").textContent = doctor.updateTime || "未更新";
            document.getElementById("doctorId").textContent = doctor.id;
            document.getElementById("deptId").textContent = doctor.deptId || "未分配";
            document.getElementById("accountStatus").textContent = doctor.status;

            // 显示专业特长
            if (doctor.speciality && doctor.speciality.trim()) {
                document.getElementById("specialitySection").style.display = "block";
                document.getElementById("specialityText").textContent = doctor.speciality;
            }
        }

        function showError() {
            document.getElementById("loadingDiv").style.display = "none";
            document.getElementById("errorDiv").style.display = "block";
        }
    </script>
</body>
</html>
