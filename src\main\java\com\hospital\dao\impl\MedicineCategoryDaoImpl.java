package com.hospital.dao.impl;

import com.hospital.dao.MedicineCategoryDao;
import com.hospital.entity.MedicineCategory;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 药品分类DAO实现类
 */
public class MedicineCategoryDaoImpl implements MedicineCategoryDao {
    
    @Override
    public int insert(MedicineCategory category) {
        String sql = "INSERT INTO medicine_category (category_name, category_type, description) VALUES (?, ?, ?)";
        return JdbcTemplate.update(sql, category.getCategoryName(), category.getCategoryType(), category.getDescription());
    }
    
    @Override
    public int update(MedicineCategory category) {
        String sql = "UPDATE medicine_category SET category_name = ?, category_type = ?, description = ? WHERE id = ?";
        return JdbcTemplate.update(sql, category.getCategoryName(), category.getCategoryType(), 
                                 category.getDescription(), category.getId());
    }
    
    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM medicine_category WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }
    
    @Override
    public MedicineCategory findById(Integer id) {
        String sql = "SELECT * FROM medicine_category WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToMedicineCategory, id);
    }
    
    @Override
    public List<MedicineCategory> findAll() {
        String sql = "SELECT * FROM medicine_category ORDER BY category_type, category_name";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicineCategory);
    }
    
    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM medicine_category";
        return JdbcTemplate.queryForInt(sql);
    }
    
    @Override
    public List<MedicineCategory> findByPage(int offset, int size) {
        String sql = "SELECT * FROM medicine_category ORDER BY category_type, category_name LIMIT ?, ?";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicineCategory, offset, size);
    }
    
    @Override
    public MedicineCategory findByCategoryName(String categoryName) {
        String sql = "SELECT * FROM medicine_category WHERE category_name = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToMedicineCategory, categoryName);
    }
    
    @Override
    public List<MedicineCategory> findByCategoryType(String categoryType) {
        String sql = "SELECT * FROM medicine_category WHERE category_type = ? ORDER BY category_name";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicineCategory, categoryType);
    }
    
    @Override
    public boolean existsByCategoryName(String categoryName) {
        String sql = "SELECT COUNT(*) FROM medicine_category WHERE category_name = ?";
        return JdbcTemplate.queryForInt(sql, categoryName) > 0;
    }
    
    /**
     * 将ResultSet映射为MedicineCategory对象
     */
    private MedicineCategory mapRowToMedicineCategory(ResultSet rs) throws SQLException {
        MedicineCategory category = new MedicineCategory();
        category.setId(rs.getInt("id"));
        category.setCategoryName(rs.getString("category_name"));
        category.setCategoryType(rs.getString("category_type"));
        category.setDescription(rs.getString("description"));
        category.setCreateTime(rs.getTimestamp("create_time"));
        category.setUpdateTime(rs.getTimestamp("update_time"));
        return category;
    }
}
