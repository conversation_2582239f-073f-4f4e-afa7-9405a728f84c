<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>简单编辑测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🔧 简单编辑测试页面</h1>
            
            <div class="success">
                ✅ 基础JSP页面访问成功！
            </div>
            
            <div class="info">
                <h3>📋 基础信息</h3>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
                <p><strong>请求URI:</strong> <%= request.getRequestURI() %></p>
                <p><strong>Context Path:</strong> <%= request.getContextPath() %></p>
                <p><strong>参数ID:</strong> <%= request.getParameter("id") %></p>
            </div>
            
            <%
                // 测试管理员登录状态
                Object adminObj = session.getAttribute("admin");
                if (adminObj == null) {
            %>
                <div class="error">
                    ❌ 管理员未登录，请先登录系统
                    <br><a href="<%= request.getContextPath() %>/login.jsp">点击登录</a>
                </div>
            <%
                } else {
            %>
                <div class="success">
                    ✅ 管理员已登录
                </div>
                
                <%
                    // 测试参数获取
                    String idStr = request.getParameter("id");
                    if (idStr == null || idStr.trim().isEmpty()) {
                %>
                    <div class="error">
                        ❌ 缺少科室ID参数
                        <br>请在URL中添加 ?id=1 参数
                    </div>
                <%
                    } else {
                        try {
                            Integer deptId = Integer.parseInt(idStr);
                %>
                    <div class="success">
                        ✅ 科室ID参数正常: <%= deptId %>
                    </div>
                    
                    <%
                            // 测试服务获取
                            try {
                    %>
                        <div class="info">
                            <h3>🔧 服务测试</h3>
                            <p>正在测试JspServiceUtil...</p>
                        </div>
                        
                        <%@ page import="com.hospital.util.JspServiceUtil" %>
                        <%@ page import="com.hospital.service.DepartmentService" %>
                        <%@ page import="com.hospital.entity.Department" %>
                        
                        <%
                            DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
                        %>
                        
                        <div class="success">
                            ✅ DepartmentService获取成功
                        </div>
                        
                        <%
                            Department department = departmentService.findById(deptId);
                            if (department == null) {
                        %>
                            <div class="error">
                                ❌ 科室不存在 (ID: <%= deptId %>)
                                <br>请检查数据库中是否有此科室
                            </div>
                        <%
                            } else {
                        %>
                            <div class="success">
                                ✅ 科室信息获取成功
                            </div>
                            
                            <div class="info">
                                <h3>🏥 科室信息</h3>
                                <p><strong>科室ID:</strong> <%= department.getId() %></p>
                                <p><strong>科室名称:</strong> <%= department.getDeptName() %></p>
                                <p><strong>科室位置:</strong> <%= department.getLocation() != null ? department.getLocation() : "未设置" %></p>
                                <p><strong>联系电话:</strong> <%= department.getPhone() != null ? department.getPhone() : "未设置" %></p>
                                <p><strong>科室介绍:</strong> <%= department.getDescription() != null ? department.getDescription() : "无介绍" %></p>
                            </div>
                            
                            <div class="success">
                                🎉 所有测试通过！编辑科室功能应该正常工作。
                                <br><br>
                                <a href="<%= request.getContextPath() %>/admin/edit-department.jsp?id=<%= deptId %>" 
                                   style="display: inline-block; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px;">
                                   访问完整编辑页面
                                </a>
                            </div>
                        <%
                            }
                        %>
                        
                    <%
                            } catch (Exception e) {
                    %>
                        <div class="error">
                            ❌ 服务获取失败: <%= e.getMessage() %>
                            <br>错误详情: <%= e.toString() %>
                        </div>
                    <%
                            }
                        } catch (NumberFormatException e) {
                    %>
                        <div class="error">
                            ❌ 科室ID格式错误: <%= idStr %>
                            <br>请提供有效的数字ID
                        </div>
                    <%
                        }
                    }
                }
            %>
            
            <div class="info">
                <h3>🔗 测试链接</h3>
                <p><a href="<%= request.getContextPath() %>/admin/simple-edit-test.jsp?id=1">测试ID=1</a></p>
                <p><a href="<%= request.getContextPath() %>/admin/simple-edit-test.jsp?id=2">测试ID=2</a></p>
                <p><a href="<%= request.getContextPath() %>/admin/simple-edit-test.jsp?id=7">测试ID=7</a></p>
                <p><a href="<%= request.getContextPath() %>/admin/departments.jsp">返回科室管理</a></p>
            </div>
        </div>
    </div>
</body>
</html>
