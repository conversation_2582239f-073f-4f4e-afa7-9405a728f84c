package com.hospital.entity;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.Date;

/**
 挂号实体类
 该类用于表示医院管理系统中的挂号信息，是连接患者、医生和科室的核心业务实体。
 记录了患者的挂号详情，包括挂号时间、费用、症状描述等信息。
 业务流程：
 1. 患者选择科室和医生进行挂号
 2. 系统生成挂号单号和挂号记录
 3. 患者按时就诊，医生进行诊疗
 4. 挂号状态随着就诊流程更新
 数据库对应表：registration
 */
public class Registration {

    private Integer id;
    private String regNo;
    private Integer patientId;
    private Integer doctorId;
    private Integer deptId;
    private Date regDate;
    private Time regTime;
    private BigDecimal regFee;
    private String status;
    private String symptoms;
    private Date createTime;
    private Date updateTime;

    private String patientName;

    private String doctorName;
    private String deptName;
    private String patientPhone;
    public Registration() {}

    public Registration(String regNo, Integer patientId, Integer doctorId,
                       Integer deptId, Date regDate, Time regTime,
                       BigDecimal regFee, String symptoms) {
        this.regNo = regNo;
        this.patientId = patientId;
        this.doctorId = doctorId;
        this.deptId = deptId;
        this.regDate = regDate;
        this.regTime = regTime;
        this.regFee = regFee;
        this.symptoms = symptoms;
        this.status = "已挂号"; // 默认状态为已挂号
    }

    // ==================== Getter和Setter方法 ====================

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRegNo() {
        return regNo;
    }

    public void setRegNo(String regNo) {
        this.regNo = regNo;
    }

    public Integer getPatientId() {
        return patientId;
    }

    public void setPatientId(Integer patientId) {
        this.patientId = patientId;
    }

    public Integer getDoctorId() {
        return doctorId;
    }

    public void setDoctorId(Integer doctorId) {
        this.doctorId = doctorId;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Date getRegDate() {
        return regDate;
    }

    public void setRegDate(Date regDate) {
        this.regDate = regDate;
    }

    public Time getRegTime() {
        return regTime;
    }

    public void setRegTime(Time regTime) {
        this.regTime = regTime;
    }

    public BigDecimal getRegFee() {
        return regFee;
    }

    public void setRegFee(BigDecimal regFee) {
        this.regFee = regFee;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSymptoms() {
        return symptoms;
    }

    public void setSymptoms(String symptoms) {
        this.symptoms = symptoms;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getPatientPhone() {
        return patientPhone;
    }

    public void setPatientPhone(String patientPhone) {
        this.patientPhone = patientPhone;
    }

    /**
     重写toString方法
     用于调试和日志输出，返回挂号对象的字符串表示
     包含挂号的所有关键信息，便于问题排查和数据追踪
     */
    @Override
    public String toString() {
        return "Registration{" +
                "id=" + id +
                ", regNo='" + regNo + '\'' +
                ", patientId=" + patientId +
                ", doctorId=" + doctorId +
                ", deptId=" + deptId +
                ", regDate=" + regDate +
                ", regTime=" + regTime +
                ", regFee=" + regFee +
                ", status='" + status + '\'' +
                ", symptoms='" + symptoms + '\'' +
                ", patientName='" + patientName + '\'' +
                ", doctorName='" + doctorName + '\'' +
                ", deptName='" + deptName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
