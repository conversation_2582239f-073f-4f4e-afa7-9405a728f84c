<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%
    Doctor doctor = (Doctor) session.getAttribute("doctor");
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>患者挂号测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .nav-links a { display: inline-block; margin-right: 15px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
        .nav-links a:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🏥 患者挂号功能测试</h1>
            
            <div class="success">
                <h2>✅ 测试结果：患者挂号页面访问正常！</h2>
                <p>医生模块的Spring依赖已完全移除，现在使用JspServiceUtil获取服务。</p>
            </div>
            
            <div class="info">
                <h3>📋 医生信息</h3>
                <p><strong>医生姓名:</strong> <%= doctor.getName() %></p>
                <p><strong>医生编号:</strong> <%= doctor.getDoctorNo() %></p>
                <p><strong>所属科室:</strong> <%= doctor.getDeptName() != null ? doctor.getDeptName() : "未知科室" %></p>
                <p><strong>职称:</strong> <%= doctor.getTitle() != null ? doctor.getTitle() : "医师" %></p>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
            </div>
            
            <div class="info">
                <h3>🔧 修复内容</h3>
                <ul>
                    <li>✅ registrations.jsp - 患者挂号管理页面</li>
                    <li>✅ add-medical-record.jsp - 添加就诊记录页面</li>
                    <li>✅ edit-medical-record.jsp - 编辑就诊记录页面</li>
                    <li>✅ 所有页面已移除Spring依赖</li>
                    <li>✅ 使用JspServiceUtil获取服务</li>
                </ul>
            </div>
            
            <div class="nav-links">
                <h3>🚀 功能测试</h3>
                <a href="${pageContext.request.contextPath}/doctor/registrations.jsp">患者挂号管理</a>
                <a href="${pageContext.request.contextPath}/doctor/medical-records.jsp">就诊记录管理</a>
                <a href="${pageContext.request.contextPath}/doctor/index.jsp">返回医生首页</a>
            </div>
            
            <div class="info">
                <h3>💡 使用说明</h3>
                <p>现在您可以：</p>
                <ol>
                    <li><strong>查看患者挂号</strong> - 点击"患者挂号管理"查看所有挂号记录</li>
                    <li><strong>开始就诊</strong> - 为已挂号的患者添加就诊记录</li>
                    <li><strong>管理记录</strong> - 查看和编辑就诊记录</li>
                    <li><strong>更新状态</strong> - 标记挂号状态为已就诊</li>
                </ol>
            </div>
            
            <div class="info">
                <h3>🔧 技术信息</h3>
                <ul>
                    <li><strong>服务获取方式</strong>: JspServiceUtil.getRegistrationService(application)</li>
                    <li><strong>Spring依赖</strong>: 已完全移除</li>
                    <li><strong>原生IoC容器</strong>: BeanFactory + ApplicationContext</li>
                    <li><strong>数据库连接</strong>: Druid连接池</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
