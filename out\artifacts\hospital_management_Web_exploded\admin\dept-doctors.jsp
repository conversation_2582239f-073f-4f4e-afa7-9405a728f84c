<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%@ page import="com.hospital.entity.Department" %>
<%@ page import="com.hospital.service.DoctorService" %>
<%@ page import="com.hospital.service.DepartmentService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    String deptIdStr = request.getParameter("deptId");
    if (deptIdStr == null || deptIdStr.trim().isEmpty()) {
        response.sendRedirect(request.getContextPath() + "/admin/departments.jsp?error=科室ID不能为空");
        return;
    }
    
    Integer deptId = null;
    try {
        deptId = Integer.parseInt(deptIdStr);
    } catch (NumberFormatException e) {
        response.sendRedirect(request.getContextPath() + "/admin/departments.jsp?error=无效的科室ID");
        return;
    }
    
    DoctorService doctorService = JspServiceUtil.getDoctorService(application);
    DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
    
    Department department = departmentService.findById(deptId);
    if (department == null) {
        response.sendRedirect(request.getContextPath() + "/admin/departments.jsp?error=科室不存在");
        return;
    }
    
    List<Doctor> doctors = doctorService.findActiveDoctorsByDeptId(deptId);
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科室医生 - 医院管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .dept-header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .dept-header h3 {
            margin: 0 0 10px 0;
            font-size: 1.5em;
        }
        .dept-header p {
            margin: 0;
            opacity: 0.9;
        }
        .doctors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        .doctor-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .doctor-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }
        .doctor-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .doctor-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5em;
            font-weight: bold;
            margin-right: 15px;
        }
        .doctor-info h4 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 1.2em;
        }
        .doctor-info p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }
        .doctor-details {
            margin-bottom: 15px;
        }
        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        .detail-label {
            color: #666;
            font-weight: 500;
        }
        .detail-value {
            color: #333;
            font-weight: 500;
        }
        .doctor-actions {
            display: flex;
            gap: 8px;
            justify-content: center;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            font-size: 0.9em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background: #138496;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background: #e0a800;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        .empty-state h3 {
            margin-bottom: 10px;
            color: #999;
        }
        .stats-bar {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .stats-bar h3 {
            margin: 0 0 10px 0;
            font-size: 2em;
        }
        .stats-bar p {
            margin: 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/view-department.jsp?id=<%= deptId %>">返回科室详情</a>
                <a href="${pageContext.request.contextPath}/admin/departments.jsp">科室管理</a>
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>科室医生</h2>
            
            <div class="dept-header">
                <h3>🏢 <%= department.getDeptName() %></h3>
                <p>科室位置：<%= department.getLocation() != null ? department.getLocation() : "未设置" %> | 联系电话：<%= department.getPhone() != null ? department.getPhone() : "未设置" %></p>
            </div>
            
            <div class="stats-bar">
                <h3><%= doctors.size() %></h3>
                <p>在职医生总数</p>
            </div>
            
            <% if (doctors != null && !doctors.isEmpty()) { %>
                <div class="doctors-grid">
                    <% for (Doctor doctor : doctors) { %>
                        <div class="doctor-card">
                            <div class="doctor-header">
                                <div class="doctor-avatar">
                                    <%= doctor.getName() != null && !doctor.getName().isEmpty() ? doctor.getName().substring(0, 1) : "?" %>
                                </div>
                                <div class="doctor-info">
                                    <h4><%= doctor.getName() != null ? doctor.getName() : "未知" %></h4>
                                    <p><%= doctor.getTitle() != null ? doctor.getTitle() : "医师" %> | <%= doctor.getGender() != null ? doctor.getGender() : "未知" %></p>
                                </div>
                            </div>
                            
                            <div class="doctor-details">
                                <div class="detail-item">
                                    <span class="detail-label">工号：</span>
                                    <span class="detail-value"><%= doctor.getDoctorNo() != null ? doctor.getDoctorNo() : "未设置" %></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">年龄：</span>
                                    <span class="detail-value"><%= doctor.getAge() != null ? doctor.getAge() + "岁" : "未填写" %></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">手机：</span>
                                    <span class="detail-value"><%= doctor.getPhone() != null ? doctor.getPhone() : "未设置" %></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">状态：</span>
                                    <span class="detail-value"><%= doctor.getStatus() != null ? doctor.getStatus() : "未知" %></span>
                                </div>
                            </div>
                            
                            <div class="doctor-actions">
                                <a href="${pageContext.request.contextPath}/admin/view-doctor.jsp?id=<%= doctor.getId() %>" class="btn btn-info">查看详情</a>
                                <a href="${pageContext.request.contextPath}/admin/edit-doctor.jsp?id=<%= doctor.getId() %>" class="btn btn-warning">编辑</a>
                            </div>
                        </div>
                    <% } %>
                </div>
            <% } else { %>
                <div class="empty-state">
                    <h3>👨‍⚕️ 暂无医生</h3>
                    <p>该科室目前没有在职医生。</p>
                    <a href="${pageContext.request.contextPath}/admin/add-doctor.jsp?deptId=<%= deptId %>" class="btn btn-info">添加医生</a>
                </div>
            <% } %>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="${pageContext.request.contextPath}/admin/add-doctor.jsp?deptId=<%= deptId %>" class="btn btn-info">添加医生</a>
                <a href="${pageContext.request.contextPath}/admin/view-department.jsp?id=<%= deptId %>" class="btn btn-secondary">返回科室详情</a>
            </div>
        </div>
    </div>
</body>
</html>
