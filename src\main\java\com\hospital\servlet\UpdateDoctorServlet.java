package com.hospital.servlet;

import com.hospital.entity.Admin;
import com.hospital.entity.Doctor;
import com.hospital.service.DoctorService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * 更新医生信息控制器
 */
@WebServlet("/UpdateDoctorServlet")
public class UpdateDoctorServlet extends HttpServlet {
    
    private DoctorService doctorService;
    
    @Override
    public void init() throws ServletException {
        BeanFactory beanFactory = BeanFactory.getInstance();
        doctorService = beanFactory.getApplicationContext().getBean(DoctorService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        
        HttpSession session = request.getSession();
        Admin admin = (Admin) session.getAttribute("admin");
        
        if (admin == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        // 获取表单参数
        String idStr = request.getParameter("id");
        String name = request.getParameter("name");
        String gender = request.getParameter("gender");
        String ageStr = request.getParameter("age");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String deptIdStr = request.getParameter("deptId");
        String title = request.getParameter("title");
        String status = request.getParameter("status");
        String speciality = request.getParameter("speciality");
        
        // 参数验证
        if (idStr == null || idStr.trim().isEmpty() ||
            name == null || name.trim().isEmpty() ||
            gender == null || gender.trim().isEmpty() ||
            phone == null || phone.trim().isEmpty() ||
            deptIdStr == null || deptIdStr.trim().isEmpty() ||
            status == null || status.trim().isEmpty()) {
            
            response.sendRedirect(request.getContextPath() + "/admin/edit-doctor.jsp?id=" + idStr + "&error=" + URLEncoder.encode("请填写所有必填项", "UTF-8"));
            return;
        }
        
        try {
            Integer doctorId = Integer.parseInt(idStr);
            Integer deptId = Integer.parseInt(deptIdStr);
            
            // 获取现有医生信息
            Doctor doctor = doctorService.findById(doctorId);
            if (doctor == null) {
                response.sendRedirect(request.getContextPath() + "/admin/doctors.jsp?error=" + URLEncoder.encode("医生不存在", "UTF-8"));
                return;
            }
            
            // 更新医生信息
            doctor.setName(name.trim());
            doctor.setGender(gender);
            doctor.setPhone(phone.trim());
            doctor.setDeptId(deptId);
            doctor.setStatus(status);
            
            // 处理可选字段
            if (ageStr != null && !ageStr.trim().isEmpty()) {
                try {
                    doctor.setAge(Integer.parseInt(ageStr.trim()));
                } catch (NumberFormatException e) {
                    response.sendRedirect(request.getContextPath() + "/admin/edit-doctor.jsp?id=" + doctorId + "&error=" + URLEncoder.encode("年龄格式不正确", "UTF-8"));
                    return;
                }
            } else {
                doctor.setAge(null);
            }
            
            if (email != null && !email.trim().isEmpty()) {
                doctor.setEmail(email.trim());
            } else {
                doctor.setEmail(null);
            }
            
            if (title != null && !title.trim().isEmpty()) {
                doctor.setTitle(title);
            } else {
                doctor.setTitle(null);
            }
            
            if (speciality != null && !speciality.trim().isEmpty()) {
                doctor.setSpeciality(speciality.trim());
            } else {
                doctor.setSpeciality(null);
            }
            
            // 保存更新
            boolean success = doctorService.updateDoctor(doctor);
            
            if (success) {
                response.sendRedirect(request.getContextPath() + "/admin/view-doctor.jsp?id=" + doctorId + "&success=" + URLEncoder.encode("医生信息更新成功", "UTF-8"));
            } else {
                response.sendRedirect(request.getContextPath() + "/admin/edit-doctor.jsp?id=" + doctorId + "&error=" + URLEncoder.encode("更新失败，请重试", "UTF-8"));
            }
            
        } catch (NumberFormatException e) {
            response.sendRedirect(request.getContextPath() + "/admin/edit-doctor.jsp?id=" + idStr + "&error=" + URLEncoder.encode("参数格式错误", "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect(request.getContextPath() + "/admin/edit-doctor.jsp?id=" + idStr + "&error=" + URLEncoder.encode("系统错误：" + e.getMessage(), "UTF-8"));
        }
    }
}
