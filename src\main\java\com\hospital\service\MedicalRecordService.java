package com.hospital.service;

import com.hospital.entity.MedicalRecord;
import java.util.Date;
import java.util.List;

/**
 * 就诊记录服务接口
 */
public interface MedicalRecordService {
    
    /**
     添加就诊记录
     */
    boolean addMedicalRecord(MedicalRecord medicalRecord);
    
    /**
     更新就诊记录
     */
    boolean updateMedicalRecord(MedicalRecord medicalRecord);
    
    /**
     根据ID查询就诊记录
     */
    MedicalRecord findById(Integer id);
    
    /**
     根据挂号ID查询就诊记录
     */
    MedicalRecord findByRegId(Integer regId);
    
    /**
     根据患者ID查询就诊记录
     */
    List<MedicalRecord> findByPatientId(Integer patientId);
    
    /**
     根据医生ID查询就诊记录
     */
    List<MedicalRecord> findByDoctorId(Integer doctorId);
    
    /**
     查询所有就诊记录（包含详情）
     */
    List<MedicalRecord> findAllWithDetails();
    
    /**
     根据患者ID查询就诊记录（包含详情）
     */
    List<MedicalRecord> findByPatientIdWithDetails(Integer patientId);
    
    /**
     根据医生ID查询就诊记录（包含详情）
     */
    List<MedicalRecord> findByDoctorIdWithDetails(Integer doctorId);
    
    /**
     根据挂号ID查询就诊记录（包含详情）
     */
    MedicalRecord findByRegIdWithDetails(Integer regId);
    
    /**
     删除就诊记录
     */
    boolean deleteMedicalRecord(Integer id);
    
    /**
     分页查询就诊记录
     */
    List<MedicalRecord> findByPage(int page, int size);
    
    /**
     * 统计就诊记录总数
     * @return 总数
     */
    int count();
    
    /**
     统计医生就诊数量
     */
    int countByDoctorAndDateRange(Integer doctorId, Date startDate, Date endDate);
    
    /**
     统计患者就诊数量
     */
    int countByPatientId(Integer patientId);
}
