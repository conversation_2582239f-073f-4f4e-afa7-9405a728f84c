package com.hospital.ioc;

import com.hospital.dao.*;
import com.hospital.dao.impl.*;
import com.hospital.service.*;
import com.hospital.service.impl.*;
import com.hospital.util.DatabaseUtil;

/**
 Bean工厂
 负责创建和管理系统中所有Bean实例的工厂类，实现了简单的IoC（控制反转）容器功能。
 该类采用单例模式，确保整个应用程序中只有一个Bean工厂实例。
 主要功能：
 1. 创建和初始化所有Bean实例（DAO、Service、工具类等）
 2. 管理Bean之间的依赖关系，实现依赖注入
 3. 提供Bean实例的统一访问入口
 4. 控制Bean的生命周期
 初始化顺序：
 1. 工具类（DatabaseUtil）
 2. DAO层实现类
 3. Service层实现类
 4. 依赖注入配置
 */
public class BeanFactory {

    /**
     单例实例
     使用volatile关键字确保多线程环境下的可见性
     */
    private static BeanFactory instance;

    /**
     应用程序上下文
     用于存储和管理所有的Bean实例
     */
    private ApplicationContext applicationContext;

    /**
     私有构造函数
     防止外部直接创建实例，确保单例模式的实现
     在构造时自动初始化所有Bean
     */
    private BeanFactory() {
        this.applicationContext = ApplicationContext.getInstance();
        initializeBeans();
    }

    /**
     获取Bean工厂单例实例
     使用双重检查锁定模式确保线程安全的单例实现
     */
    public static BeanFactory getInstance() {
        if (instance == null) {
            synchronized (BeanFactory.class) {
                if (instance == null) {
                    instance = new BeanFactory();
                }
            }
        }
        return instance;
    }
    
    /**
     初始化所有Bean
     按照依赖关系的顺序创建和注册所有Bean实例：
     1. 首先创建基础工具类（DatabaseUtil）
     2. 然后创建DAO层实现类
     3. 接着创建Service层实现类
     4. 最后执行依赖注入，建立Bean之间的关联关系
     所有Bean都注册到ApplicationContext中，便于后续获取和管理
     */
    private void initializeBeans() {
        try {
            // ==================== 第一步：初始化基础工具类 ====================

            // 创建数据库工具实例，提供数据库连接管理功能
            DatabaseUtil databaseUtil = new DatabaseUtil();
            applicationContext.registerBean("databaseUtil", databaseUtil);

            // ==================== 第二步：初始化DAO层 ====================

            // 创建所有DAO层实现类实例，负责数据访问操作
            PatientDao patientDao = new PatientDaoImpl();           // 患者数据访问对象
            DoctorDao doctorDao = new DoctorDaoImpl();             // 医生数据访问对象
            DepartmentDao departmentDao = new DepartmentDaoImpl(); // 科室数据访问对象
            RegistrationDao registrationDao = new RegistrationDaoImpl(); // 挂号数据访问对象
            MedicalRecordDao medicalRecordDao = new MedicalRecordDaoImpl(); // 病历数据访问对象

            // 药品和处方相关DAO
            MedicineCategoryDao medicineCategoryDao = new MedicineCategoryDaoImpl(); // 药品分类数据访问对象
            MedicineDao medicineDao = new MedicineDaoImpl(); // 药品数据访问对象
            ChineseMedicineAttrDao chineseMedicineAttrDao = new ChineseMedicineAttrDaoImpl(); // 中药特殊属性数据访问对象
            PrescriptionDao prescriptionDao = new PrescriptionDaoImpl(); // 处方数据访问对象
            PrescriptionDetailDao prescriptionDetailDao = new PrescriptionDetailDaoImpl(); // 处方明细数据访问对象
            ChinesePrescriptionAttrDao chinesePrescriptionAttrDao = new ChinesePrescriptionAttrDaoImpl(); // 中药处方特殊属性数据访问对象

            // 将DAO实例注册到应用程序上下文中
            applicationContext.registerBean("patientDao", patientDao);
            applicationContext.registerBean("doctorDao", doctorDao);
            applicationContext.registerBean("departmentDao", departmentDao);
            applicationContext.registerBean("registrationDao", registrationDao);
            applicationContext.registerBean("medicalRecordDao", medicalRecordDao);

            // 注册药品和处方相关DAO
            applicationContext.registerBean("medicineCategoryDao", medicineCategoryDao);
            applicationContext.registerBean("medicineDao", medicineDao);
            applicationContext.registerBean("chineseMedicineAttrDao", chineseMedicineAttrDao);
            applicationContext.registerBean("prescriptionDao", prescriptionDao);
            applicationContext.registerBean("prescriptionDetailDao", prescriptionDetailDao);
            applicationContext.registerBean("chinesePrescriptionAttrDao", chinesePrescriptionAttrDao);

            // ==================== 第三步：初始化Service层 ====================

            // 创建所有Service层实现类实例，负责业务逻辑处理
            PatientService patientService = new PatientServiceImpl();           // 患者业务服务
            DoctorService doctorService = new DoctorServiceImpl();             // 医生业务服务
            DepartmentService departmentService = new DepartmentServiceImpl(); // 科室业务服务
            RegistrationService registrationService = new RegistrationServiceImpl(); // 挂号业务服务
            MedicalRecordService medicalRecordService = new MedicalRecordServiceImpl(); // 病历业务服务

            // 药品和处方相关Service
            MedicineCategoryService medicineCategoryService = new MedicineCategoryServiceImpl(); // 药品分类业务服务
            MedicineService medicineService = new MedicineServiceImpl(); // 药品业务服务
            PrescriptionService prescriptionService = new PrescriptionServiceImpl(); // 处方业务服务

            // 将Service实例注册到应用程序上下文中
            applicationContext.registerBean("patientService", patientService);
            applicationContext.registerBean("doctorService", doctorService);
            applicationContext.registerBean("departmentService", departmentService);
            applicationContext.registerBean("registrationService", registrationService);
            applicationContext.registerBean("medicalRecordService", medicalRecordService);

            // 注册药品和处方相关Service
            applicationContext.registerBean("medicineCategoryService", medicineCategoryService);
            applicationContext.registerBean("medicineService", medicineService);
            applicationContext.registerBean("prescriptionService", prescriptionService);

            // ==================== 第四步：执行依赖注入 ====================

            // 建立Bean之间的依赖关系，实现自动装配
            injectDependencies();

        } catch (Exception e) {
            // 如果初始化过程中出现任何异常，包装为运行时异常抛出
            throw new RuntimeException("Failed to initialize beans", e);
        }
    }
    
    /**
     * 执行依赖注入
     * 建立Bean之间的依赖关系，实现控制反转（IoC）的核心功能。
     * 通过setter方法将依赖的Bean注入到目标Bean中，避免了硬编码的依赖关系。
     * 依赖注入顺序：
     * 1. 为DAO层注入DatabaseUtil依赖
     * 2. 为Service层注入DAO层依赖
     * 3. 为Service层注入其他Service依赖（如有需要）
     */
    private void injectDependencies() {

        // ==================== 第一步：为DAO层注入DatabaseUtil依赖 ====================

        // 从应用程序上下文中获取DAO层实例
        PatientDaoImpl patientDao = (PatientDaoImpl) applicationContext.getBean(PatientDao.class);
        DoctorDaoImpl doctorDao = (DoctorDaoImpl) applicationContext.getBean(DoctorDao.class);
        DepartmentDaoImpl departmentDao = (DepartmentDaoImpl) applicationContext.getBean(DepartmentDao.class);
        RegistrationDaoImpl registrationDao = (RegistrationDaoImpl) applicationContext.getBean(RegistrationDao.class);
        MedicalRecordDaoImpl medicalRecordDao = (MedicalRecordDaoImpl) applicationContext.getBean(MedicalRecordDao.class);

        // 获取药品和处方相关DAO层实例
        MedicineCategoryDaoImpl medicineCategoryDao = (MedicineCategoryDaoImpl) applicationContext.getBean(MedicineCategoryDao.class);
        MedicineDaoImpl medicineDao = (MedicineDaoImpl) applicationContext.getBean(MedicineDao.class);
        ChineseMedicineAttrDaoImpl chineseMedicineAttrDao = (ChineseMedicineAttrDaoImpl) applicationContext.getBean(ChineseMedicineAttrDao.class);
        PrescriptionDaoImpl prescriptionDao = (PrescriptionDaoImpl) applicationContext.getBean(PrescriptionDao.class);
        PrescriptionDetailDaoImpl prescriptionDetailDao = (PrescriptionDetailDaoImpl) applicationContext.getBean(PrescriptionDetailDao.class);
        ChinesePrescriptionAttrDaoImpl chinesePrescriptionAttrDao = (ChinesePrescriptionAttrDaoImpl) applicationContext.getBean(ChinesePrescriptionAttrDao.class);

        // 获取数据库工具实例
        DatabaseUtil databaseUtil = applicationContext.getBean(DatabaseUtil.class);

        // 为所有DAO实例注入数据库工具依赖
        patientDao.setDatabaseUtil(databaseUtil);
        doctorDao.setDatabaseUtil(databaseUtil);
        departmentDao.setDatabaseUtil(databaseUtil);
        registrationDao.setDatabaseUtil(databaseUtil);
        medicalRecordDao.setDatabaseUtil(databaseUtil);

        // 为药品和处方相关DAO注入数据库工具依赖
        // 注意：这些DAO实现类没有setDatabaseUtil方法，因为它们直接使用JdbcTemplate

        // ==================== 第二步：为Service层注入DAO层依赖 ====================

        // 从应用程序上下文中获取Service层实例
        PatientServiceImpl patientService = (PatientServiceImpl) applicationContext.getBean(PatientService.class);
        DoctorServiceImpl doctorService = (DoctorServiceImpl) applicationContext.getBean(DoctorService.class);
        DepartmentServiceImpl departmentService = (DepartmentServiceImpl) applicationContext.getBean(DepartmentService.class);
        RegistrationServiceImpl registrationService = (RegistrationServiceImpl) applicationContext.getBean(RegistrationService.class);
        MedicalRecordServiceImpl medicalRecordService = (MedicalRecordServiceImpl) applicationContext.getBean(MedicalRecordService.class);

        // 为Service层注入对应的DAO层依赖
        patientService.setPatientDao(patientDao);
        doctorService.setDoctorDao(doctorDao);
        departmentService.setDepartmentDao(departmentDao);
        registrationService.setRegistrationDao(registrationDao);
        medicalRecordService.setMedicalRecordDao(medicalRecordDao);

        // ==================== 第三步：为Service层注入其他Service依赖 ====================

        // 病历服务需要依赖挂号服务来获取挂号信息
        medicalRecordService.setRegistrationService(registrationService);
    }

    /**
     * 获取ApplicationContext
     *
     * 提供对应用程序上下文的访问，外部可以通过此方法获取Bean实例
     *
     * @return 应用程序上下文实例
     */
    public ApplicationContext getApplicationContext() {
        return applicationContext;
    }
}
