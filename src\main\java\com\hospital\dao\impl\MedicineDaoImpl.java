package com.hospital.dao.impl;

import com.hospital.dao.MedicineDao;
import com.hospital.entity.Medicine;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 药品DAO实现类
 */
public class MedicineDaoImpl implements MedicineDao {
    
    @Override
    public int insert(Medicine medicine) {
        String sql = "INSERT INTO medicine (medicine_code, medicine_name, medicine_type, category_id, " +
                    "specification, dosage_form, unit, price, stock, manufacturer, approval_number, " +
                    "description, usage_method, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        return JdbcTemplate.update(sql, medicine.getMedicineCode(), medicine.getMedicineName(),
                medicine.getMedicineType(), medicine.getCategoryId(), medicine.getSpecification(),
                medicine.getDosageForm(), medicine.getUnit(), medicine.getPrice(), medicine.getStock(),
                medicine.getManufacturer(), medicine.getApprovalNumber(), medicine.getDescription(),
                medicine.getUsageMethod(), medicine.getStatus());
    }
    
    @Override
    public int update(Medicine medicine) {
        String sql = "UPDATE medicine SET medicine_code = ?, medicine_name = ?, medicine_type = ?, " +
                    "category_id = ?, specification = ?, dosage_form = ?, unit = ?, price = ?, " +
                    "stock = ?, manufacturer = ?, approval_number = ?, description = ?, " +
                    "usage_method = ?, status = ? WHERE id = ?";
        return JdbcTemplate.update(sql, medicine.getMedicineCode(), medicine.getMedicineName(),
                medicine.getMedicineType(), medicine.getCategoryId(), medicine.getSpecification(),
                medicine.getDosageForm(), medicine.getUnit(), medicine.getPrice(), medicine.getStock(),
                medicine.getManufacturer(), medicine.getApprovalNumber(), medicine.getDescription(),
                medicine.getUsageMethod(), medicine.getStatus(), medicine.getId());
    }
    
    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM medicine WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }
    
    @Override
    public Medicine findById(Integer id) {
        String sql = "SELECT * FROM medicine WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToMedicine, id);
    }
    
    @Override
    public List<Medicine> findAll() {
        String sql = "SELECT * FROM medicine ORDER BY medicine_type, medicine_name";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicine);
    }
    
    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM medicine";
        return JdbcTemplate.queryForInt(sql);
    }
    
    @Override
    public List<Medicine> findByPage(int offset, int size) {
        String sql = "SELECT * FROM medicine ORDER BY medicine_type, medicine_name LIMIT ?, ?";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicine, offset, size);
    }
    
    @Override
    public Medicine findByMedicineCode(String medicineCode) {
        String sql = "SELECT * FROM medicine WHERE medicine_code = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToMedicine, medicineCode);
    }
    
    @Override
    public List<Medicine> findByMedicineNameLike(String medicineName) {
        String sql = "SELECT * FROM medicine WHERE medicine_name LIKE ? ORDER BY medicine_name";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicine, "%" + medicineName + "%");
    }
    
    @Override
    public List<Medicine> findByMedicineType(String medicineType) {
        String sql = "SELECT * FROM medicine WHERE medicine_type = ? ORDER BY medicine_name";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicine, medicineType);
    }
    
    @Override
    public List<Medicine> findByCategoryId(Integer categoryId) {
        String sql = "SELECT * FROM medicine WHERE category_id = ? ORDER BY medicine_name";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicine, categoryId);
    }
    
    @Override
    public List<Medicine> findByStatus(String status) {
        String sql = "SELECT * FROM medicine WHERE status = ? ORDER BY medicine_type, medicine_name";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicine, status);
    }
    
    @Override
    public List<Medicine> findAllWithCategory() {
        String sql = "SELECT m.*, mc.category_name FROM medicine m " +
                    "LEFT JOIN medicine_category mc ON m.category_id = mc.id " +
                    "ORDER BY m.medicine_type, m.medicine_name";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicineWithCategory);
    }
    
    @Override
    public List<Medicine> findByMedicineTypeWithCategory(String medicineType) {
        String sql = "SELECT m.*, mc.category_name FROM medicine m " +
                    "LEFT JOIN medicine_category mc ON m.category_id = mc.id " +
                    "WHERE m.medicine_type = ? ORDER BY m.medicine_name";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicineWithCategory, medicineType);
    }
    
    @Override
    public List<Medicine> findByMedicineNameLikeWithCategory(String medicineName) {
        String sql = "SELECT m.*, mc.category_name FROM medicine m " +
                    "LEFT JOIN medicine_category mc ON m.category_id = mc.id " +
                    "WHERE m.medicine_name LIKE ? ORDER BY m.medicine_name";
        return JdbcTemplate.queryForList(sql, this::mapRowToMedicineWithCategory, "%" + medicineName + "%");
    }
    
    @Override
    public boolean existsByMedicineCode(String medicineCode) {
        String sql = "SELECT COUNT(*) FROM medicine WHERE medicine_code = ?";
        return JdbcTemplate.queryForInt(sql, medicineCode) > 0;
    }
    
    @Override
    public int updateStock(Integer medicineId, Integer stock) {
        String sql = "UPDATE medicine SET stock = ? WHERE id = ?";
        return JdbcTemplate.update(sql, stock, medicineId);
    }
    
    @Override
    public int reduceStock(Integer medicineId, Integer quantity) {
        String sql = "UPDATE medicine SET stock = stock - ? WHERE id = ? AND stock >= ?";
        return JdbcTemplate.update(sql, quantity, medicineId, quantity);
    }
    
    /**
     * 将ResultSet映射为Medicine对象
     */
    private Medicine mapRowToMedicine(ResultSet rs) throws SQLException {
        Medicine medicine = new Medicine();
        medicine.setId(rs.getInt("id"));
        medicine.setMedicineCode(rs.getString("medicine_code"));
        medicine.setMedicineName(rs.getString("medicine_name"));
        medicine.setMedicineType(rs.getString("medicine_type"));
        medicine.setCategoryId(rs.getInt("category_id"));
        medicine.setSpecification(rs.getString("specification"));
        medicine.setDosageForm(rs.getString("dosage_form"));
        medicine.setUnit(rs.getString("unit"));
        medicine.setPrice(rs.getBigDecimal("price"));
        medicine.setStock(rs.getInt("stock"));
        medicine.setManufacturer(rs.getString("manufacturer"));
        medicine.setApprovalNumber(rs.getString("approval_number"));
        medicine.setDescription(rs.getString("description"));
        medicine.setUsageMethod(rs.getString("usage_method"));
        medicine.setStatus(rs.getString("status"));
        medicine.setCreateTime(rs.getTimestamp("create_time"));
        medicine.setUpdateTime(rs.getTimestamp("update_time"));
        return medicine;
    }
    
    /**
     * 将ResultSet映射为Medicine对象（包含分类信息）
     */
    private Medicine mapRowToMedicineWithCategory(ResultSet rs) throws SQLException {
        Medicine medicine = mapRowToMedicine(rs);
        medicine.setCategoryName(rs.getString("category_name"));
        return medicine;
    }
}
