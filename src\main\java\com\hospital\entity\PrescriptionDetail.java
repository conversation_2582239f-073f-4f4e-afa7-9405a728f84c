package com.hospital.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 处方明细实体类
 * 用于管理处方中的具体药品信息
 * 数据库对应表：prescription_detail
 */
public class PrescriptionDetail {
    
    private Integer id;
    private Integer prescriptionId;
    private Integer medicineId;
    private String medicineName;
    private String medicineType; // 中药、西药
    private String specification;
    private BigDecimal dosage; // 剂量
    private String dosageUnit; // 剂量单位
    private String frequency; // 用药频次
    private String usageMethod; // 用法
    private Integer days; // 用药天数
    private BigDecimal quantity; // 数量
    private BigDecimal unitPrice; // 单价
    private BigDecimal totalPrice; // 总价
    private String notes; // 备注
    private Date createTime;
    private Date updateTime;
    
    // 非数据库字段，用于显示
    private String medicineCode;
    private String categoryName;
    
    public PrescriptionDetail() {}
    
    public PrescriptionDetail(Integer prescriptionId, Integer medicineId, String medicineName, 
                             String medicineType, String specification, BigDecimal dosage, 
                             String dosageUnit, String frequency, String usageMethod, 
                             Integer days, BigDecimal quantity, BigDecimal unitPrice) {
        this.prescriptionId = prescriptionId;
        this.medicineId = medicineId;
        this.medicineName = medicineName;
        this.medicineType = medicineType;
        this.specification = specification;
        this.dosage = dosage;
        this.dosageUnit = dosageUnit;
        this.frequency = frequency;
        this.usageMethod = usageMethod;
        this.days = days;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.totalPrice = quantity.multiply(unitPrice);
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public Integer getPrescriptionId() {
        return prescriptionId;
    }
    
    public void setPrescriptionId(Integer prescriptionId) {
        this.prescriptionId = prescriptionId;
    }
    
    public Integer getMedicineId() {
        return medicineId;
    }
    
    public void setMedicineId(Integer medicineId) {
        this.medicineId = medicineId;
    }
    
    public String getMedicineName() {
        return medicineName;
    }
    
    public void setMedicineName(String medicineName) {
        this.medicineName = medicineName;
    }
    
    public String getMedicineType() {
        return medicineType;
    }
    
    public void setMedicineType(String medicineType) {
        this.medicineType = medicineType;
    }
    
    public String getSpecification() {
        return specification;
    }
    
    public void setSpecification(String specification) {
        this.specification = specification;
    }
    
    public BigDecimal getDosage() {
        return dosage;
    }
    
    public void setDosage(BigDecimal dosage) {
        this.dosage = dosage;
    }
    
    public String getDosageUnit() {
        return dosageUnit;
    }
    
    public void setDosageUnit(String dosageUnit) {
        this.dosageUnit = dosageUnit;
    }
    
    public String getFrequency() {
        return frequency;
    }
    
    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }
    
    public String getUsageMethod() {
        return usageMethod;
    }
    
    public void setUsageMethod(String usageMethod) {
        this.usageMethod = usageMethod;
    }
    
    public Integer getDays() {
        return days;
    }
    
    public void setDays(Integer days) {
        this.days = days;
    }
    
    public BigDecimal getQuantity() {
        return quantity;
    }
    
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
    
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }
    
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }
    
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }
    
    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getMedicineCode() {
        return medicineCode;
    }
    
    public void setMedicineCode(String medicineCode) {
        this.medicineCode = medicineCode;
    }
    
    public String getCategoryName() {
        return categoryName;
    }
    
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
    
    @Override
    public String toString() {
        return "PrescriptionDetail{" +
                "id=" + id +
                ", prescriptionId=" + prescriptionId +
                ", medicineId=" + medicineId +
                ", medicineName='" + medicineName + '\'' +
                ", medicineType='" + medicineType + '\'' +
                ", dosage=" + dosage +
                ", dosageUnit='" + dosageUnit + '\'' +
                ", frequency='" + frequency + '\'' +
                ", days=" + days +
                ", quantity=" + quantity +
                ", unitPrice=" + unitPrice +
                ", totalPrice=" + totalPrice +
                '}';
    }
}
