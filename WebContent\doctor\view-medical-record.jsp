<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%@ page import="com.hospital.entity.Registration" %>
<%@ page import="com.hospital.entity.MedicalRecord" %>
<%@ page import="com.hospital.service.RegistrationService" %>
<%@ page import="com.hospital.service.MedicalRecordService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Doctor doctor = (Doctor) session.getAttribute("doctor");
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    String regIdStr = request.getParameter("regId");
    if (regIdStr == null) {
        response.sendRedirect(request.getContextPath() + "/doctor/registrations.jsp");
        return;
    }
    
    // 获取服务
    RegistrationService registrationService = JspServiceUtil.getRegistrationService(application);
    MedicalRecordService medicalRecordService = JspServiceUtil.getMedicalRecordService(application);
    
    // 获取挂号信息
    Registration registration = registrationService.findById(Integer.parseInt(regIdStr));
    if (registration == null || !registration.getDoctorId().equals(doctor.getId())) {
        response.sendRedirect(request.getContextPath() + "/doctor/registrations.jsp");
        return;
    }
    
    // 获取挂号详情
    registration = registrationService.findByRegNo(registration.getRegNo());
    if (registration.getPatientName() == null) {
        java.util.List<Registration> regList = registrationService.findByDoctorIdWithDetails(doctor.getId());
        for (Registration reg : regList) {
            if (reg.getId().equals(registration.getId())) {
                registration = reg;
                break;
            }
        }
    }
    
    // 获取就诊记录
    MedicalRecord medicalRecord = medicalRecordService.findByRegId(Integer.parseInt(regIdStr));
    if (medicalRecord == null) {
        response.sendRedirect(request.getContextPath() + "/doctor/registrations.jsp");
        return;
    }
    
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
    SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查看就诊记录 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }
        
        .patient-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid var(--primary-color);
        }
        
        .patient-info h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .info-label {
            font-weight: 500;
            color: #666;
        }
        
        .info-value {
            color: #333;
        }
        
        .record-section {
            margin-bottom: 25px;
        }
        
        .record-section h4 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 1.1em;
            border-bottom: 1px solid #e1e5e9;
            padding-bottom: 5px;
        }
        
        .record-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
            min-height: 20px;
        }
        
        .record-content p {
            margin: 0;
            color: #333;
            line-height: 1.5;
        }
        
        .empty-content {
            color: #999;
            font-style: italic;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-right: 10px;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/doctor/registrations.jsp">返回挂号管理</a>
                <a href="${pageContext.request.contextPath}/doctor/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>查看就诊记录</h2>
            
            <div class="patient-info">
                <h3>患者信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">患者姓名：</span>
                        <span class="info-value"><%= registration.getPatientName() != null ? registration.getPatientName() : "未知" %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">挂号单号：</span>
                        <span class="info-value"><%= registration.getRegNo() %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">挂号日期：</span>
                        <span class="info-value"><%= dateFormat.format(registration.getRegDate()) %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">挂号时间：</span>
                        <span class="info-value"><%= timeFormat.format(registration.getRegTime()) %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">就诊时间：</span>
                        <span class="info-value"><%= medicalRecord.getVisitDate() != null ? dateTimeFormat.format(medicalRecord.getVisitDate()) : "未记录" %></span>
                    </div>
                </div>
            </div>
            
            <div class="record-section">
                <h4>主诉</h4>
                <div class="record-content">
                    <p><%= medicalRecord.getChiefComplaint() != null && !medicalRecord.getChiefComplaint().trim().isEmpty() ? medicalRecord.getChiefComplaint() : "<span class='empty-content'>未填写</span>" %></p>
                </div>
            </div>
            
            <div class="record-section">
                <h4>现病史</h4>
                <div class="record-content">
                    <p><%= medicalRecord.getPresentIllness() != null && !medicalRecord.getPresentIllness().trim().isEmpty() ? medicalRecord.getPresentIllness() : "<span class='empty-content'>未填写</span>" %></p>
                </div>
            </div>
            
            <div class="record-section">
                <h4>体格检查</h4>
                <div class="record-content">
                    <p><%= medicalRecord.getPhysicalExam() != null && !medicalRecord.getPhysicalExam().trim().isEmpty() ? medicalRecord.getPhysicalExam() : "<span class='empty-content'>未填写</span>" %></p>
                </div>
            </div>
            
            <div class="record-section">
                <h4>诊断</h4>
                <div class="record-content">
                    <p><%= medicalRecord.getDiagnosis() != null && !medicalRecord.getDiagnosis().trim().isEmpty() ? medicalRecord.getDiagnosis() : "<span class='empty-content'>未填写</span>" %></p>
                </div>
            </div>
            
            <div class="record-section">
                <h4>治疗方案</h4>
                <div class="record-content">
                    <p><%= medicalRecord.getTreatmentPlan() != null && !medicalRecord.getTreatmentPlan().trim().isEmpty() ? medicalRecord.getTreatmentPlan() : "<span class='empty-content'>未填写</span>" %></p>
                </div>
            </div>
            
            <div class="record-section">
                <h4>处方</h4>
                <div class="record-content">
                    <p><%= medicalRecord.getPrescription() != null && !medicalRecord.getPrescription().trim().isEmpty() ? medicalRecord.getPrescription() : "<span class='empty-content'>未填写</span>" %></p>
                </div>
            </div>
            
            <div class="record-section">
                <h4>医嘱</h4>
                <div class="record-content">
                    <p><%= medicalRecord.getAdvice() != null && !medicalRecord.getAdvice().trim().isEmpty() ? medicalRecord.getAdvice() : "<span class='empty-content'>未填写</span>" %></p>
                </div>
            </div>
            
            <% if (medicalRecord.getNextVisitDate() != null) { %>
            <div class="record-section">
                <h4>下次复诊日期</h4>
                <div class="record-content">
                    <p><%= dateFormat.format(medicalRecord.getNextVisitDate()) %></p>
                </div>
            </div>
            <% } %>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="${pageContext.request.contextPath}/doctor/edit-medical-record.jsp?id=<%= medicalRecord.getId() %>" class="btn btn-warning">编辑记录</a>
                <a href="${pageContext.request.contextPath}/doctor/registrations.jsp" class="btn btn-secondary">返回列表</a>
            </div>
        </div>
    </div>
</body>
</html>
