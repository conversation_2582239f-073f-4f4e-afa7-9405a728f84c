package com.hospital.dao.impl;

import com.hospital.dao.PatientDao;
import com.hospital.entity.Patient;
import com.hospital.util.DatabaseUtil;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 患者DAO实现类
 */
public class PatientDaoImpl implements PatientDao {

    private DatabaseUtil databaseUtil;

    public void setDatabaseUtil(DatabaseUtil databaseUtil) {
        this.databaseUtil = databaseUtil;
    }
    
    @Override
    public int insert(Patient patient) {
        String sql = "INSERT INTO patient (patient_no, name, gender, age, phone, id_card, " +
                    "address, emergency_contact, emergency_phone, password) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        return JdbcTemplate.update(sql, patient.getPatientNo(), patient.getName(),
                patient.getGender(), patient.getAge(), patient.getPhone(),
                patient.getIdCard(), patient.getAddress(), patient.getEmergencyContact(),
                patient.getEmergencyPhone(), patient.getPassword());
    }

    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM patient WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }

    @Override
    public int update(Patient patient) {
        String sql = "UPDATE patient SET name = ?, gender = ?, age = ?, phone = ?, " +
                    "id_card = ?, address = ?, emergency_contact = ?, emergency_phone = ? " +
                    "WHERE id = ?";
        return JdbcTemplate.update(sql, patient.getName(), patient.getGender(),
                patient.getAge(), patient.getPhone(), patient.getIdCard(),
                patient.getAddress(), patient.getEmergencyContact(),
                patient.getEmergencyPhone(), patient.getId());
    }
    
    @Override
    public Patient findById(Integer id) {
        String sql = "SELECT * FROM patient WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapPatient, id);
    }

    @Override
    public List<Patient> findAll() {
        String sql = "SELECT * FROM patient ORDER BY create_time DESC";
        return JdbcTemplate.query(sql, this::mapPatient);
    }

    @Override
    public List<Patient> findByPage(int offset, int limit) {
        String sql = "SELECT * FROM patient ORDER BY create_time DESC LIMIT ?, ?";
        return JdbcTemplate.query(sql, this::mapPatient, offset, limit);
    }

    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM patient";
        Long count = JdbcTemplate.queryForObject(sql, Long.class);
        return count != null ? count.intValue() : 0;
    }
    
    @Override
    public Patient findByPatientNo(String patientNo) {
        String sql = "SELECT * FROM patient WHERE patient_no = ?";
        return JdbcTemplate.queryForObject(sql, this::mapPatient, patientNo);
    }

    @Override
    public Patient findByIdCard(String idCard) {
        String sql = "SELECT * FROM patient WHERE id_card = ?";
        return JdbcTemplate.queryForObject(sql, this::mapPatient, idCard);
    }

    @Override
    public Patient findByPhone(String phone) {
        String sql = "SELECT * FROM patient WHERE phone = ?";
        return JdbcTemplate.queryForObject(sql, this::mapPatient, phone);
    }

    @Override
    public List<Patient> findByNameLike(String name) {
        String sql = "SELECT * FROM patient WHERE name LIKE ? ORDER BY create_time DESC";
        return JdbcTemplate.query(sql, this::mapPatient, "%" + name + "%");
    }

    @Override
    public Patient login(String patientNo, String password) {
        String sql = "SELECT * FROM patient WHERE patient_no = ? AND password = ?";
        return JdbcTemplate.queryForObject(sql, this::mapPatient, patientNo, password);
    }

    @Override
    public int updatePassword(Integer id, String newPassword) {
        String sql = "UPDATE patient SET password = ? WHERE id = ?";
        return JdbcTemplate.update(sql, newPassword, id);
    }

    /**
     映射ResultSet到Patient对象
     */
    private Patient mapPatient(ResultSet rs) throws SQLException {
        Patient patient = new Patient();
        patient.setId(rs.getInt("id"));
        patient.setPatientNo(rs.getString("patient_no"));
        patient.setName(rs.getString("name"));
        patient.setGender(rs.getString("gender"));
        patient.setAge(rs.getInt("age"));
        patient.setPhone(rs.getString("phone"));
        patient.setIdCard(rs.getString("id_card"));
        patient.setAddress(rs.getString("address"));
        patient.setEmergencyContact(rs.getString("emergency_contact"));
        patient.setEmergencyPhone(rs.getString("emergency_phone"));
        patient.setPassword(rs.getString("password"));
        patient.setCreateTime(rs.getTimestamp("create_time"));
        return patient;
    }
}
