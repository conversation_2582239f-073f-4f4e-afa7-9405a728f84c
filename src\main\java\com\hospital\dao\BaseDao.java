package com.hospital.dao;

import java.util.List;

/**
 基础DAO接口
 定义了数据访问层的基本CRUD操作，所有具体的DAO接口都应该继承此接口。
 使用泛型T来支持不同的实体类型，提供了统一的数据访问方法。
 该接口遵循DAO设计模式，将数据访问逻辑与业务逻辑分离，
 为上层Service提供统一的数据访问接口。
 */
public interface BaseDao<T> {
    
    /**
     添加实体
     向数据库中插入一条新记录，实体对象的ID字段通常由数据库自动生成
     */
    int insert(T entity);

    /**
     根据ID删除实体
     从数据库中删除指定ID的记录，这是物理删除操作
     */
    int deleteById(Integer id);

    /**
     更新实体
     根据实体对象的ID更新数据库中对应的记录
     */
    int update(T entity);

    /**
     根据ID查询实体
     从数据库中查询指定ID的记录并转换为实体对象
     */
    T findById(Integer id);

    /**
     查询所有实体
     从数据库中查询所有记录并转换为实体对象列表
     注意：当数据量较大时，建议使用分页查询方法
     */
    List<T> findAll();

    /**
     分页查询
     从数据库中分页查询记录，用于处理大量数据的场景
     */
    List<T> findByPage(int offset, int limit);

    /**
     统计总数
     统计数据库表中的记录总数，常用于分页查询时计算总页数
     */
    int count();
}
