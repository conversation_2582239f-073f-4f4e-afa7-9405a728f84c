<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Patient" %>
<%
    Patient patient = (Patient) session.getAttribute("patient");
    if (patient == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>我的挂号测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .nav-links a { display: inline-block; margin-right: 15px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
        .nav-links a:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🏥 我的挂号功能测试</h1>
            
            <div class="success">
                <h2>✅ 测试结果：我的挂号页面访问正常！</h2>
                <p>患者模块的Spring依赖已完全移除，现在使用JspServiceUtil获取服务。</p>
            </div>
            
            <div class="info">
                <h3>📋 患者信息</h3>
                <p><strong>患者姓名:</strong> <%= patient.getName() %></p>
                <p><strong>患者编号:</strong> <%= patient.getPatientNo() %></p>
                <p><strong>身份证号:</strong> <%= patient.getIdCard() != null ? patient.getIdCard().substring(0, 6) + "****" + patient.getIdCard().substring(14) : "未设置" %></p>
                <p><strong>联系电话:</strong> <%= patient.getPhone() != null ? patient.getPhone() : "未设置" %></p>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
            </div>
            
            <div class="info">
                <h3>🔧 修复内容</h3>
                <ul>
                    <li>✅ my-registrations.jsp - 我的挂号记录页面</li>
                    <li>✅ doctors.jsp - 医生排班查看页面</li>
                    <li>✅ 所有页面已移除Spring依赖</li>
                    <li>✅ 使用JspServiceUtil获取服务</li>
                </ul>
            </div>
            
            <div class="nav-links">
                <h3>🚀 功能测试</h3>
                <a href="${pageContext.request.contextPath}/patient/my-registrations.jsp">我的挂号记录</a>
                <a href="${pageContext.request.contextPath}/patient/doctors.jsp">医生排班</a>
                <a href="${pageContext.request.contextPath}/patient/registration.jsp">在线挂号</a>
                <a href="${pageContext.request.contextPath}/patient/index.jsp">返回患者首页</a>
            </div>
            
            <div class="info">
                <h3>💡 使用说明</h3>
                <p>现在您可以：</p>
                <ol>
                    <li><strong>查看挂号记录</strong> - 点击"我的挂号记录"查看所有挂号历史</li>
                    <li><strong>查看医生信息</strong> - 点击"医生排班"查看医生信息和科室</li>
                    <li><strong>在线挂号</strong> - 选择科室和医生进行预约挂号</li>
                    <li><strong>查看就诊记录</strong> - 查看历史就诊记录和诊断信息</li>
                </ol>
            </div>
            
            <div class="info">
                <h3>🔧 技术信息</h3>
                <ul>
                    <li><strong>服务获取方式</strong>: JspServiceUtil.getRegistrationService(application)</li>
                    <li><strong>Spring依赖</strong>: 已完全移除</li>
                    <li><strong>原生IoC容器</strong>: BeanFactory + ApplicationContext</li>
                    <li><strong>数据库连接</strong>: Druid连接池</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>📋 挂号记录功能</h3>
                <p>我的挂号页面包含以下功能：</p>
                <ul>
                    <li>📊 <strong>统计信息</strong>: 显示挂号记录总数</li>
                    <li>📋 <strong>详细列表</strong>: 挂号编号、科室、医生、就诊时间等</li>
                    <li>🏷️ <strong>状态标识</strong>: 已挂号、已就诊、已取消状态</li>
                    <li>📝 <strong>症状描述</strong>: 显示患者主要症状</li>
                    <li>🔄 <strong>实时更新</strong>: 挂号状态实时同步</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
