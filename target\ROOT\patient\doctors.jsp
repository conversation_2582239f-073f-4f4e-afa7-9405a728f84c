<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Patient" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%@ page import="com.hospital.entity.Department" %>
<%@ page import="com.hospital.service.DoctorService" %>
<%@ page import="com.hospital.service.DepartmentService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%
    Patient patient = (Patient) session.getAttribute("patient");
    if (patient == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    // 获取服务
    DoctorService doctorService = JspServiceUtil.getDoctorService(application);
    DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
    
    List<Doctor> doctors = doctorService.findAllWithDept();
    List<Department> departments = departmentService.findAll();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医生排班 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .filter-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-bar select,
        .filter-bar input {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1em;
        }
        
        .filter-bar select:focus,
        .filter-bar input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .doctors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .doctor-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            padding: 25px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .doctor-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .doctor-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .doctor-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5em;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .doctor-info h3 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 1.2em;
        }
        
        .doctor-title {
            color: #667eea;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .doctor-dept {
            color: #666;
            font-size: 0.9em;
        }
        
        .doctor-details {
            margin-bottom: 20px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .detail-label {
            color: #666;
            font-weight: 500;
        }
        
        .detail-value {
            color: #333;
        }
        
        .speciality {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 3px solid #667eea;
        }
        
        .speciality-label {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        
        .speciality-text {
            color: #666;
            line-height: 1.4;
            font-size: 0.9em;
        }
        
        .schedule {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        
        .schedule-title {
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }
        
        .schedule-times {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }
        
        .time-slot {
            background: white;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-size: 0.85em;
            color: #666;
            border: 1px solid #e1e5e9;
        }
        
        .time-slot.available {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        
        .doctor-actions {
            text-align: center;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/patient/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>医生排班信息</h2>
            
            <div class="filter-bar">
                <label>科室筛选：</label>
                <select id="deptFilter" onchange="filterDoctors()">
                    <option value="">全部科室</option>
                    <% for (Department dept : departments) { %>
                        <option value="<%= dept.getDeptName() %>"><%= dept.getDeptName() %></option>
                    <% } %>
                </select>
                
                <label>医生姓名：</label>
                <input type="text" id="nameFilter" placeholder="输入医生姓名" onkeyup="filterDoctors()">
                
                <label>职称筛选：</label>
                <select id="titleFilter" onchange="filterDoctors()">
                    <option value="">全部职称</option>
                    <option value="主任医师">主任医师</option>
                    <option value="副主任医师">副主任医师</option>
                    <option value="主治医师">主治医师</option>
                    <option value="住院医师">住院医师</option>
                </select>
            </div>
            
            <% if (doctors != null && !doctors.isEmpty()) { %>
                <div class="doctors-grid" id="doctorsGrid">
                    <% for (Doctor doctor : doctors) { %>
                        <% if ("在职".equals(doctor.getStatus())) { %>
                            <div class="doctor-card" data-dept="<%= doctor.getDeptName() != null ? doctor.getDeptName() : "" %>" 
                                 data-name="<%= doctor.getName().toLowerCase() %>"
                                 data-title="<%= doctor.getTitle() != null ? doctor.getTitle() : "" %>">
                                <div class="doctor-header">
                                    <div class="doctor-avatar">
                                        <%= doctor.getName().substring(0, 1) %>
                                    </div>
                                    <div class="doctor-info">
                                        <h3><%= doctor.getName() %></h3>
                                        <div class="doctor-title"><%= doctor.getTitle() != null ? doctor.getTitle() : "医师" %></div>
                                        <div class="doctor-dept"><%= doctor.getDeptName() != null ? doctor.getDeptName() : "未分配科室" %></div>
                                    </div>
                                </div>
                                
                                <div class="doctor-details">
                                    <div class="detail-item">
                                        <span class="detail-label">工号：</span>
                                        <span class="detail-value"><%= doctor.getDoctorNo() %></span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">性别：</span>
                                        <span class="detail-value"><%= doctor.getGender() %></span>
                                    </div>
                                    <% if (doctor.getAge() != null) { %>
                                    <div class="detail-item">
                                        <span class="detail-label">年龄：</span>
                                        <span class="detail-value"><%= doctor.getAge() %>岁</span>
                                    </div>
                                    <% } %>
                                </div>
                                
                                <% if (doctor.getSpeciality() != null && !doctor.getSpeciality().trim().isEmpty()) { %>
                                <div class="speciality">
                                    <div class="speciality-label">专业特长</div>
                                    <div class="speciality-text"><%= doctor.getSpeciality() %></div>
                                </div>
                                <% } %>
                                
                                <div class="schedule">
                                    <div class="schedule-title">📅 出诊时间</div>
                                    <div class="schedule-times">
                                        <div class="time-slot available">周一上午 8:00-12:00</div>
                                        <div class="time-slot available">周一下午 14:00-18:00</div>
                                        <div class="time-slot available">周三上午 8:00-12:00</div>
                                        <div class="time-slot available">周三下午 14:00-18:00</div>
                                        <div class="time-slot available">周五上午 8:00-12:00</div>
                                        <div class="time-slot">周五下午 休息</div>
                                    </div>
                                </div>
                                
                                <div class="doctor-actions">
                                    <a href="${pageContext.request.contextPath}/patient/registration.jsp?doctorId=<%= doctor.getId() %>" 
                                       class="btn btn-primary">立即挂号</a>
                                </div>
                            </div>
                        <% } %>
                    <% } %>
                </div>
            <% } else { %>
                <div class="empty-state">
                    <div style="font-size: 4em; margin-bottom: 20px;">👨‍⚕️</div>
                    <h3>暂无医生信息</h3>
                    <p>当前没有可用的医生排班信息。</p>
                </div>
            <% } %>
        </div>
    </div>
    
    <script>
        function filterDoctors() {
            const deptFilter = document.getElementById('deptFilter').value;
            const nameFilter = document.getElementById('nameFilter').value.toLowerCase();
            const titleFilter = document.getElementById('titleFilter').value;
            const doctorsGrid = document.getElementById('doctorsGrid');
            
            if (!doctorsGrid) return;
            
            const doctorCards = doctorsGrid.getElementsByClassName('doctor-card');
            
            for (let i = 0; i < doctorCards.length; i++) {
                const card = doctorCards[i];
                const dept = card.getAttribute('data-dept');
                const name = card.getAttribute('data-name');
                const title = card.getAttribute('data-title');
                
                let showCard = true;
                
                // 科室筛选
                if (deptFilter && dept !== deptFilter) {
                    showCard = false;
                }
                
                // 姓名筛选
                if (nameFilter && !name.includes(nameFilter)) {
                    showCard = false;
                }
                
                // 职称筛选
                if (titleFilter && title !== titleFilter) {
                    showCard = false;
                }
                
                card.style.display = showCard ? '' : 'none';
            }
        }
    </script>
</body>
</html>
