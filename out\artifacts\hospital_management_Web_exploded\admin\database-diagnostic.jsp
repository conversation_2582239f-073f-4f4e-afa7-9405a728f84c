<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="java.sql.*" %>
<%@ page import="java.util.*" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>数据库诊断页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0; color: #856404; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .nav-links a { display: inline-block; margin-right: 15px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🔍 数据库诊断页面</h1>
            
            <div class="info">
                <h3>📋 管理员信息</h3>
                <p><strong>管理员用户名:</strong> <%= admin.getUsername() %></p>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
            </div>
            
            <%
                List<String> databases = new ArrayList<>();
                List<String> tables = new ArrayList<>();
                String connectionStatus = "";
                String errorMessage = "";
                boolean connectionSuccess = false;
                
                // 测试数据库连接
                String[] dbNames = {"hospital_management", "hospital", "medical_system"};
                String workingDb = null;
                
                for (String dbName : dbNames) {
                    try {
                        String url = "***************************/" + dbName + "?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai";
                        String username = "root";
                        String password = "123456";
                        
                        Class.forName("com.mysql.cj.jdbc.Driver");
                        Connection conn = DriverManager.getConnection(url, username, password);
                        
                        connectionStatus = "✅ 成功连接到数据库: " + dbName;
                        connectionSuccess = true;
                        workingDb = dbName;
                        
                        // 获取所有表
                        DatabaseMetaData metaData = conn.getMetaData();
                        ResultSet rs = metaData.getTables(dbName, null, "%", new String[]{"TABLE"});
                        while (rs.next()) {
                            tables.add(rs.getString("TABLE_NAME"));
                        }
                        rs.close();
                        conn.close();
                        break;
                        
                    } catch (Exception e) {
                        databases.add("❌ " + dbName + ": " + e.getMessage());
                    }
                }
                
                if (!connectionSuccess) {
                    errorMessage = "无法连接到任何数据库";
                }
            %>
            
            <div class="<%= connectionSuccess ? "success" : "error" %>">
                <h2><%= connectionSuccess ? connectionStatus : "❌ 数据库连接失败" %></h2>
                <% if (!connectionSuccess) { %>
                    <p><%= errorMessage %></p>
                <% } %>
            </div>
            
            <% if (!databases.isEmpty()) { %>
                <div class="warning">
                    <h3>🔍 数据库连接尝试结果</h3>
                    <% for (String db : databases) { %>
                        <p><%= db %></p>
                    <% } %>
                </div>
            <% } %>
            
            <% if (connectionSuccess && workingDb != null) { %>
                <div class="info">
                    <h3>📊 数据库信息</h3>
                    <p><strong>工作数据库:</strong> <%= workingDb %></p>
                    <p><strong>表数量:</strong> <%= tables.size() %></p>
                </div>
                
                <div class="card">
                    <h3>📋 数据库表列表</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>表名</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% 
                                int index = 1;
                                boolean hasDepartments = false;
                                for (String table : tables) { 
                                    if ("departments".equals(table)) {
                                        hasDepartments = true;
                                    }
                            %>
                                <tr>
                                    <td><%= index++ %></td>
                                    <td><%= table %></td>
                                    <td>
                                        <% if ("departments".equals(table)) { %>
                                            <span style="color: green;">✅ 科室表存在</span>
                                        <% } else if ("doctors".equals(table)) { %>
                                            <span style="color: blue;">👨‍⚕️ 医生表</span>
                                        <% } else if ("patients".equals(table)) { %>
                                            <span style="color: purple;">👤 患者表</span>
                                        <% } else { %>
                                            <span style="color: gray;">📄 其他表</span>
                                        <% } %>
                                    </td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                    
                    <% if (!hasDepartments) { %>
                        <div class="error">
                            <h3>❌ 关键问题发现</h3>
                            <p><strong>departments表不存在！</strong></p>
                            <p>这就是编辑科室信息时出现错误的原因。</p>
                        </div>
                    <% } else { %>
                        <div class="success">
                            <h3>✅ departments表存在</h3>
                            <p>数据库表结构正常，可以进行科室管理操作。</p>
                        </div>
                    <% } %>
                </div>
                
                <% if (hasDepartments) { %>
                    <%
                        // 检查departments表结构
                        try {
                            String url = "***************************/" + workingDb + "?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai";
                            Connection conn = DriverManager.getConnection(url, "root", "123456");
                            
                            DatabaseMetaData metaData = conn.getMetaData();
                            ResultSet columns = metaData.getColumns(workingDb, null, "departments", null);
                    %>
                            <div class="card">
                                <h3>🏗️ departments表结构</h3>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>列名</th>
                                            <th>数据类型</th>
                                            <th>是否可空</th>
                                            <th>默认值</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% while (columns.next()) { %>
                                            <tr>
                                                <td><%= columns.getString("COLUMN_NAME") %></td>
                                                <td><%= columns.getString("TYPE_NAME") %>(<%= columns.getInt("COLUMN_SIZE") %>)</td>
                                                <td><%= columns.getString("IS_NULLABLE") %></td>
                                                <td><%= columns.getString("COLUMN_DEF") != null ? columns.getString("COLUMN_DEF") : "NULL" %></td>
                                            </tr>
                                        <% } %>
                                    </tbody>
                                </table>
                            </div>
                    <%
                            columns.close();
                            conn.close();
                        } catch (Exception e) {
                            out.println("<div class='error'>获取表结构失败: " + e.getMessage() + "</div>");
                        }
                    %>
                <% } %>
            <% } %>
            
            <div class="info">
                <h3>🔧 解决方案</h3>
                <% if (connectionSuccess) { %>
                    <% if (tables.contains("departments")) { %>
                        <p>✅ 数据库连接正常，departments表存在。</p>
                        <p>编辑科室功能应该可以正常工作。如果仍有问题，请检查:</p>
                        <ul>
                            <li>DepartmentService是否正确实现</li>
                            <li>数据库连接池配置是否正确</li>
                            <li>JspServiceUtil是否正确获取服务</li>
                        </ul>
                    <% } else { %>
                        <p>❌ departments表不存在，需要创建数据库表。</p>
                        <p>请执行以下SQL创建departments表:</p>
                        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dept_name VARCHAR(100) NOT NULL,
    location VARCHAR(200),
    phone VARCHAR(20),
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
                        </pre>
                    <% } %>
                <% } else { %>
                    <p>❌ 数据库连接失败，请检查:</p>
                    <ul>
                        <li>MySQL服务是否启动</li>
                        <li>数据库名称是否正确</li>
                        <li>用户名密码是否正确</li>
                        <li>端口号是否为3306</li>
                    </ul>
                <% } %>
            </div>
            
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/departments.jsp">返回科室管理</a>
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
                <a href="javascript:location.reload()">刷新诊断</a>
            </div>
        </div>
    </div>
</body>
</html>
