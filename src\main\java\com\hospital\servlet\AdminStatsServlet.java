package com.hospital.servlet;

import com.hospital.service.*;
import com.hospital.ioc.BeanFactory;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 管理员统计数据Servlet
 */
@WebServlet("/AdminStatsServlet")
public class AdminStatsServlet extends HttpServlet {
    
    private PatientService patientService;
    private DoctorService doctorService;
    private DepartmentService departmentService;
    private RegistrationService registrationService;
    private MedicalRecordService medicalRecordService;
    
    @Override
    public void init() throws ServletException {
        super.init();
        // 获取原生IoC容器中的服务
        BeanFactory beanFactory = BeanFactory.getInstance();
        patientService = beanFactory.getApplicationContext().getBean(PatientService.class);
        doctorService = beanFactory.getApplicationContext().getBean(DoctorService.class);
        departmentService = beanFactory.getApplicationContext().getBean(DepartmentService.class);
        registrationService = beanFactory.getApplicationContext().getBean(RegistrationService.class);
        medicalRecordService = beanFactory.getApplicationContext().getBean(MedicalRecordService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // 设置响应类型为JSON
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        try {
            // 获取统计数据
            Map<String, Object> stats = new HashMap<>();
            
            // 获取各项统计数据
            int patientCount = patientService.count();
            int doctorCount = doctorService.count();
            int departmentCount = departmentService.count();
            int registrationCount = registrationService.count();
            int medicalRecordCount = medicalRecordService.count();
            
            // 将统计数据放入Map
            stats.put("patientCount", patientCount);
            stats.put("doctorCount", doctorCount);
            stats.put("departmentCount", departmentCount);
            stats.put("registrationCount", registrationCount);
            stats.put("medicalRecordCount", medicalRecordCount);
            stats.put("success", true);
            
            // 转换为JSON并返回
            ObjectMapper mapper = new ObjectMapper();
            String jsonResponse = mapper.writeValueAsString(stats);
            
            response.getWriter().write(jsonResponse);
            
        } catch (Exception e) {
            e.printStackTrace();
            
            // 如果出错，返回错误信息
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "获取统计数据失败: " + e.getMessage());
            errorResponse.put("patientCount", 0);
            errorResponse.put("doctorCount", 0);
            errorResponse.put("departmentCount", 0);
            errorResponse.put("registrationCount", 0);
            errorResponse.put("medicalRecordCount", 0);
            
            ObjectMapper mapper = new ObjectMapper();
            String jsonResponse = mapper.writeValueAsString(errorResponse);
            
            response.getWriter().write(jsonResponse);
        }
    }
}
