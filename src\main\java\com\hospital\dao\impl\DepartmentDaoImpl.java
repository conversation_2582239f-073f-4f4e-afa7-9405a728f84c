package com.hospital.dao.impl;

import com.hospital.dao.DepartmentDao;
import com.hospital.entity.Department;
import com.hospital.util.DatabaseUtil;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 科室DAO实现类
 */
public class DepartmentDaoImpl implements DepartmentDao {

    private DatabaseUtil databaseUtil;

    public void setDatabaseUtil(DatabaseUtil databaseUtil) {
        this.databaseUtil = databaseUtil;
    }

    @Override
    public int insert(Department department) {
        String sql = "INSERT INTO department (dept_name, dept_desc, location, phone) VALUES (?, ?, ?, ?)";
        return JdbcTemplate.update(sql, department.getDeptName(), department.getDeptDesc(),
                department.getLocation(), department.getPhone());
    }

    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM department WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }

    @Override
    public int update(Department department) {
        String sql = "UPDATE department SET dept_name = ?, dept_desc = ?, location = ?, phone = ? WHERE id = ?";
        return JdbcTemplate.update(sql, department.getDeptName(), department.getDeptDesc(),
                department.getLocation(), department.getPhone(), department.getId());
    }

    @Override
    public Department findById(Integer id) {
        String sql = "SELECT * FROM department WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapDepartment, id);
    }

    @Override
    public List<Department> findAll() {
        String sql = "SELECT * FROM department ORDER BY create_time DESC";
        return JdbcTemplate.query(sql, this::mapDepartment);
    }

    @Override
    public List<Department> findByPage(int offset, int limit) {
        String sql = "SELECT * FROM department ORDER BY create_time DESC LIMIT ?, ?";
        return JdbcTemplate.query(sql, this::mapDepartment, offset, limit);
    }

    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM department";
        Long count = JdbcTemplate.queryForObject(sql, Long.class);
        return count != null ? count.intValue() : 0;
    }

    @Override
    public Department findByDeptName(String deptName) {
        String sql = "SELECT * FROM department WHERE dept_name = ?";
        return JdbcTemplate.queryForObject(sql, this::mapDepartment, deptName);
    }

    @Override
    public List<Department> findByDeptNameLike(String deptName) {
        String sql = "SELECT * FROM department WHERE dept_name LIKE ? ORDER BY create_time DESC";
        return JdbcTemplate.query(sql, this::mapDepartment, "%" + deptName + "%");
    }

    /**
     映射ResultSet到Department对象
     */
    private Department mapDepartment(ResultSet rs) throws SQLException {
        Department department = new Department();
        department.setId(rs.getInt("id"));
        department.setDeptName(rs.getString("dept_name"));
        department.setDeptDesc(rs.getString("dept_desc"));
        department.setLocation(rs.getString("location"));
        department.setPhone(rs.getString("phone"));
        department.setCreateTime(rs.getTimestamp("create_time"));
        return department;
    }
}
