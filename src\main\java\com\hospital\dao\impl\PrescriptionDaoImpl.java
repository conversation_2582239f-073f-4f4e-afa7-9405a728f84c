package com.hospital.dao.impl;

import com.hospital.dao.PrescriptionDao;
import com.hospital.entity.Prescription;
import com.hospital.util.JdbcTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * 处方DAO实现类
 */
public class PrescriptionDaoImpl implements PrescriptionDao {
    
    @Override
    public int insert(Prescription prescription) {
        String sql = "INSERT INTO prescription (prescription_no, medical_record_id, patient_id, doctor_id, " +
                    "prescription_type, diagnosis, prescription_date, status, notes) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        int generatedId = JdbcTemplate.insertAndReturnKey(sql, prescription.getPrescriptionNo(), prescription.getMedicalRecordId(),
                prescription.getPatientId(), prescription.getDoctorId(), prescription.getPrescriptionType(),
                prescription.getDiagnosis(), prescription.getPrescriptionDate(), prescription.getStatus(),
                prescription.getNotes());

        // 设置生成的ID到对象中
        prescription.setId(generatedId);
        return generatedId > 0 ? 1 : 0;
    }
    
    @Override
    public int update(Prescription prescription) {
        String sql = "UPDATE prescription SET prescription_no = ?, medical_record_id = ?, patient_id = ?, " +
                    "doctor_id = ?, prescription_type = ?, diagnosis = ?, prescription_date = ?, " +
                    "status = ?, notes = ? WHERE id = ?";
        return JdbcTemplate.update(sql, prescription.getPrescriptionNo(), prescription.getMedicalRecordId(),
                prescription.getPatientId(), prescription.getDoctorId(), prescription.getPrescriptionType(),
                prescription.getDiagnosis(), prescription.getPrescriptionDate(), prescription.getStatus(),
                prescription.getNotes(), prescription.getId());
    }
    
    @Override
    public int deleteById(Integer id) {
        String sql = "DELETE FROM prescription WHERE id = ?";
        return JdbcTemplate.update(sql, id);
    }
    
    @Override
    public Prescription findById(Integer id) {
        String sql = "SELECT * FROM prescription WHERE id = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToPrescription, id);
    }
    
    @Override
    public List<Prescription> findAll() {
        String sql = "SELECT * FROM prescription ORDER BY prescription_date DESC";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescription);
    }
    
    @Override
    public int count() {
        String sql = "SELECT COUNT(*) FROM prescription";
        return JdbcTemplate.queryForInt(sql);
    }
    
    @Override
    public List<Prescription> findByPage(int offset, int size) {
        String sql = "SELECT * FROM prescription ORDER BY prescription_date DESC LIMIT ?, ?";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescription, offset, size);
    }
    
    @Override
    public Prescription findByPrescriptionNo(String prescriptionNo) {
        String sql = "SELECT * FROM prescription WHERE prescription_no = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToPrescription, prescriptionNo);
    }
    
    @Override
    public List<Prescription> findByMedicalRecordId(Integer medicalRecordId) {
        String sql = "SELECT * FROM prescription WHERE medical_record_id = ? ORDER BY prescription_date DESC";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescription, medicalRecordId);
    }
    
    @Override
    public List<Prescription> findByPatientId(Integer patientId) {
        String sql = "SELECT * FROM prescription WHERE patient_id = ? ORDER BY prescription_date DESC";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescription, patientId);
    }
    
    @Override
    public List<Prescription> findByDoctorId(Integer doctorId) {
        String sql = "SELECT * FROM prescription WHERE doctor_id = ? ORDER BY prescription_date DESC";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescription, doctorId);
    }
    
    @Override
    public List<Prescription> findByPrescriptionType(String prescriptionType) {
        String sql = "SELECT * FROM prescription WHERE prescription_type = ? ORDER BY prescription_date DESC";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescription, prescriptionType);
    }
    
    @Override
    public List<Prescription> findByStatus(String status) {
        String sql = "SELECT * FROM prescription WHERE status = ? ORDER BY prescription_date DESC";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescription, status);
    }
    
    @Override
    public List<Prescription> findByDateRange(Date startDate, Date endDate) {
        String sql = "SELECT * FROM prescription WHERE prescription_date BETWEEN ? AND ? ORDER BY prescription_date DESC";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescription, startDate, endDate);
    }
    
    @Override
    public List<Prescription> findAllWithDetails() {
        String sql = "SELECT p.*, pt.name as patient_name, d.name as doctor_name, dept.dept_name as department_name " +
                    "FROM prescription p " +
                    "LEFT JOIN patient pt ON p.patient_id = pt.id " +
                    "LEFT JOIN doctor d ON p.doctor_id = d.id " +
                    "LEFT JOIN department dept ON d.dept_id = dept.id " +
                    "ORDER BY p.prescription_date DESC";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescriptionWithDetails);
    }
    
    @Override
    public List<Prescription> findByPatientIdWithDetails(Integer patientId) {
        String sql = "SELECT p.*, pt.name as patient_name, d.name as doctor_name, dept.dept_name as department_name " +
                    "FROM prescription p " +
                    "LEFT JOIN patient pt ON p.patient_id = pt.id " +
                    "LEFT JOIN doctor d ON p.doctor_id = d.id " +
                    "LEFT JOIN department dept ON d.dept_id = dept.id " +
                    "WHERE p.patient_id = ? ORDER BY p.prescription_date DESC";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescriptionWithDetails, patientId);
    }
    
    @Override
    public List<Prescription> findByDoctorIdWithDetails(Integer doctorId) {
        String sql = "SELECT p.*, pt.name as patient_name, d.name as doctor_name, dept.dept_name as department_name " +
                    "FROM prescription p " +
                    "LEFT JOIN patient pt ON p.patient_id = pt.id " +
                    "LEFT JOIN doctor d ON p.doctor_id = d.id " +
                    "LEFT JOIN department dept ON d.dept_id = dept.id " +
                    "WHERE p.doctor_id = ? ORDER BY p.prescription_date DESC";
        return JdbcTemplate.queryForList(sql, this::mapRowToPrescriptionWithDetails, doctorId);
    }
    
    @Override
    public Prescription findByPrescriptionNoWithDetails(String prescriptionNo) {
        String sql = "SELECT p.*, pt.name as patient_name, d.name as doctor_name, dept.dept_name as department_name " +
                    "FROM prescription p " +
                    "LEFT JOIN patient pt ON p.patient_id = pt.id " +
                    "LEFT JOIN doctor d ON p.doctor_id = d.id " +
                    "LEFT JOIN department dept ON d.dept_id = dept.id " +
                    "WHERE p.prescription_no = ?";
        return JdbcTemplate.queryForObject(sql, this::mapRowToPrescriptionWithDetails, prescriptionNo);
    }
    
    @Override
    public boolean existsByPrescriptionNo(String prescriptionNo) {
        String sql = "SELECT COUNT(*) FROM prescription WHERE prescription_no = ?";
        return JdbcTemplate.queryForInt(sql, prescriptionNo) > 0;
    }
    
    @Override
    public int updateStatus(Integer prescriptionId, String status) {
        String sql = "UPDATE prescription SET status = ? WHERE id = ?";
        return JdbcTemplate.update(sql, status, prescriptionId);
    }
    
    /**
     * 将ResultSet映射为Prescription对象
     */
    private Prescription mapRowToPrescription(ResultSet rs) throws SQLException {
        Prescription prescription = new Prescription();
        prescription.setId(rs.getInt("id"));
        prescription.setPrescriptionNo(rs.getString("prescription_no"));
        prescription.setMedicalRecordId(rs.getInt("medical_record_id"));
        prescription.setPatientId(rs.getInt("patient_id"));
        prescription.setDoctorId(rs.getInt("doctor_id"));
        prescription.setPrescriptionType(rs.getString("prescription_type"));
        prescription.setDiagnosis(rs.getString("diagnosis"));
        prescription.setPrescriptionDate(rs.getTimestamp("prescription_date"));
        prescription.setStatus(rs.getString("status"));
        prescription.setNotes(rs.getString("notes"));
        prescription.setCreateTime(rs.getTimestamp("create_time"));
        prescription.setUpdateTime(rs.getTimestamp("update_time"));
        return prescription;
    }
    
    /**
     * 将ResultSet映射为Prescription对象（包含关联信息）
     */
    private Prescription mapRowToPrescriptionWithDetails(ResultSet rs) throws SQLException {
        Prescription prescription = mapRowToPrescription(rs);
        prescription.setPatientName(rs.getString("patient_name"));
        prescription.setDoctorName(rs.getString("doctor_name"));
        prescription.setDepartmentName(rs.getString("department_name"));
        return prescription;
    }
}
