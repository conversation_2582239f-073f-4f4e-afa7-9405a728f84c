<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.service.DepartmentService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="com.hospital.entity.Department" %>
<%@ page import="java.util.List" %>
<%
    try {
        // 测试JSP服务获取
        DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
        List<Department> departments = departmentService.findAll();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>JSP服务测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>JSP服务测试页面</h1>
    
    <div class="success">
        <h2>✅ 测试成功！</h2>
        <p>JSP页面已成功使用JspServiceUtil获取服务，Spring依赖已完全移除！</p>
    </div>
    
    <h3>科室列表 (共 <%= departments.size() %> 个科室)</h3>
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>科室名称</th>
                <th>科室描述</th>
            </tr>
        </thead>
        <tbody>
            <% for (Department dept : departments) { %>
            <tr>
                <td><%= dept.getId() %></td>
                <td><%= dept.getDeptName() %></td>
                <td><%= dept.getDescription() != null ? dept.getDescription() : "无描述" %></td>
            </tr>
            <% } %>
        </tbody>
    </table>
    
    <h3>技术信息</h3>
    <ul>
        <li><strong>原生IoC容器</strong>: BeanFactory + ApplicationContext</li>
        <li><strong>服务获取方式</strong>: JspServiceUtil.getDepartmentService(application)</li>
        <li><strong>Spring依赖</strong>: 已完全移除</li>
        <li><strong>数据库连接</strong>: Druid连接池</li>
    </ul>
    
    <p><a href="${pageContext.request.contextPath}/">返回首页</a></p>
    
<%
    } catch (Exception e) {
%>
    <div class="error">
        <h2>❌ 测试失败</h2>
        <p>错误信息: <%= e.getMessage() %></p>
        <pre><%= e.toString() %></pre>
    </div>
<%
    }
%>
</body>
</html>
