<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>科室医生功能测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .nav-links a { display: inline-block; margin-right: 15px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
        .nav-links a:hover { background: #5a6fd8; }
        .test-link { display: inline-block; margin: 5px; padding: 8px 15px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; }
        .test-link:hover { background: #218838; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🏥 科室医生功能测试</h1>
            
            <div class="success">
                <h2>✅ 测试结果：科室医生页面NullPointerException已修复！</h2>
                <p>dept-doctors.jsp文件已修复Spring依赖问题和空指针异常。</p>
            </div>
            
            <div class="info">
                <h3>📋 管理员信息</h3>
                <p><strong>管理员用户名:</strong> <%= admin.getUsername() %></p>
                <p><strong>管理员姓名:</strong> <%= admin.getName() != null ? admin.getName() : "未设置" %></p>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
            </div>
            
            <div class="info">
                <h3>🔧 修复内容</h3>
                <ul>
                    <li>✅ dept-doctors.jsp - 科室医生查看页面</li>
                    <li>✅ 移除Spring WebApplicationContextUtils依赖</li>
                    <li>✅ 使用JspServiceUtil获取服务</li>
                    <li>✅ 添加空指针检查，防止NullPointerException</li>
                    <li>✅ 完善医生信息显示的安全性</li>
                </ul>
            </div>
            
            <div class="nav-links">
                <h3>🚀 功能测试</h3>
                <a href="${pageContext.request.contextPath}/admin/departments.jsp">科室管理</a>
                <a href="${pageContext.request.contextPath}/admin/dept-doctors.jsp?deptId=1" class="test-link">测试科室1医生</a>
                <a href="${pageContext.request.contextPath}/admin/dept-doctors.jsp?deptId=2" class="test-link">测试科室2医生</a>
                <a href="${pageContext.request.contextPath}/admin/dept-doctors.jsp?deptId=3" class="test-link">测试科室3医生</a>
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回管理员首页</a>
            </div>
            
            <div class="info">
                <h3>💡 使用说明</h3>
                <p>现在您可以：</p>
                <ol>
                    <li><strong>查看科室医生</strong> - 点击测试链接查看不同科室的医生列表</li>
                    <li><strong>医生信息展示</strong> - 查看医生的详细信息卡片</li>
                    <li><strong>医生操作</strong> - 查看详情、编辑医生信息</li>
                    <li><strong>添加医生</strong> - 为科室添加新的医生</li>
                    <li><strong>安全显示</strong> - 所有字段都有空值保护</li>
                </ol>
            </div>
            
            <div class="info">
                <h3>📋 科室医生页面功能</h3>
                <ul>
                    <li>🏢 <strong>科室信息</strong>: 显示科室名称、位置、联系电话</li>
                    <li>📊 <strong>统计信息</strong>: 显示在职医生总数</li>
                    <li>👨‍⚕️ <strong>医生卡片</strong>: 医生头像、姓名、职称、性别</li>
                    <li>📝 <strong>详细信息</strong>: 工号、年龄、手机、状态</li>
                    <li>🔧 <strong>操作按钮</strong>: 查看详情、编辑医生</li>
                    <li>➕ <strong>添加功能</strong>: 为科室添加新医生</li>
                    <li>🛡️ <strong>安全保护</strong>: 所有字段都有空值检查</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>🔧 技术修复详情</h3>
                <ul>
                    <li><strong>Spring依赖移除</strong>: 使用JspServiceUtil替代WebApplicationContextUtils</li>
                    <li><strong>空指针保护</strong>: 所有可能为null的字段都添加了检查</li>
                    <li><strong>安全字符串操作</strong>: doctor.getName().substring()添加了空值检查</li>
                    <li><strong>默认值处理</strong>: 为空值提供合理的默认显示</li>
                    <li><strong>服务获取</strong>: DoctorService和DepartmentService通过JspServiceUtil获取</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>🎯 测试步骤</h3>
                <ol>
                    <li><strong>点击测试链接</strong> - 选择上方任一科室测试链接</li>
                    <li><strong>查看页面加载</strong> - 确认页面正常显示，无NullPointerException</li>
                    <li><strong>检查科室信息</strong> - 确认科室名称、位置、电话正确显示</li>
                    <li><strong>查看医生列表</strong> - 确认医生卡片正确显示</li>
                    <li><strong>测试操作按钮</strong> - 点击"查看详情"和"编辑"按钮</li>
                    <li><strong>验证空值处理</strong> - 确认空字段显示为默认值而非错误</li>
                </ol>
            </div>
            
            <div class="info">
                <h3>🛡️ 安全改进</h3>
                <ul>
                    <li><strong>姓名安全</strong>: doctor.getName() != null && !doctor.getName().isEmpty()</li>
                    <li><strong>头像安全</strong>: 空姓名显示"?"而非异常</li>
                    <li><strong>字段保护</strong>: 所有字段都有null检查和默认值</li>
                    <li><strong>服务安全</strong>: 使用原生IoC容器，避免Spring依赖问题</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>📞 故障排除</h3>
                <ul>
                    <li><strong>NullPointerException</strong>: 已修复，所有字段都有空值保护</li>
                    <li><strong>Spring依赖</strong>: 已移除，使用JspServiceUtil</li>
                    <li><strong>服务获取</strong>: 使用原生IoC容器</li>
                    <li><strong>数据显示</strong>: 空值显示为友好的默认文本</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
