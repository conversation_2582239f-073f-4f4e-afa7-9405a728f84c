<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>患者注册 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #07080b 0%, #e6e2b3 100%);
            min-height: 100vh;
        }
        
        .register-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .register-header h2 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 1.8em;
        }
        
        .register-header p {
            color: #666;
            margin: 0;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            flex: 1;
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group textarea {
            resize: vertical;
            height: 80px;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        .required {
            color: red;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h2>患者注册</h2>
            <p>请填写完整的个人信息进行注册</p>
        </div>
        
        <% if (request.getAttribute("error") != null) { %>
            <div class="error">
                <%= request.getAttribute("error") %>
            </div>
        <% } %>
        
        <% if (request.getAttribute("success") != null) { %>
            <div class="success">
                <%= request.getAttribute("success") %>
            </div>
        <% } %>
        
        <form action="${pageContext.request.contextPath}/RegisterServlet" method="post">
            <div class="form-row">
                <div class="form-group">
                    <label for="name">姓名 <span class="required">*</span></label>
                    <input type="text" id="name" name="name" placeholder="请输入真实姓名" required>
                </div>
                <div class="form-group">
                    <label for="gender">性别 <span class="required">*</span></label>
                    <select id="gender" name="gender" required>
                        <option value="">请选择性别</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="age">年龄</label>
                    <input type="number" id="age" name="age" placeholder="请输入年龄" min="1" max="150">
                </div>
                <div class="form-group">
                    <label for="phone">联系电话 <span class="required">*</span></label>
                    <input type="tel" id="phone" name="phone" placeholder="请输入手机号码" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="idCard">身份证号</label>
                <input type="text" id="idCard" name="idCard" placeholder="请输入身份证号码" maxlength="18">
            </div>
            
            <div class="form-group">
                <label for="address">家庭住址</label>
                <textarea id="address" name="address" placeholder="请输入详细地址"></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="emergencyContact">紧急联系人</label>
                    <input type="text" id="emergencyContact" name="emergencyContact" placeholder="请输入紧急联系人姓名">
                </div>
                <div class="form-group">
                    <label for="emergencyPhone">紧急联系电话</label>
                    <input type="tel" id="emergencyPhone" name="emergencyPhone" placeholder="请输入紧急联系电话">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="password">登录密码 <span class="required">*</span></label>
                    <input type="password" id="password" name="password" placeholder="请输入登录密码" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">确认密码 <span class="required">*</span></label>
                    <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" required>
                </div>
            </div>
            
            <button type="submit" class="btn">注册</button>
        </form>
        
        <div class="links">
            <a href="${pageContext.request.contextPath}/login.jsp">已有账号？立即登录</a>
            <a href="${pageContext.request.contextPath}/index.jsp">返回首页</a>
        </div>
    </div>
    
    <script>
        // 密码确认验证
        document.getElementById('confirmPassword').addEventListener('blur', function() {
            var password = document.getElementById('password').value;
            var confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('两次输入的密码不一致');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
