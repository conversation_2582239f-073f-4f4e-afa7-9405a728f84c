package com.hospital.dao;

import com.hospital.entity.Registration;
import java.util.Date;
import java.util.List;

/**
 * 挂号DAO接口
 */
public interface RegistrationDao extends BaseDao<Registration> {
    
    /**
     根据挂号单号查询挂号信息
     */
    Registration findByRegNo(String regNo);
    
    /**
     根据患者ID查询挂号记录
     */
    List<Registration> findByPatientId(Integer patientId);
    
    /**
     根据医生ID查询挂号记录
     */
    List<Registration> findByDoctorId(Integer doctorId);
    
    /**
     根据科室ID查询挂号记录
     */
    List<Registration> findByDeptId(Integer deptId);
    
    /**
     根据挂号日期查询挂号记录
     */
    List<Registration> findByRegDate(Date regDate);
    
    /**
     查询挂号记录（包含关联信息）
     */
    List<Registration> findAllWithDetails();
    
    /**
     根据患者ID查询挂号记录（包含关联信息）
     */
    List<Registration> findByPatientIdWithDetails(Integer patientId);
    
    /**
     根据医生ID查询挂号记录（包含关联信息）
     */
    List<Registration> findByDoctorIdWithDetails(Integer doctorId);
    
    /**
     更新挂号状态
     */
    int updateStatus(Integer id, String status);
    
    /**
     统计医生工作量
     */
    int countByDoctorAndDateRange(Integer doctorId, Date startDate, Date endDate);

    /**
     统计指定科室的挂号数量
     */
    int countByDeptId(Integer deptId);

    /**
     统计指定科室本月的挂号数量
     */
    int countByDeptIdAndMonth(Integer deptId);


}
