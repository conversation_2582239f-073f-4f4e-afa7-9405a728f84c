package com.hospital.service.impl;

import com.hospital.dao.DepartmentDao;
import com.hospital.entity.Department;
import com.hospital.service.DepartmentService;
import com.hospital.util.TransactionManager;

import java.util.List;

/**
 科室服务实现类
 */
public class DepartmentServiceImpl implements DepartmentService {

    private DepartmentDao departmentDao;

    public void setDepartmentDao(DepartmentDao departmentDao) {
        this.departmentDao = departmentDao;
    }
    
    @Override
    public Department findById(Integer id) {
        return departmentDao.findById(id);
    }
    
    @Override
    public Department findByDeptName(String deptName) {
        return departmentDao.findByDeptName(deptName);
    }
    
    @Override
    public List<Department> findAll() {
        return departmentDao.findAll();
    }
    
    @Override
    public List<Department> findByDeptNameLike(String deptName) {
        return departmentDao.findByDeptNameLike(deptName);
    }
    
    @Override
    public boolean addDepartment(Department department) {
        // 检查科室名称是否已存在
        if (isDeptNameExists(department.getDeptName())) {
            return false;
        }
        
        return departmentDao.insert(department) > 0;
    }
    
    @Override
    public boolean updateDepartment(Department department) {
        return departmentDao.update(department) > 0;
    }
    
    @Override
    public boolean deleteDepartment(Integer id) {
        return departmentDao.deleteById(id) > 0;
    }
    
    @Override
    public List<Department> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return departmentDao.findByPage(offset, size);
    }
    
    @Override
    public int count() {
        return departmentDao.count();
    }
    
    @Override
    public boolean isDeptNameExists(String deptName) {
        return departmentDao.findByDeptName(deptName) != null;
    }
}
