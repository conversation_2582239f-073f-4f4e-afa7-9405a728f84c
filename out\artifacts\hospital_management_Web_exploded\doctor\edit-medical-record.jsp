<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%@ page import="com.hospital.entity.MedicalRecord" %>
<%@ page import="com.hospital.service.MedicalRecordService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Doctor doctor = (Doctor) session.getAttribute("doctor");
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    String idStr = request.getParameter("id");
    if (idStr == null) {
        response.sendRedirect(request.getContextPath() + "/doctor/medical-records.jsp");
        return;
    }
    
    // 获取服务
    MedicalRecordService medicalRecordService = JspServiceUtil.getMedicalRecordService(application);
    
    // 获取就诊记录
    MedicalRecord medicalRecord = null;
    try {
        medicalRecord = medicalRecordService.findById(Integer.parseInt(idStr));
        if (medicalRecord == null) {
            response.sendRedirect(request.getContextPath() + "/doctor/medical-records.jsp?error=记录不存在");
            return;
        }
        
        // 验证权限：只能编辑自己创建的就诊记录
        if (!medicalRecord.getDoctorId().equals(doctor.getId())) {
            response.sendRedirect(request.getContextPath() + "/doctor/medical-records.jsp?error=无权限编辑此记录");
            return;
        }
    } catch (Exception e) {
        response.sendRedirect(request.getContextPath() + "/doctor/medical-records.jsp?error=记录ID无效");
        return;
    }
    
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑就诊记录 - 医院管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .patient-info {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .patient-info h3 {
            margin: 0 0 10px 0;
            font-size: 1.3em;
        }
        
        .patient-info p {
            margin: 5px 0;
            opacity: 0.9;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin-right: 15px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .required {
            color: #dc3545;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/doctor/medical-records.jsp">返回就诊记录</a>
                <a href="${pageContext.request.contextPath}/doctor/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>编辑就诊记录</h2>
            
            <div class="patient-info">
                <h3>👤 就诊记录信息</h3>
                <p><strong>记录ID：</strong><%= medicalRecord.getId() %></p>
                <p><strong>挂号ID：</strong><%= medicalRecord.getRegId() %></p>
                <p><strong>患者ID：</strong><%= medicalRecord.getPatientId() %></p>
                <p><strong>当前医生：</strong><%= doctor.getName() %></p>
                <p><strong>就诊时间：</strong><%= medicalRecord.getVisitDate() != null ? new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm").format(medicalRecord.getVisitDate()) : "未设置" %></p>
                <p><strong>编辑时间：</strong><%= new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()) %></p>
            </div>
            
            <% String error = (String) request.getAttribute("error"); %>
            <% if (error != null) { %>
                <div class="alert alert-danger">
                    <strong>错误：</strong><%= error %>
                </div>
            <% } %>
            
            <form action="${pageContext.request.contextPath}/MedicalRecordServlet" method="post">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="id" value="<%= medicalRecord.getId() %>">
                
                <div class="form-group">
                    <label for="chiefComplaint">主诉 <span class="required">*</span></label>
                    <textarea id="chiefComplaint" name="chiefComplaint" required 
                              placeholder="请输入患者的主诉..."><%= medicalRecord.getChiefComplaint() != null ? medicalRecord.getChiefComplaint() : "" %></textarea>
                </div>
                
                <div class="form-group">
                    <label for="presentIllness">现病史</label>
                    <textarea id="presentIllness" name="presentIllness" 
                              placeholder="请详细描述患者的现病史..."><%= medicalRecord.getPresentIllness() != null ? medicalRecord.getPresentIllness() : "" %></textarea>
                </div>
                
                <div class="form-group">
                    <label for="physicalExam">体格检查</label>
                    <textarea id="physicalExam" name="physicalExam" 
                              placeholder="请输入体格检查结果..."><%= medicalRecord.getPhysicalExam() != null ? medicalRecord.getPhysicalExam() : "" %></textarea>
                </div>
                
                <div class="form-group">
                    <label for="diagnosis">诊断结果 <span class="required">*</span></label>
                    <textarea id="diagnosis" name="diagnosis" required 
                              placeholder="请输入诊断结果..."><%= medicalRecord.getDiagnosis() != null ? medicalRecord.getDiagnosis() : "" %></textarea>
                </div>
                
                <div class="form-group">
                    <label for="treatmentPlan">治疗方案</label>
                    <textarea id="treatmentPlan" name="treatmentPlan" 
                              placeholder="请输入治疗方案和处理措施..."><%= medicalRecord.getTreatmentPlan() != null ? medicalRecord.getTreatmentPlan() : "" %></textarea>
                </div>
                
                <div class="form-group">
                    <label for="prescription">处方药物</label>
                    <textarea id="prescription" name="prescription" 
                              placeholder="请输入处方药物和用法用量..."><%= medicalRecord.getPrescription() != null ? medicalRecord.getPrescription() : "" %></textarea>
                </div>
                
                <div class="form-group">
                    <label for="advice">医嘱</label>
                    <textarea id="advice" name="advice" 
                              placeholder="请输入医嘱和注意事项..."><%= medicalRecord.getAdvice() != null ? medicalRecord.getAdvice() : "" %></textarea>
                </div>
                
                <div class="form-group">
                    <label for="nextVisitDate">下次复诊日期</label>
                    <input type="date" id="nextVisitDate" name="nextVisitDate" 
                           value="<%= medicalRecord.getNextVisitDate() != null ? dateFormat.format(medicalRecord.getNextVisitDate()) : "" %>"
                           min="<%= dateFormat.format(new java.util.Date()) %>">
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary">保存修改</button>
                    <a href="${pageContext.request.contextPath}/doctor/view-medical-record.jsp?id=<%= medicalRecord.getId() %>" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
