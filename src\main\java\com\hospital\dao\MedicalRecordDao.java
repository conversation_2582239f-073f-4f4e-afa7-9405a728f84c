package com.hospital.dao;

import com.hospital.entity.MedicalRecord;
import java.util.Date;
import java.util.List;

/**
 就诊记录DAO接口
 */
public interface MedicalRecordDao extends BaseDao<MedicalRecord> {
    
    /**
     根据挂号ID查询就诊记录
     */
    MedicalRecord findByRegId(Integer regId);
    
    /**
     根据患者ID查询就诊记录
     */
    List<MedicalRecord> findByPatientId(Integer patientId);
    
    /**
     根据医生ID查询就诊记录
     */
    List<MedicalRecord> findByDoctorId(Integer doctorId);
    
    /**
     根据就诊日期查询就诊记录
     */
    List<MedicalRecord> findByVisitDate(Date visitDate);
    
    /**
     查询就诊记录（包含关联信息）
     */
    List<MedicalRecord> findAllWithDetails();
    
    /**
     根据患者ID查询就诊记录（包含关联信息）
     */
    List<MedicalRecord> findByPatientIdWithDetails(Integer patientId);
    
    /**
     根据医生ID查询就诊记录（包含关联信息）
     */
    List<MedicalRecord> findByDoctorIdWithDetails(Integer doctorId);
    
    /**
     根据挂号ID查询就诊记录（包含关联信息）
     */
    MedicalRecord findByRegIdWithDetails(Integer regId);
    
    /**
     统计医生就诊数量
     */
    int countByDoctorAndDateRange(Integer doctorId, Date startDate, Date endDate);
    
    /**
     统计患者就诊数量
     */
    int countByPatientId(Integer patientId);
}
