package com.hospital.servlet;

import com.hospital.entity.Patient;
import com.hospital.service.PatientService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 患者注册控制器
 */
@WebServlet("/RegisterServlet")
public class RegisterServlet extends HttpServlet {

    private PatientService patientService;

    @Override
    public void init() throws ServletException {
        BeanFactory beanFactory = BeanFactory.getInstance();
        patientService = beanFactory.getApplicationContext().getBean(PatientService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        
        // 获取表单参数
        String name = request.getParameter("name");
        String gender = request.getParameter("gender");
        String ageStr = request.getParameter("age");
        String phone = request.getParameter("phone");
        String idCard = request.getParameter("idCard");
        String address = request.getParameter("address");
        String emergencyContact = request.getParameter("emergencyContact");
        String emergencyPhone = request.getParameter("emergencyPhone");
        String password = request.getParameter("password");
        String confirmPassword = request.getParameter("confirmPassword");
        
        // 参数验证
        if (name == null || name.trim().isEmpty() ||
            gender == null || gender.trim().isEmpty() ||
            phone == null || phone.trim().isEmpty() ||
            password == null || password.trim().isEmpty() ||
            confirmPassword == null || confirmPassword.trim().isEmpty()) {
            
            request.setAttribute("error", "请填写所有必填项");
            request.getRequestDispatcher("/register.jsp").forward(request, response);
            return;
        }
        
        // 密码确认验证
        if (!password.equals(confirmPassword)) {
            request.setAttribute("error", "两次输入的密码不一致");
            request.getRequestDispatcher("/register.jsp").forward(request, response);
            return;
        }
        
        try {
            // 创建患者对象
            Patient patient = new Patient();
            patient.setName(name.trim());
            patient.setGender(gender);
            patient.setPhone(phone.trim());
            patient.setPassword(password);
            
            // 处理可选字段
            if (ageStr != null && !ageStr.trim().isEmpty()) {
                try {
                    patient.setAge(Integer.parseInt(ageStr.trim()));
                } catch (NumberFormatException e) {
                    request.setAttribute("error", "年龄格式不正确");
                    request.getRequestDispatcher("/register.jsp").forward(request, response);
                    return;
                }
            }
            
            if (idCard != null && !idCard.trim().isEmpty()) {
                patient.setIdCard(idCard.trim());
            }
            
            if (address != null && !address.trim().isEmpty()) {
                patient.setAddress(address.trim());
            }
            
            if (emergencyContact != null && !emergencyContact.trim().isEmpty()) {
                patient.setEmergencyContact(emergencyContact.trim());
            }
            
            if (emergencyPhone != null && !emergencyPhone.trim().isEmpty()) {
                patient.setEmergencyPhone(emergencyPhone.trim());
            }
            
            // 生成患者编号
            patient.setPatientNo(patientService.generatePatientNo());
            
            // 注册患者
            boolean success = patientService.register(patient);
            
            if (success) {
                request.setAttribute("success", "注册成功！您的患者编号是：" + patient.getPatientNo() + "，请妥善保管。");
                request.getRequestDispatcher("/register.jsp").forward(request, response);
            } else {
                request.setAttribute("error", "注册失败，可能是身份证号或手机号已存在");
                request.getRequestDispatcher("/register.jsp").forward(request, response);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "系统错误：" + e.getMessage());
            request.getRequestDispatcher("/register.jsp").forward(request, response);
        }
    }
}
