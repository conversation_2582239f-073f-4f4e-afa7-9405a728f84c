<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>路径测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; font-size: 1.2em; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .test-links { margin: 20px 0; }
        .test-links a { display: block; margin: 10px 0; padding: 15px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; text-align: center; }
        .test-links a:hover { background: #5a6fd8; }
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🔧 路径测试页面</h1>
            
            <div class="success">
                ✅ 页面访问成功！
            </div>
            
            <div class="info">
                <p>如果您能看到这个页面，说明基本的JSP访问是正常的。</p>
            </div>
            
            <div class="debug-info">
                <h3>📋 当前路径信息</h3>
                <p><strong>当前时间:</strong> <%= new java.util.Date() %></p>
                <p><strong>请求URL:</strong> <%= request.getRequestURL() %></p>
                <p><strong>请求URI:</strong> <%= request.getRequestURI() %></p>
                <p><strong>Context Path:</strong> <%= request.getContextPath() %></p>
                <p><strong>Servlet Path:</strong> <%= request.getServletPath() %></p>
                <p><strong>Server Name:</strong> <%= request.getServerName() %></p>
                <p><strong>Server Port:</strong> <%= request.getServerPort() %></p>
            </div>
            
            <div class="test-links">
                <h3>🚀 测试链接</h3>
                
                <a href="<%= request.getContextPath() %>/admin/debug-edit-department.jsp">
                    🔍 调试页面 - 检查文件存在性
                </a>
                
                <a href="<%= request.getContextPath() %>/admin/fix-edit-department.jsp?id=1">
                    🛠️ 修复版编辑页面 (ID=1)
                </a>
                
                <a href="<%= request.getContextPath() %>/admin/edit-department.jsp?id=1">
                    📝 原版编辑页面 (ID=1)
                </a>
                
                <a href="<%= request.getContextPath() %>/admin/departments.jsp">
                    📋 科室管理页面
                </a>
                
                <a href="<%= request.getContextPath() %>/admin/index.jsp">
                    🏠 管理员首页
                </a>
            </div>
            
            <div class="info">
                <h3>💡 使用说明</h3>
                <ol>
                    <li><strong>先点击"调试页面"</strong> - 检查文件是否正确部署</li>
                    <li><strong>再点击"修复版编辑页面"</strong> - 测试修复后的功能</li>
                    <li><strong>如果都正常，点击"科室管理页面"</strong> - 进行完整测试</li>
                </ol>
            </div>
            
            <div class="info">
                <h3>🔧 如果仍有问题</h3>
                <p>请检查以下几点：</p>
                <ul>
                    <li>Tomcat服务器是否正常运行</li>
                    <li>IDE的Application context是否设置为 "/"</li>
                    <li>浏览器缓存是否已清理</li>
                    <li>是否使用了正确的URL格式</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
