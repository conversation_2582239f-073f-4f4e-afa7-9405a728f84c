package com.hospital.filter;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 登录验证过滤器
 该过滤器用于验证用户的登录状态，确保只有已登录的用户才能访问受保护的资源。
 过滤器会拦截指定路径的请求，检查Session中是否存在相应的用户信息。
 拦截路径：
 patient 患者相关页面和功能
 doctor 医生相关页面和功能
 admin 管理员相关页面和功能
 验证逻辑：
 1. 检查请求路径是否需要登录验证
 2. 根据访问路径判断用户类型
 3. 检查Session中是否存在对应的用户信息
 4. 未登录用户重定向到登录页面
 5. 已登录用户继续访问请求的资源
 排除路径：
 登录、注册相关页面
 静态资源（CSS、JS、图片等）
 公共页面
 */
@WebFilter(filterName = "LoginFilter", urlPatterns = {"/patient/*", "/doctor/*", "/admin/*"})
public class LoginFilter implements Filter {

    /**
     过滤器初始化方法
     在过滤器实例创建时调用，用于执行初始化操作
     */

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        System.out.println("LoginFilter初始化");
    }
    
    /**
     过滤器核心方法
     对每个HTTP请求进行登录状态验证，确保用户访问权限的安全性
     处理流程：
     1. 设置请求和响应的字符编码
     2. 解析请求路径，判断是否需要登录验证
     3. 检查排除路径，跳过不需要验证的请求
     4. 根据访问路径确定用户类型并验证登录状态
     5. 未登录用户重定向到登录页面
     6. 已登录用户继续处理请求
     @param request 请求对象，包含客户端请求信息
     @param response 响应对象，用于向客户端发送响应
     @param chain 过滤器链，用于继续处理请求
     @throws IOException 当I/O操作失败时抛出异常
     @throws ServletException 当Servlet处理失败时抛出异常
     */

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        // 类型转换，获取HTTP特定的请求和响应对象
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 设置字符编码，防止中文乱码
        httpRequest.setCharacterEncoding("UTF-8");
        httpResponse.setCharacterEncoding("UTF-8");
        httpResponse.setContentType("text/html;charset=UTF-8");

        // 解析请求路径
        String uri = httpRequest.getRequestURI();           // 完整的请求URI
        String contextPath = httpRequest.getContextPath();  // 应用上下文路径
        String path = uri.substring(contextPath.length());  // 去除上下文路径后的相对路径

        // 定义不需要登录验证的路径
        // 包括登录注册页面、静态资源、公共页面等
        String[] excludePaths = {
            "/login.jsp", "/register.jsp", "/index.jsp",
            "/LoginServlet", "/RegisterServlet", "/LogoutServlet",
            "/css/", "/js/", "/images/", "/static/"
        };

        // 检查当前请求是否需要登录验证
        boolean needLogin = true;
        for (String excludePath : excludePaths) {
            if (path.startsWith(excludePath)) {
                needLogin = false;
                break;
            }
        }
        
        // 如果需要登录验证，则检查用户登录状态
        if (needLogin) {
            HttpSession session = httpRequest.getSession();
            Object user = null;

            // 根据访问路径判断用户类型并获取对应的Session属性
            // 不同类型的用户在Session中使用不同的属性名存储
            if (path.startsWith("/patient/")) {
                // 患者访问患者相关页面，检查患者登录状态
                user = session.getAttribute("patient");
            } else if (path.startsWith("/doctor/")) {
                // 医生访问医生相关页面，检查医生登录状态
                user = session.getAttribute("doctor");
            } else if (path.startsWith("/admin/")) {
                // 管理员访问管理员相关页面，检查管理员登录状态
                user = session.getAttribute("admin");
            }

            // 如果Session中没有对应的用户信息，说明用户未登录
            if (user == null) {
                // 重定向到登录页面，阻止访问受保护的资源
                httpResponse.sendRedirect(contextPath + "/login.jsp");
                return; // 终止请求处理，不继续执行过滤器链
            }
        }

        // 验证通过或不需要验证，继续执行过滤器链
        // 将请求传递给下一个过滤器或目标资源
        chain.doFilter(request, response);
    }

    /**
     过滤器销毁方法
     在过滤器实例销毁时调用，用于清理资源
     通常在应用程序关闭时执行
     */
    @Override
    public void destroy() {
        System.out.println("LoginFilter销毁");
    }
}
