<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%@ page import="com.hospital.service.DoctorService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    // 获取服务
    DoctorService doctorService = JspServiceUtil.getDoctorService(application);
    
    List<Doctor> doctors = doctorService.findAllWithDept();
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医生管理 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-box input,
        .search-box select {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1em;
        }
        
        .search-box input:focus,
        .search-box select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            font-size: 1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .doctor-info {
            font-weight: bold;
            color: #333;
        }
        
        .doctor-no {
            color: #667eea;
            font-family: monospace;
        }
        
        .contact-info {
            font-size: 0.9em;
            color: #666;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .speciality {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 3em;
            margin-bottom: 20px;
            color: #ccc;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>医生管理</h2>
            
            <% if (request.getParameter("success") != null) { %>
                <div class="alert alert-success">
                    <%= request.getParameter("success") %>
                </div>
            <% } %>
            
            <% if (request.getParameter("error") != null) { %>
                <div class="alert alert-error">
                    <%= request.getParameter("error") %>
                </div>
            <% } %>
            
            <div class="toolbar">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="搜索医生姓名或工号..." onkeyup="searchDoctors()">
                    <select id="statusFilter" onchange="filterDoctors()">
                        <option value="">全部状态</option>
                        <option value="在职">在职</option>
                        <option value="离职">离职</option>
                    </select>
                    <button class="btn btn-primary" onclick="searchDoctors()">搜索</button>
                </div>
                <div>
                    <a href="${pageContext.request.contextPath}/admin/add-doctor.jsp" class="btn btn-success">添加医生</a>
                    <span style="color: #666; margin-left: 15px;">共 <%= doctors.size() %> 名医生</span>
                </div>
            </div>
            
            <% if (doctors != null && !doctors.isEmpty()) { %>
                <table class="table" id="doctorTable">
                    <thead>
                        <tr>
                            <th>工号</th>
                            <th>医生信息</th>
                            <th>科室</th>
                            <th>职称</th>
                            <th>联系方式</th>
                            <th>专业特长</th>
                            <th>状态</th>
                            <th>入职时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% for (Doctor doctor : doctors) { %>
                            <tr data-name="<%= doctor.getName().toLowerCase() %>" 
                                data-no="<%= doctor.getDoctorNo().toLowerCase() %>"
                                data-status="<%= doctor.getStatus() %>">
                                <td class="doctor-no"><%= doctor.getDoctorNo() %></td>
                                <td>
                                    <div class="doctor-info"><%= doctor.getName() %></div>
                                    <div class="contact-info">
                                        <%= doctor.getGender() %>
                                        <% if (doctor.getAge() != null) { %>
                                            | <%= doctor.getAge() %>岁
                                        <% } %>
                                    </div>
                                </td>
                                <td><%= doctor.getDeptName() != null ? doctor.getDeptName() : "未分配" %></td>
                                <td><%= doctor.getTitle() != null ? doctor.getTitle() : "-" %></td>
                                <td>
                                    <div><%= doctor.getPhone() != null ? doctor.getPhone() : "-" %></div>
                                    <% if (doctor.getEmail() != null && !doctor.getEmail().trim().isEmpty()) { %>
                                        <div class="contact-info"><%= doctor.getEmail() %></div>
                                    <% } %>
                                </td>
                                <td class="speciality" title="<%= doctor.getSpeciality() != null ? doctor.getSpeciality() : "" %>">
                                    <%= doctor.getSpeciality() != null ? doctor.getSpeciality() : "-" %>
                                </td>
                                <td>
                                    <% 
                                        String status = doctor.getStatus();
                                        String statusClass = "在职".equals(status) ? "status-active" : "status-inactive";
                                    %>
                                    <span class="status <%= statusClass %>"><%= status %></span>
                                </td>
                                <td>
                                    <%= doctor.getCreateTime() != null ? dateFormat.format(doctor.getCreateTime()) : "-" %>
                                </td>
                                <td>
                                    <a href="${pageContext.request.contextPath}/admin/view-doctor.jsp?id=<%= doctor.getId() %>" 
                                       class="btn btn-info" style="font-size: 0.8em; padding: 4px 8px;">查看详情</a>
                                </td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            <% } else { %>
                <div class="empty-state">
                    <div style="font-size: 3em; margin-bottom: 20px;">👨‍⚕️</div>
                    <h3>暂无医生数据</h3>
                    <p>系统中还没有医生信息。</p>
                </div>
            <% } %>
        </div>
    </div>
    
    <script>
        function searchDoctors() {
            filterDoctors();
        }
        
        function filterDoctors() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const table = document.getElementById('doctorTable');
            
            if (!table) return;
            
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const name = row.getAttribute('data-name');
                const doctorNo = row.getAttribute('data-no');
                const status = row.getAttribute('data-status');
                
                let showRow = true;
                
                // 搜索过滤
                if (searchTerm && !name.includes(searchTerm) && !doctorNo.includes(searchTerm)) {
                    showRow = false;
                }
                
                // 状态过滤
                if (statusFilter && status !== statusFilter) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
            }
        }
        
        // 回车键搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchDoctors();
            }
        });
    </script>
</body>
</html>
