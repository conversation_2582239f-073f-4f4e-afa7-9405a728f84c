<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>添加医生页面测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: green; font-weight: bold; font-size: 1.2em; }
        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 添加医生页面连通性测试</h1>
        
        <div class="success">
            ✅ 页面访问成功！
        </div>
        
        <div class="info">
            <h3>📋 页面信息</h3>
            <ul>
                <li><strong>当前时间</strong>: <%= new java.util.Date() %></li>
                <li><strong>请求URI</strong>: <%= request.getRequestURI() %></li>
                <li><strong>Context Path</strong>: <%= request.getContextPath() %></li>
                <li><strong>服务器信息</strong>: <%= application.getServerInfo() %></li>
                <li><strong>Session ID</strong>: <%= session.getId() %></li>
            </ul>
        </div>
        
        <div class="info">
            <h3>🔧 测试结果</h3>
            <p>如果您能看到这个页面，说明：</p>
            <ul>
                <li>✅ JSP页面路径正确</li>
                <li>✅ Tomcat服务器正常运行</li>
                <li>✅ 应用部署成功</li>
                <li>✅ 文件访问权限正常</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <a href="${pageContext.request.contextPath}/admin/add-doctor.jsp" class="btn">访问完整添加医生页面</a>
            <a href="${pageContext.request.contextPath}/admin/test-add-doctor.jsp" class="btn">访问功能测试页面</a>
            <a href="${pageContext.request.contextPath}/admin/doctors.jsp" class="btn">返回医生管理</a>
        </div>
        
        <div class="info">
            <h3>🚀 下一步操作</h3>
            <p>如果这个页面正常显示，请点击上面的"访问完整添加医生页面"按钮测试完整功能。</p>
            <p>如果仍然出现404错误，请检查：</p>
            <ol>
                <li>Tomcat服务器是否正常运行</li>
                <li>应用是否正确部署</li>
                <li>浏览器缓存是否已清理</li>
            </ol>
        </div>
    </div>
</body>
</html>
