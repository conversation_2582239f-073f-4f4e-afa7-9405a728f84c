<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.hospital.entity.*" %>
<%@ page import="com.hospital.service.*" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.*" %>
<%
    // 检查医生登录状态
    Doctor doctor = (Doctor) session.getAttribute("doctor");
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/doctor/login.jsp");
        return;
    }
    
    // 获取就诊记录ID
    String medicalRecordIdStr = request.getParameter("medicalRecordId");
    if (medicalRecordIdStr == null || medicalRecordIdStr.isEmpty()) {
        response.sendRedirect(request.getContextPath() + "/doctor/medical-records.jsp");
        return;
    }
    
    Integer medicalRecordId = Integer.parseInt(medicalRecordIdStr);
    
    // 获取就诊记录信息
    MedicalRecordService medicalRecordService = JspServiceUtil.getMedicalRecordService(application);
    MedicalRecord medicalRecord = medicalRecordService.findById(medicalRecordId);
    
    if (medicalRecord == null || !medicalRecord.getDoctorId().equals(doctor.getId())) {
        response.sendRedirect(request.getContextPath() + "/doctor/medical-records.jsp");
        return;
    }
    
    // 获取药品分类和药品信息
    MedicineCategoryService medicineCategoryService = JspServiceUtil.getMedicineCategoryService(application);
    MedicineService medicineService = JspServiceUtil.getMedicineService(application);
    
    List<MedicineCategory> chineseCategories = medicineCategoryService.findByCategoryType("中药");
    List<MedicineCategory> westernCategories = medicineCategoryService.findByCategoryType("西药");
    List<Medicine> chineseMedicines = medicineService.findByMedicineTypeWithCategory("中药");
    List<Medicine> westernMedicines = medicineService.findByMedicineTypeWithCategory("西药");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开具处方 - 医院管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
        }
        
        .nav-links a {
            color: #666;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            background: #667eea;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .patient-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .info-label {
            font-weight: 500;
            color: #666;
        }
        
        .info-value {
            color: #333;
        }
        
        .prescription-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .tab-button {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .medicine-search {
            margin-bottom: 20px;
        }
        
        .search-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .medicine-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .medicine-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .medicine-item:hover {
            background-color: #f8f9fa;
        }
        
        .medicine-item:last-child {
            border-bottom: none;
        }
        
        .medicine-name {
            font-weight: 500;
            color: #333;
        }
        
        .medicine-spec {
            color: #666;
            font-size: 14px;
            margin-top: 4px;
        }
        
        .medicine-price {
            color: #e74c3c;
            font-weight: 500;
            float: right;
        }
        
        .selected-medicines {
            margin-top: 20px;
        }
        
        .selected-medicine {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .medicine-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .remove-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .dosage-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        
        .input-group {
            display: flex;
            flex-direction: column;
        }
        
        .input-group label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .input-group input, .input-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .chinese-attrs {
            margin-top: 20px;
            padding: 20px;
            background: #fff8dc;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        
        .chinese-attrs h4 {
            color: #8b4513;
            margin-bottom: 15px;
        }
        
        .attrs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/doctor/medical-records.jsp">返回就诊记录</a>
                <a href="${pageContext.request.contextPath}/doctor/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>开具处方</h2>
            
            <div class="patient-info">
                <h3>患者信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">患者姓名：</span>
                        <span class="info-value"><%= medicalRecord.getPatientName() != null ? medicalRecord.getPatientName() : "未知" %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">就诊日期：</span>
                        <span class="info-value"><%= new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm").format(medicalRecord.getVisitDate()) %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">诊断：</span>
                        <span class="info-value"><%= medicalRecord.getDiagnosis() != null ? medicalRecord.getDiagnosis() : "无" %></span>
                    </div>
                </div>
            </div>
            
            <% if (request.getAttribute("error") != null) { %>
                <div class="alert alert-error">
                    <%= request.getAttribute("error") %>
                </div>
            <% } %>
            
            <% if (request.getAttribute("success") != null) { %>
                <div class="alert alert-success">
                    <%= request.getAttribute("success") %>
                </div>
            <% } %>
            
            <form id="prescriptionForm" action="${pageContext.request.contextPath}/PrescriptionServlet" method="post">
                <input type="hidden" name="action" value="add">
                <input type="hidden" name="medicalRecordId" value="<%= medicalRecordId %>">
                <input type="hidden" name="patientId" value="<%= medicalRecord.getPatientId() %>">
                <input type="hidden" name="doctorId" value="<%= doctor.getId() %>">
                <input type="hidden" name="diagnosis" value="<%= medicalRecord.getDiagnosis() != null ? medicalRecord.getDiagnosis() : "" %>">
                
                <div class="prescription-tabs">
                    <button type="button" class="tab-button active" onclick="switchTab('western')">西药处方</button>
                    <button type="button" class="tab-button" onclick="switchTab('chinese')">中药处方</button>
                    <button type="button" class="tab-button" onclick="switchTab('mixed')">中西药混合</button>
                </div>
                
                <div id="western-tab" class="tab-content active">
                    <h4>西药处方</h4>
                    <div class="medicine-search">
                        <input type="text" class="search-input" placeholder="搜索西药..." onkeyup="searchMedicine('western', this.value)">
                    </div>
                    <div id="western-medicine-list" class="medicine-list">
                        <% for (Medicine medicine : westernMedicines) { %>
                            <div class="medicine-item" onclick="selectMedicine('western', <%= medicine.getId() %>, '<%= medicine.getMedicineName() %>', '<%= medicine.getSpecification() %>', <%= medicine.getPrice() %>, '<%= medicine.getUnit() %>')">
                                <div class="medicine-name"><%= medicine.getMedicineName() %></div>
                                <div class="medicine-spec">规格：<%= medicine.getSpecification() %> | 库存：<%= medicine.getStock() %><%= medicine.getUnit() %></div>
                                <div class="medicine-price">￥<%= medicine.getPrice() %>/<%= medicine.getUnit() %></div>
                            </div>
                        <% } %>
                    </div>
                    <div id="western-selected" class="selected-medicines">
                        <h4>已选择的西药</h4>
                    </div>
                </div>
                
                <div id="chinese-tab" class="tab-content">
                    <h4>中药处方</h4>
                    <div class="medicine-search">
                        <input type="text" class="search-input" placeholder="搜索中药..." onkeyup="searchMedicine('chinese', this.value)">
                    </div>
                    <div id="chinese-medicine-list" class="medicine-list">
                        <% for (Medicine medicine : chineseMedicines) { %>
                            <div class="medicine-item" onclick="selectMedicine('chinese', <%= medicine.getId() %>, '<%= medicine.getMedicineName() %>', '<%= medicine.getSpecification() %>', <%= medicine.getPrice() %>, '<%= medicine.getUnit() %>')">
                                <div class="medicine-name"><%= medicine.getMedicineName() %></div>
                                <div class="medicine-spec">规格：<%= medicine.getSpecification() %> | 库存：<%= medicine.getStock() %><%= medicine.getUnit() %></div>
                                <div class="medicine-price">￥<%= medicine.getPrice() %>/<%= medicine.getUnit() %></div>
                            </div>
                        <% } %>
                    </div>
                    <div id="chinese-selected" class="selected-medicines">
                        <h4>已选择的中药</h4>
                    </div>
                    
                    <div class="chinese-attrs">
                        <h4>中药煎煮方法</h4>
                        <div class="attrs-grid">
                            <div class="input-group">
                                <label>煎煮方法</label>
                                <textarea name="decoctionMethod" placeholder="请输入煎煮方法..." rows="3">先煎30分钟，后下其他药材煎煮15分钟</textarea>
                            </div>
                            <div class="input-group">
                                <label>煎煮次数</label>
                                <select name="decoctionTimes">
                                    <option value="1">1次</option>
                                    <option value="2" selected>2次</option>
                                    <option value="3">3次</option>
                                </select>
                            </div>
                            <div class="input-group">
                                <label>服用方法</label>
                                <input type="text" name="chineseUsageMethod" placeholder="如：温服" value="温服">
                            </div>
                            <div class="input-group">
                                <label>每次用量</label>
                                <input type="text" name="dosagePerTime" placeholder="如：150ml" value="150ml">
                            </div>
                            <div class="input-group">
                                <label>服用频次</label>
                                <input type="text" name="chineseFrequency" placeholder="如：一日2次" value="一日2次">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="mixed-tab" class="tab-content">
                    <h4>中西药混合处方</h4>
                    <p>请分别在西药和中药标签页中选择需要的药品</p>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-success">开具处方</button>
                    <a href="${pageContext.request.contextPath}/doctor/medical-records.jsp" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>

    <script>
        let selectedMedicines = {
            western: [],
            chinese: []
        };
        
        let currentTab = 'western';
        
        function switchTab(tab) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有按钮的active类
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tab + '-tab').classList.add('active');
            event.target.classList.add('active');
            
            currentTab = tab;
            
            // 更新表单的处方类型
            updatePrescriptionType();
        }
        
        function updatePrescriptionType() {
            let prescriptionType = '';
            if (currentTab === 'western') {
                prescriptionType = '西药处方';
            } else if (currentTab === 'chinese') {
                prescriptionType = '中药处方';
            } else if (currentTab === 'mixed') {
                prescriptionType = '中西药混合';
            }
            
            // 添加隐藏字段或更新现有字段
            let typeInput = document.querySelector('input[name="prescriptionType"]');
            if (!typeInput) {
                typeInput = document.createElement('input');
                typeInput.type = 'hidden';
                typeInput.name = 'prescriptionType';
                document.getElementById('prescriptionForm').appendChild(typeInput);
            }
            typeInput.value = prescriptionType;
        }
        
        function searchMedicine(type, keyword) {
            const medicineList = document.getElementById(type + '-medicine-list');
            const items = medicineList.querySelectorAll('.medicine-item');
            
            items.forEach(item => {
                const medicineName = item.querySelector('.medicine-name').textContent;
                if (medicineName.toLowerCase().includes(keyword.toLowerCase())) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }
        
        function selectMedicine(type, id, name, spec, price, unit) {
            // 检查是否已经选择
            if (selectedMedicines[type].find(m => m.id === id)) {
                alert('该药品已经选择！');
                return;
            }
            
            const medicine = {
                id: id,
                name: name,
                spec: spec,
                price: price,
                unit: unit,
                dosage: 1,
                frequency: type === 'chinese' ? '' : '一日3次',
                days: type === 'chinese' ? 7 : 7,
                quantity: type === 'chinese' ? 30 : 1,
                usageMethod: type === 'chinese' ? '煎煮后服用' : '口服'
            };
            
            selectedMedicines[type].push(medicine);
            renderSelectedMedicines(type);
        }
        
        function removeMedicine(type, id) {
            selectedMedicines[type] = selectedMedicines[type].filter(m => m.id !== id);
            renderSelectedMedicines(type);
        }
        
        function renderSelectedMedicines(type) {
            const container = document.getElementById(type + '-selected');
            const medicinesHtml = selectedMedicines[type].map((medicine, index) => `
                <div class="selected-medicine">
                    <div class="medicine-header">
                        <strong>${medicine.name}</strong>
                        <button type="button" class="remove-btn" onclick="removeMedicine('${type}', ${medicine.id})">删除</button>
                    </div>
                    <div class="dosage-inputs">
                        <div class="input-group">
                            <label>剂量</label>
                            <input type="number" step="0.1" value="${medicine.dosage}" onchange="updateMedicine('${type}', ${medicine.id}, 'dosage', this.value)">
                        </div>
                        <div class="input-group">
                            <label>频次</label>
                            <input type="text" value="${medicine.frequency}" onchange="updateMedicine('${type}', ${medicine.id}, 'frequency', this.value)">
                        </div>
                        <div class="input-group">
                            <label>天数</label>
                            <input type="number" value="${medicine.days}" onchange="updateMedicine('${type}', ${medicine.id}, 'days', this.value)">
                        </div>
                        <div class="input-group">
                            <label>总量</label>
                            <input type="number" step="0.1" value="${medicine.quantity}" onchange="updateMedicine('${type}', ${medicine.id}, 'quantity', this.value)">
                        </div>
                        <div class="input-group">
                            <label>用法</label>
                            <input type="text" value="${medicine.usageMethod}" onchange="updateMedicine('${type}', ${medicine.id}, 'usageMethod', this.value)">
                        </div>
                        <div class="input-group">
                            <label>小计</label>
                            <input type="text" value="￥${(medicine.quantity * medicine.price).toFixed(2)}" readonly>
                        </div>
                    </div>
                    <input type="hidden" name="medicineIds" value="${medicine.id}">
                    <input type="hidden" name="dosages" value="${medicine.dosage}">
                    <input type="hidden" name="frequencies" value="${medicine.frequency}">
                    <input type="hidden" name="days" value="${medicine.days}">
                    <input type="hidden" name="quantities" value="${medicine.quantity}">
                    <input type="hidden" name="usageMethods" value="${medicine.usageMethod}">
                    <input type="hidden" name="medicineTypes" value="${type === 'chinese' ? '中药' : '西药'}">
                </div>
            `).join('');
            
            container.innerHTML = '<h4>已选择的' + (type === 'chinese' ? '中药' : '西药') + '</h4>' + medicinesHtml;
        }
        
        function updateMedicine(type, id, field, value) {
            const medicine = selectedMedicines[type].find(m => m.id === id);
            if (medicine) {
                medicine[field] = value;
                renderSelectedMedicines(type);
            }
        }
        
        // 初始化
        updatePrescriptionType();
    </script>
</body>
</html>
