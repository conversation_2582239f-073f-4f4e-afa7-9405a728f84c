package com.hospital.servlet;

import com.hospital.entity.Admin;
import com.hospital.entity.Doctor;
import com.hospital.entity.Patient;
import com.hospital.service.PatientService;
import com.hospital.service.DoctorService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 登录控制器
 处理系统的用户登录请求，支持三种用户类型的登录：
 1. 患者登录 - 使用患者编号和密码
 2. 医生登录 - 使用医生工号和密码
 3. 管理员登录 - 使用管理员用户名和密码
 登录成功后会在Session中保存用户信息和用户类型，
 请求参数：
userType: 用户类型（patient/doctor/admin）
username: 用户名（患者编号/医生工号/管理员用户名）
password: 密码
 */
@WebServlet("/LoginServlet")
public class LoginServlet extends HttpServlet {

    /**
     患者服务接口
     用于处理患者相关的业务逻辑，包括患者登录验证
     */
    private PatientService patientService;

    /**
     医生服务接口
     用于处理医生相关的业务逻辑，包括医生登录验证
     */
    private DoctorService doctorService;

    /**
     Servlet初始化方法
     在Servlet实例创建时调用，用于初始化依赖的服务对象
     通过IoC容器获取服务实例，实现依赖注入
     */
    @Override
    public void init() throws ServletException {
        BeanFactory beanFactory = BeanFactory.getInstance();
        patientService = beanFactory.getApplicationContext().getBean(PatientService.class);
        doctorService = beanFactory.getApplicationContext().getBean(DoctorService.class);
    }
    
    /**
     处理GET请求
     将GET请求转发给doPost方法处理，确保GET和POST请求的处理逻辑一致
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doPost(request, response);
    }

    /**
     处理POST请求
     处理用户登录请求的核心方法，包括：
     1. 参数验证和字符编码设置
     2. 根据用户类型调用相应的登录验证服务
     3. 登录成功后设置Session并重定向
     4. 登录失败时返回错误信息
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // 设置字符编码，防止中文乱码
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");

        // 获取登录参数
        String userType = request.getParameter("userType");
        String username = request.getParameter("username");
        String password = request.getParameter("password");

        // 参数验证
        if (userType == null || username == null || password == null) {
            request.setAttribute("error", "参数不完整");
            request.getRequestDispatcher("/login.jsp").forward(request, response);
            return;
        }

        // 获取Session对象，用于保存登录状态
        HttpSession session = request.getSession();
        
        try {
            // 根据用户类型进行不同的登录验证
            switch (userType) {
                case "patient":
                    // 患者登录验证
                    Patient patient = patientService.login(username, password);
                    if (patient != null) {
                        // 登录成功，保存患者信息到Session
                        session.setAttribute("patient", patient);
                        session.setAttribute("userType", "patient");
                        // 重定向到患者主页
                        response.sendRedirect(request.getContextPath() + "/patient/index.jsp");
                    } else {
                        // 登录失败，返回错误信息
                        request.setAttribute("error", "患者编号或密码错误");
                        request.getRequestDispatcher("/login.jsp").forward(request, response);
                    }
                    break;

                case "doctor":
                    // 医生登录验证
                    Doctor doctor = doctorService.login(username, password);
                    if (doctor != null) {
                        // 登录成功，保存医生信息到Session
                        session.setAttribute("doctor", doctor);
                        session.setAttribute("userType", "doctor");
                        // 重定向到医生主页
                        response.sendRedirect(request.getContextPath() + "/doctor/index.jsp");
                    } else {
                        // 登录失败，返回错误信息
                        request.setAttribute("error", "医生工号或密码错误");
                        request.getRequestDispatcher("/login.jsp").forward(request, response);
                    }
                    break;

                case "admin":
                    // 管理员登录验证
                    // 注意：这里使用硬编码验证，实际项目中应该从数据库验证
                    // TODO: 将管理员信息存储到数据库并实现相应的Service
                    if ("admin".equals(username) && "123456".equals(password)) {
                        // 创建管理员对象
                        Admin admin = new Admin();
                        admin.setUsername(username);
                        admin.setName("系统管理员");
                        admin.setRole("超级管理员");

                        // 登录成功，保存管理员信息到Session
                        session.setAttribute("admin", admin);
                        session.setAttribute("userType", "admin");

                        // 调试信息，用于开发阶段排查问题
                        String contextPath = request.getContextPath();
                        String redirectUrl = contextPath + "/admin/index.jsp";
                        System.out.println("管理员登录成功，重定向到: " + redirectUrl);
                        System.out.println("Context Path: " + contextPath);

                        // 重定向到管理员主页
                        response.sendRedirect(redirectUrl);
                    } else {
                        // 登录失败，返回错误信息
                        request.setAttribute("error", "管理员用户名或密码错误");
                        request.getRequestDispatcher("/login.jsp").forward(request, response);
                    }
                    break;

                default:
                    // 无效的用户类型
                    request.setAttribute("error", "无效的用户类型");
                    request.getRequestDispatcher("/login.jsp").forward(request, response);
                    break;
            }
        } catch (Exception e) {
            // 异常处理：记录异常信息并返回友好的错误提示
            e.printStackTrace();
            request.setAttribute("error", "系统错误：" + e.getMessage());
            request.getRequestDispatcher("/login.jsp").forward(request, response);
        }
    }
}
