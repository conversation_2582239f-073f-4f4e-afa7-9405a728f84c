<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%@ page import="com.hospital.entity.Registration" %>
<%@ page import="com.hospital.service.RegistrationService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Doctor doctor = (Doctor) session.getAttribute("doctor");
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    // 获取服务
    RegistrationService registrationService = JspServiceUtil.getRegistrationService(application);
    
    List<Registration> registrations = registrationService.findByDoctorIdWithDetails(doctor.getId());
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");

    // 处理成功消息
    String successParam = request.getParameter("success");
    String successMessage = "";
    if ("1".equals(successParam)) {
        successMessage = "✅ 就诊记录添加成功！";
    } else if ("2".equals(successParam)) {
        successMessage = "✅ 就诊记录更新成功！";
    } else if ("3".equals(successParam)) {
        successMessage = "✅ 挂号状态更新成功！";
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>患者挂号管理 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .filter-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-bar select,
        .filter-bar input {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1em;
        }
        
        .filter-bar select:focus,
        .filter-bar input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .status-registered {
            background: #d4edda;
            color: #155724;
        }
        
        .status-visited {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 0.9em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin-right: 5px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 3em;
            margin-bottom: 20px;
            color: #ccc;
        }
        
        .patient-info {
            font-weight: bold;
            color: #333;
        }

        .patient-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .patient-name {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
        }

        .patient-meta {
            display: flex;
            gap: 12px;
            font-size: 0.85em;
            color: #666;
        }

        .patient-id {
            background: #e3f2fd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;
        }

        .patient-phone {
            background: #f3e5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;
        }
        
        .symptoms {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/doctor/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>患者挂号管理</h2>

            <% if (!successMessage.isEmpty()) { %>
                <div class="alert alert-success">
                    <%= successMessage %>
                </div>
            <% } %>

            <div class="filter-bar">
                <label>状态筛选：</label>
                <select id="statusFilter" onchange="filterTable()">
                    <option value="">全部状态</option>
                    <option value="已挂号">已挂号</option>
                    <option value="已就诊">已就诊</option>
                    <option value="已取消">已取消</option>
                </select>
                
                <label>日期筛选：</label>
                <input type="date" id="dateFilter" onchange="filterTable()">
                
                <label>患者姓名：</label>
                <input type="text" id="nameFilter" placeholder="输入患者姓名" onkeyup="filterTable()">
            </div>
            
            <% if (registrations != null && !registrations.isEmpty()) { %>
                <table class="table" id="registrationTable">
                    <thead>
                        <tr>
                            <th>挂号单号</th>
                            <th>患者信息</th>
                            <th>挂号日期</th>
                            <th>挂号时间</th>
                            <th>挂号费</th>
                            <th>状态</th>
                            <th>主要症状</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% for (Registration reg : registrations) { %>
                            <tr data-status="<%= reg.getStatus() %>" data-date="<%= dateFormat.format(reg.getRegDate()) %>" data-name="<%= reg.getPatientName() %>">
                                <td><%= reg.getRegNo() %></td>
                                <td class="patient-info">
                                    <div class="patient-details">
                                        <div class="patient-name"><%= reg.getPatientName() %></div>
                                        <div class="patient-meta">
                                            <span class="patient-id">ID: <%= reg.getPatientId() %></span>
                                            <% if (reg.getPatientPhone() != null) { %>
                                                <span class="patient-phone">📞 <%= reg.getPatientPhone() %></span>
                                            <% } %>
                                        </div>
                                    </div>
                                </td>
                                <td><%= dateFormat.format(reg.getRegDate()) %></td>
                                <td><%= timeFormat.format(reg.getRegTime()) %></td>
                                <td>¥<%= reg.getRegFee() %></td>
                                <td>
                                    <% 
                                        String status = reg.getStatus();
                                        String statusClass = "";
                                        if ("已挂号".equals(status)) {
                                            statusClass = "status-registered";
                                        } else if ("已就诊".equals(status)) {
                                            statusClass = "status-visited";
                                        } else if ("已取消".equals(status)) {
                                            statusClass = "status-cancelled";
                                        }
                                    %>
                                    <span class="status <%= statusClass %>"><%= status %></span>
                                </td>
                                <td class="symptoms" title="<%= reg.getSymptoms() != null ? reg.getSymptoms() : "" %>">
                                    <%= reg.getSymptoms() != null ? reg.getSymptoms() : "-" %>
                                </td>
                                <td>
                                    <% if ("已挂号".equals(status)) { %>
                                        <a href="${pageContext.request.contextPath}/doctor/add-medical-record.jsp?regId=<%= reg.getId() %>" 
                                           class="btn btn-primary">开始就诊</a>
                                        <a href="${pageContext.request.contextPath}/UpdateRegistrationStatusServlet?id=<%= reg.getId() %>&status=已就诊" 
                                           class="btn btn-success"
                                           onclick="return confirm('确认标记为已就诊？')">标记已就诊</a>
                                    <% } else if ("已就诊".equals(status)) { %>
                                        <a href="${pageContext.request.contextPath}/doctor/view-medical-record.jsp?regId=<%= reg.getId() %>" 
                                           class="btn btn-primary">查看就诊记录</a>
                                    <% } else { %>
                                        -
                                    <% } %>
                                </td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            <% } else { %>
                <div class="empty-state">
                    <div style="font-size: 3em; margin-bottom: 20px;">📋</div>
                    <h3>暂无挂号记录</h3>
                    <p>当前没有患者挂号记录。</p>
                </div>
            <% } %>
        </div>
    </div>
    
    <script>
        function filterTable() {
            const statusFilter = document.getElementById('statusFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;
            const nameFilter = document.getElementById('nameFilter').value.toLowerCase();
            const table = document.getElementById('registrationTable');
            
            if (!table) return;
            
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const status = row.getAttribute('data-status');
                const date = row.getAttribute('data-date');
                const name = row.getAttribute('data-name').toLowerCase();
                
                let showRow = true;
                
                // 状态筛选
                if (statusFilter && status !== statusFilter) {
                    showRow = false;
                }
                
                // 日期筛选
                if (dateFilter && date !== dateFilter) {
                    showRow = false;
                }
                
                // 姓名筛选
                if (nameFilter && !name.includes(nameFilter)) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
            }
        }
    </script>
</body>
</html>
