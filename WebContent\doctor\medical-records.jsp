<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%@ page import="com.hospital.entity.MedicalRecord" %>
<%@ page import="com.hospital.service.MedicalRecordService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Doctor doctor = (Doctor) session.getAttribute("doctor");
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    // 获取服务
    MedicalRecordService medicalRecordService = JspServiceUtil.getMedicalRecordService(application);
    
    List<MedicalRecord> medicalRecords = medicalRecordService.findByDoctorIdWithDetails(doctor.getId());
    SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>就诊记录管理 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }
        
        .filter-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-bar input {
            padding: 8px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1em;
        }
        
        .filter-bar input:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .record-list {
            display: grid;
            gap: 20px;
        }
        
        .record-item {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
            transition: box-shadow 0.3s ease;
        }
        
        .record-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .patient-info {
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }
        
        .visit-time {
            color: #666;
            font-size: 0.9em;
        }
        
        .record-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .content-section {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
        }
        
        .content-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .content-text {
            color: #666;
            line-height: 1.5;
            font-size: 0.95em;
        }
        
        .record-actions {
            margin-top: 15px;
            text-align: right;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 0.9em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin-left: 5px;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 3em;
            margin-bottom: 20px;
            color: #ccc;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/doctor/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>就诊记录管理</h2>
            
            <%
                String success = request.getParameter("success");
                if (success != null) {
                    String message = "";
                    switch (success) {
                        case "1":
                            message = "✅ 就诊记录创建成功！";
                            break;
                        case "2":
                            message = "✅ 就诊记录修改成功！";
                            break;
                        case "3":
                            message = "✅ 就诊记录删除成功！";
                            break;
                        default:
                            message = "✅ 操作成功！";
                            break;
                    }
            %>
                <div class="alert alert-success">
                    <%= message %>
                </div>
            <% } %>
            
            <div class="filter-bar">
                <label>患者姓名：</label>
                <input type="text" id="nameFilter" placeholder="输入患者姓名" onkeyup="filterRecords()">
                
                <label>就诊日期：</label>
                <input type="date" id="dateFilter" onchange="filterRecords()">
            </div>
            
            <% if (medicalRecords != null && !medicalRecords.isEmpty()) { %>
                <div class="record-list" id="recordList">
                    <% for (MedicalRecord record : medicalRecords) { %>
                        <div class="record-item" data-name="<%= record.getPatientName() != null ? record.getPatientName().toLowerCase() : "" %>" 
                             data-date="<%= dateFormat.format(record.getVisitDate()) %>">
                            <div class="record-header">
                                <div class="patient-info">
                                    患者：<%= record.getPatientName() != null ? record.getPatientName() : "未知" %>
                                    | 挂号单号：<%= record.getRegNo() != null ? record.getRegNo() : "未知" %>
                                </div>
                                <div class="visit-time">
                                    就诊时间：<%= dateTimeFormat.format(record.getVisitDate()) %>
                                </div>
                            </div>
                            
                            <div class="record-content">
                                <div class="content-section">
                                    <div class="content-label">主诉</div>
                                    <div class="content-text"><%= record.getChiefComplaint() != null ? record.getChiefComplaint() : "-" %></div>
                                </div>
                                
                                <div class="content-section">
                                    <div class="content-label">诊断</div>
                                    <div class="content-text"><%= record.getDiagnosis() != null ? record.getDiagnosis() : "-" %></div>
                                </div>
                                
                                <% if (record.getTreatmentPlan() != null && !record.getTreatmentPlan().trim().isEmpty()) { %>
                                <div class="content-section">
                                    <div class="content-label">治疗方案</div>
                                    <div class="content-text"><%= record.getTreatmentPlan() %></div>
                                </div>
                                <% } %>
                                
                                <% if (record.getPrescription() != null && !record.getPrescription().trim().isEmpty()) { %>
                                <div class="content-section">
                                    <div class="content-label">处方</div>
                                    <div class="content-text"><%= record.getPrescription() %></div>
                                </div>
                                <% } %>
                                
                                <% if (record.getAdvice() != null && !record.getAdvice().trim().isEmpty()) { %>
                                <div class="content-section">
                                    <div class="content-label">医嘱</div>
                                    <div class="content-text"><%= record.getAdvice() %></div>
                                </div>
                                <% } %>
                                
                                <% if (record.getNextVisitDate() != null) { %>
                                <div class="content-section">
                                    <div class="content-label">下次复诊</div>
                                    <div class="content-text"><%= dateFormat.format(record.getNextVisitDate()) %></div>
                                </div>
                                <% } %>
                            </div>
                            
                            <div class="record-actions">
                                <a href="${pageContext.request.contextPath}/doctor/view-medical-record.jsp?id=<%= record.getId() %>"
                                   class="btn btn-info">查看详情</a>
                                <a href="${pageContext.request.contextPath}/doctor/edit-medical-record.jsp?id=<%= record.getId() %>"
                                   class="btn btn-primary">编辑</a>
                                <a href="${pageContext.request.contextPath}/doctor/prescription.jsp?medicalRecordId=<%= record.getId() %>"
                                   class="btn btn-success">开药</a>
                            </div>
                        </div>
                    <% } %>
                </div>
            <% } else { %>
                <div class="empty-state">
                    <div style="font-size: 3em; margin-bottom: 20px;">📑</div>
                    <h3>暂无就诊记录</h3>
                    <p>当前没有就诊记录。</p>
                </div>
            <% } %>
        </div>
    </div>
    
    <script>
        function filterRecords() {
            const nameFilter = document.getElementById('nameFilter').value.toLowerCase();
            const dateFilter = document.getElementById('dateFilter').value;
            const recordList = document.getElementById('recordList');
            
            if (!recordList) return;
            
            const records = recordList.getElementsByClassName('record-item');
            
            for (let i = 0; i < records.length; i++) {
                const record = records[i];
                const name = record.getAttribute('data-name');
                const date = record.getAttribute('data-date');
                
                let showRecord = true;
                
                // 姓名筛选
                if (nameFilter && !name.includes(nameFilter)) {
                    showRecord = false;
                }
                
                // 日期筛选
                if (dateFilter && date !== dateFilter) {
                    showRecord = false;
                }
                
                record.style.display = showRecord ? '' : 'none';
            }
        }
    </script>
</body>
</html>
