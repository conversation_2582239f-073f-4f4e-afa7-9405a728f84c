﻿﻿﻿﻿﻿<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Admin" %>
<%@ page import="com.hospital.entity.Patient" %>
<%@ page import="com.hospital.service.PatientService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    Admin admin = (Admin) session.getAttribute("admin");
    if (admin == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    PatientService patientService = JspServiceUtil.getPatientService(application);
    List<Patient> patients = patientService.findAll();
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>患者管理 - 医院管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .patient-no {
            color: #667eea;
            font-family: monospace;
        }

        .patient-info {
            font-weight: bold;
        }

        .contact-info {
            font-size: 0.9em;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/admin/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card">
        <% 
            String success = request.getParameter("success");
            if (success != null) { 
                String message = "";
                switch (success) {
                    case "1": message = " 患者信息创建成功！"; break;
                    case "2": message = " 患者信息修改成功！"; break;
                    case "3": message = " 患者信息删除成功！"; break;
                    default: message = " 操作成功！"; break;
                }
        %>
            <div class="alert alert-success"><%= message %></div>
        <% } %>
        
        <% if (request.getParameter("error") != null) { %>
            <div class="alert alert-error"><%= request.getParameter("error") %></div>
        <% } %>
        
        <h2>患者管理</h2>
        <p>共 <%= patients.size() %> 名患者</p>
        
        <% if (patients != null && !patients.isEmpty()) { %>
            <table>
                <thead>
                    <tr>
                        <th>患者编号</th>
                        <th>患者信息</th>
                        <th>联系方式</th>
                        <th>身份证号</th>
                        <th>注册时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <% for (Patient patient : patients) { %>
                        <tr>
                            <td class="patient-no"><%= patient.getPatientNo() %></td>
                            <td>
                                <div class="patient-info"><%= patient.getName() %></div>
                                <div class="contact-info">
                                    <%= patient.getGender() %>
                                    <% if (patient.getAge() != null) { %> | <%= patient.getAge() %>岁<% } %>
                                </div>
                            </td>
                            <td>
                                <div><%= patient.getPhone() %></div>
                                <% if (patient.getAddress() != null && !patient.getAddress().trim().isEmpty()) { %>
                                    <div class="contact-info"><%= patient.getAddress() %></div>
                                <% } %>
                            </td>
                            <td><%= patient.getIdCard() != null ? patient.getIdCard() : "-" %></td>
                            <td><%= patient.getCreateTime() != null ? dateFormat.format(patient.getCreateTime()) : "-" %></td>
                            <td>
                                <a href="${pageContext.request.contextPath}/admin/view-patient.jsp?id=<%= patient.getId() %>"
                                   style="color: #17a2b8; text-decoration: none;">查看详情</a>
                            </td>
                        </tr>
                    <% } %>
                </tbody>
            </table>
        <% } else { %>
            <div class="empty-state">
                <div style="font-size: 3em; margin-bottom: 20px;"></div>
                <h3>暂无患者数据</h3>
                <p>系统中还没有注册的患者。</p>
            </div>
        <% } %>
        </div>
    </div>
</body>
</html>
