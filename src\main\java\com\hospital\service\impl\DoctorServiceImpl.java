package com.hospital.service.impl;

import com.hospital.dao.DoctorDao;
import com.hospital.entity.Doctor;
import com.hospital.service.DoctorService;
import com.hospital.util.TransactionManager;

import java.util.List;

/**
 医生服务实现类
 */
public class DoctorServiceImpl implements DoctorService {

    private DoctorDao doctorDao;

    public void setDoctorDao(Doctor<PERSON>ao doctorDao) {
        this.doctorDao = doctorDao;
    }
    
    @Override
    public Doctor login(String doctorNo, String password) {
        return doctorDao.login(doctorNo, password);
    }
    
    @Override
    public Doctor findById(Integer id) {
        return doctorDao.findById(id);
    }
    
    @Override
    public Doctor findByDoctorNo(String doctorNo) {
        return doctorDao.findByDoctorNo(doctorNo);
    }
    
    @Override
    public List<Doctor> findByDeptId(Integer deptId) {
        return doctorDao.findByDeptId(deptId);
    }
    
    @Override
    public List<Doctor> findActiveDoctorsByDeptId(Integer deptId) {
        return doctorDao.findActiveDoctorsByDeptId(deptId);
    }
    
    @Override
    public List<Doctor> findAllWithDept() {
        return doctorDao.findAllWithDept();
    }
    
    @Override
    public List<Doctor> findByNameLike(String name) {
        return doctorDao.findByNameLike(name);
    }
    
    @Override
    public boolean addDoctor(Doctor doctor) {
        // 检查医生工号是否已存在
        if (isDoctorNoExists(doctor.getDoctorNo())) {
            return false;
        }
        
        // 设置默认状态
        if (doctor.getStatus() == null) {
            doctor.setStatus("在职");
        }
        
        return doctorDao.insert(doctor) > 0;
    }
    
    @Override
    public boolean updateDoctor(Doctor doctor) {
        return doctorDao.update(doctor) > 0;
    }
    
    @Override
    public boolean deleteDoctor(Integer id) {
        return doctorDao.deleteById(id) > 0;
    }
    
    @Override
    public boolean changePassword(Integer id, String oldPassword, String newPassword) {
        Doctor doctor = doctorDao.findById(id);
        if (doctor == null || !doctor.getPassword().equals(oldPassword)) {
            return false;
        }
        return doctorDao.updatePassword(id, newPassword) > 0;
    }
    
    @Override
    public List<Doctor> findAll() {
        return doctorDao.findAll();
    }
    
    @Override
    public List<Doctor> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return doctorDao.findByPage(offset, size);
    }
    
    @Override
    public int count() {
        return doctorDao.count();
    }
    
    @Override
    public boolean isDoctorNoExists(String doctorNo) {
        return doctorDao.findByDoctorNo(doctorNo) != null;
    }

    @Override
    public int countByDeptId(Integer deptId) {
        return doctorDao.countByDeptId(deptId);
    }
}
