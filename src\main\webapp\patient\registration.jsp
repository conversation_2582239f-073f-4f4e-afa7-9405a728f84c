<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Patient" %>
<%@ page import="com.hospital.entity.Department" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%@ page import="com.hospital.service.DepartmentService" %>
<%@ page import="com.hospital.service.DoctorService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%@ page import="java.util.List" %>
<%
    Patient patient = (Patient) session.getAttribute("patient");
    if (patient == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    // 获取服务
    DepartmentService departmentService = JspServiceUtil.getDepartmentService(application);
    DoctorService doctorService = JspServiceUtil.getDoctorService(application);
    
    List<Department> departments = departmentService.findAll();
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线挂号 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group select,
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group select:focus,
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .required {
            color: red;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/patient/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>在线挂号</h2>
            
            <% if (request.getAttribute("success") != null) { %>
                <div class="alert alert-success">
                    <%= request.getAttribute("success") %>
                </div>
            <% } %>
            
            <% if (request.getAttribute("error") != null) { %>
                <div class="alert alert-error">
                    <%= request.getAttribute("error") %>
                </div>
            <% } %>
            
            <form action="${pageContext.request.contextPath}/RegistrationServlet" method="post">
                <input type="hidden" name="action" value="register">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="deptId">选择科室 <span class="required">*</span></label>
                        <select id="deptId" name="deptId" required onchange="loadDoctors()">
                            <option value="">请选择科室</option>
                            <% for (Department dept : departments) { %>
                                <option value="<%= dept.getId() %>"><%= dept.getDeptName() %></option>
                            <% } %>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="doctorId">选择医生 <span class="required">*</span></label>
                        <select id="doctorId" name="doctorId" required>
                            <option value="">请先选择科室</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="regDate">挂号日期 <span class="required">*</span></label>
                        <input type="date" id="regDate" name="regDate" required min="<%= new java.text.SimpleDateFormat("yyyy-MM-dd").format(new java.util.Date()) %>">
                    </div>
                    
                    <div class="form-group">
                        <label for="regTime">挂号时间 <span class="required">*</span></label>
                        <select id="regTime" name="regTime" required>
                            <option value="">请选择时间</option>
                            <option value="08:00">08:00-09:00</option>
                            <option value="09:00">09:00-10:00</option>
                            <option value="10:00">10:00-11:00</option>
                            <option value="11:00">11:00-12:00</option>
                            <option value="14:00">14:00-15:00</option>
                            <option value="15:00">15:00-16:00</option>
                            <option value="16:00">16:00-17:00</option>
                            <option value="17:00">17:00-18:00</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="regFee">挂号费用</label>
                    <input type="number" id="regFee" name="regFee" value="10.00" step="0.01" min="0" readonly>
                </div>
                
                <div class="form-group">
                    <label for="symptoms">主要症状</label>
                    <textarea id="symptoms" name="symptoms" rows="4" placeholder="请简要描述您的主要症状..."></textarea>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary">确认挂号</button>
                    <a href="${pageContext.request.contextPath}/patient/index.jsp" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function loadDoctors() {
            const deptId = document.getElementById('deptId').value;
            const doctorSelect = document.getElementById('doctorId');
            
            // 清空医生选项
            doctorSelect.innerHTML = '<option value="">加载中...</option>';
            
            if (!deptId) {
                doctorSelect.innerHTML = '<option value="">请先选择科室</option>';
                return;
            }
            
            // 这里应该通过AJAX加载医生列表，暂时使用静态数据
            fetch('${pageContext.request.contextPath}/GetDoctorsServlet?deptId=' + deptId)
                .then(response => response.json())
                .then(doctors => {
                    doctorSelect.innerHTML = '<option value="">请选择医生</option>';
                    doctors.forEach(doctor => {
                        const option = document.createElement('option');
                        option.value = doctor.id;
                        option.textContent = doctor.name + ' - ' + doctor.title;
                        doctorSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('加载医生列表失败:', error);
                    doctorSelect.innerHTML = '<option value="">加载失败，请重试</option>';
                });
        }
    </script>
</body>
</html>
