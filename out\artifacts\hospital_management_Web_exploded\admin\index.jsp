<%@ page contentType="text/html;charset=UTF-8" language="java" %> <%@ page
import="com.hospital.entity.Admin" %> <% Admin admin = (Admin)
session.getAttribute("admin"); if (admin == null) {
response.sendRedirect(request.getContextPath() + "/login.jsp"); return; } %>
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>管理员控制台 - 医院挂号就诊管理系统</title>
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 0;
        padding: 0;
        background: #f5f5f5;
      }
      .header {
        background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
        color: white;
        padding: 20px 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .header-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .logo {
        font-size: 1.5em;
        font-weight: bold;
      }
      .user-info {
        display: flex;
        align-items: center;
        gap: 20px;
      }
      .logout-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 8px 16px;
        border-radius: 5px;
        text-decoration: none;
      }
      .container {
        max-width: 1200px;
        margin: 30px auto;
        padding: 0 20px;
      }
      .welcome-card {
        background: white;
        border-radius: 10px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }
      .stat-card {
        background: white;
        border-radius: 10px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 10px;
        color: #667eea;
      }
      .stat-label {
        color: #666;
        font-size: 1.1em;
      }
      .menu-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
      }
      .menu-item {
        background: white;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-decoration: none;
        color: inherit;
        transition: transform 0.3s ease;
      }
      .menu-item:hover {
        transform: translateY(-5px);
      }
      .menu-icon {
        font-size: 3em;
        margin-bottom: 15px;
        color: #667eea;
      }
      .menu-item h3 {
        margin: 0 0 10px 0;
        color: #333;
      }
      .menu-item p {
        margin: 0;
        color: #666;
        font-size: 0.9em;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="header-content">
        <div class="logo">🏥 医院管理系统</div>
        <div class="user-info">
          <span>欢迎，<%= admin.getName() %> (<%= admin.getRole() %>)</span>
          <a
            href="${pageContext.request.contextPath}/LogoutServlet"
            class="logout-btn"
            >退出登录</a
          >
        </div>
      </div>
    </div>

    <div class="container">
      <div class="welcome-card">
        <h2>管理员控制台</h2>
        <p>欢迎使用医院管理系统，您可以在这里管理系统的各项功能和数据。</p>
      </div>

      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-number" id="patientCount">0</div>
          <div class="stat-label">注册患者</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="doctorCount">0</div>
          <div class="stat-label">在职医生</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="departmentCount">0</div>
          <div class="stat-label">科室数量</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="registrationCount">0</div>
          <div class="stat-label">总挂号数</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="medicalRecordCount">0</div>
          <div class="stat-label">就诊记录</div>
        </div>
      </div>

      <div class="menu-grid">
        <a
          href="${pageContext.request.contextPath}/admin/patients.jsp"
          class="menu-item"
        >
          <div class="menu-icon">👥</div>
          <h3>患者管理</h3>
          <p>查看和管理患者信息</p>
        </a>

        <a
          href="${pageContext.request.contextPath}/admin/doctors.jsp"
          class="menu-item"
        >
          <div class="menu-icon">👨‍⚕️</div>
          <h3>医生管理</h3>
          <p>管理医生信息和排班</p>
        </a>

        <a
          href="${pageContext.request.contextPath}/admin/departments.jsp"
          class="menu-item"
        >
          <div class="menu-icon">🏢</div>
          <h3>科室管理</h3>
          <p>管理医院科室信息</p>
        </a>

        <a
          href="${pageContext.request.contextPath}/admin/registrations.jsp"
          class="menu-item"
        >
          <div class="menu-icon">📋</div>
          <h3>挂号管理</h3>
          <p>查看和管理挂号记录</p>
        </a>

        <a
          href="${pageContext.request.contextPath}/admin/medical-records.jsp"
          class="menu-item"
        >
          <div class="menu-icon">📄</div>
          <h3>就诊记录</h3>
          <p>查看所有就诊记录</p>
        </a>
      </div>
    </div>

    <script>
      // 从后端获取真实统计数据
      document.addEventListener("DOMContentLoaded", function () {
        loadStatsData();
      });

      function loadStatsData() {
        // 显示加载状态
        showLoading();

        // 发送AJAX请求获取统计数据
        fetch("${pageContext.request.contextPath}/AdminStatsServlet")
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              // 更新统计数据
              document.getElementById("patientCount").textContent =
                data.patientCount;
              document.getElementById("doctorCount").textContent =
                data.doctorCount;
              document.getElementById("departmentCount").textContent =
                data.departmentCount;
              document.getElementById("registrationCount").textContent =
                data.registrationCount;
              document.getElementById("medicalRecordCount").textContent =
                data.medicalRecordCount;
            } else {
              console.error("获取统计数据失败:", data.error);
              // 显示错误状态
              showError();
            }
          })
          .catch((error) => {
            console.error("请求失败:", error);
            // 显示错误状态
            showError();
          });
      }

      function showLoading() {
        document.getElementById("patientCount").textContent = "...";
        document.getElementById("doctorCount").textContent = "...";
        document.getElementById("departmentCount").textContent = "...";
        document.getElementById("registrationCount").textContent = "...";
        document.getElementById("medicalRecordCount").textContent = "...";
      }

      function showError() {
        document.getElementById("patientCount").textContent = "错误";
        document.getElementById("doctorCount").textContent = "错误";
        document.getElementById("departmentCount").textContent = "错误";
        document.getElementById("registrationCount").textContent = "错误";
        document.getElementById("medicalRecordCount").textContent = "错误";
      }
    </script>
  </body>
</html>
