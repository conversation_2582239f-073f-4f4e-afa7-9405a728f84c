package com.hospital.service;

import com.hospital.entity.Medicine;
import java.util.List;

/**
 * 药品服务接口
 * 提供药品的业务逻辑处理
 */
public interface MedicineService {
    
    /**
     * 添加药品
     */
    boolean addMedicine(Medicine medicine);
    
    /**
     * 更新药品
     */
    boolean updateMedicine(Medicine medicine);
    
    /**
     * 删除药品
     */
    boolean deleteMedicine(Integer id);
    
    /**
     * 根据ID查询药品
     */
    Medicine findById(Integer id);
    
    /**
     * 查询所有药品
     */
    List<Medicine> findAll();
    
    /**
     * 根据药品编码查询药品
     */
    Medicine findByMedicineCode(String medicineCode);
    
    /**
     * 根据药品名称模糊查询
     */
    List<Medicine> findByMedicineNameLike(String medicineName);
    
    /**
     * 根据药品类型查询药品列表
     */
    List<Medicine> findByMedicineType(String medicineType);
    
    /**
     * 根据分类ID查询药品列表
     */
    List<Medicine> findByCategoryId(Integer categoryId);
    
    /**
     * 根据状态查询药品列表
     */
    List<Medicine> findByStatus(String status);
    
    /**
     * 查询所有药品（包含分类信息）
     */
    List<Medicine> findAllWithCategory();
    
    /**
     * 根据药品类型查询药品（包含分类信息）
     */
    List<Medicine> findByMedicineTypeWithCategory(String medicineType);
    
    /**
     * 根据药品名称模糊查询（包含分类信息）
     */
    List<Medicine> findByMedicineNameLikeWithCategory(String medicineName);
    
    /**
     * 检查药品编码是否已存在
     */
    boolean isMedicineCodeExists(String medicineCode);
    
    /**
     * 更新药品库存
     */
    boolean updateStock(Integer medicineId, Integer stock);
    
    /**
     * 减少药品库存
     */
    boolean reduceStock(Integer medicineId, Integer quantity);
    
    /**
     * 分页查询药品
     */
    List<Medicine> findByPage(int page, int size);
    
    /**
     * 统计药品总数
     */
    int count();
    
    /**
     * 生成药品编码
     */
    String generateMedicineCode(String medicineType);
}
