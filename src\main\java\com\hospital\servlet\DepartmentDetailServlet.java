package com.hospital.servlet;

import com.hospital.entity.Department;
import com.hospital.service.DepartmentService;
import com.hospital.ioc.BeanFactory;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

/**
 科室详情API
 */
@WebServlet("/DepartmentDetailServlet")
public class DepartmentDetailServlet extends HttpServlet {
    
    private DepartmentService departmentService;
    
    @Override
    public void init() throws ServletException {
        super.init();
        BeanFactory beanFactory = BeanFactory.getInstance();
        departmentService = beanFactory.getApplicationContext().getBean(DepartmentService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        String deptIdStr = request.getParameter("id");
        if (deptIdStr == null || deptIdStr.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "科室ID不能为空");
            return;
        }
        
        try {
            Integer deptId = Integer.parseInt(deptIdStr);
            Department department = departmentService.findById(deptId);
            
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> result = new HashMap<>();
            
            if (department != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                
                result.put("success", true);
                result.put("id", department.getId());
                result.put("deptName", department.getDeptName());
                result.put("location", department.getLocation());
                result.put("phone", department.getPhone());
                result.put("deptDesc", department.getDeptDesc());
                result.put("createTime", department.getCreateTime() != null ? dateFormat.format(department.getCreateTime()) : null);
                result.put("updateTime", department.getUpdateTime() != null ? dateFormat.format(department.getUpdateTime()) : null);
            } else {
                result.put("success", false);
                result.put("error", "科室不存在");
            }
            
            String jsonResponse = mapper.writeValueAsString(result);
            response.getWriter().write(jsonResponse);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "无效的科室ID");
        } catch (Exception e) {
            e.printStackTrace();
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "获取科室信息失败: " + e.getMessage());
            
            ObjectMapper mapper = new ObjectMapper();
            String jsonResponse = mapper.writeValueAsString(errorResponse);
            response.getWriter().write(jsonResponse);
        }
    }
}
