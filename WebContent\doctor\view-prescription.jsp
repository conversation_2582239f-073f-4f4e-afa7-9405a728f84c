<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.hospital.entity.*" %>
<%@ page import="java.util.*" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%
    // 检查医生登录状态
    Doctor doctor = (Doctor) session.getAttribute("doctor");
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/doctor/login.jsp");
        return;
    }
    
    // 获取处方信息
    Prescription prescription = (Prescription) request.getAttribute("prescription");
    if (prescription == null) {
        response.sendRedirect(request.getContextPath() + "/PrescriptionServlet?action=list");
        return;
    }
    
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>处方详情 - 医院管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
        }
        
        .nav-links a {
            color: #666;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .nav-links a:hover {
            background: #667eea;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .prescription-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .prescription-no {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        
        .prescription-type {
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 1em;
            font-weight: 500;
        }
        
        .type-chinese {
            background: #fff8dc;
            color: #8b4513;
            border: 1px solid #ddd;
        }
        
        .type-western {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        
        .type-mixed {
            background: #f3e5f5;
            color: #7b1fa2;
            border: 1px solid #ce93d8;
        }
        
        .info-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .info-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 4px;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .status-issued {
            background: #d4edda;
            color: #155724;
        }
        
        .status-dispensed {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .medicine-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .medicine-table th, .medicine-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .medicine-table th {
            background-color: #f8f9fa;
            color: #333;
            font-weight: 600;
        }
        
        .medicine-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .chinese-attrs {
            background: #fff8dc;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .chinese-attrs h4 {
            color: #8b4513;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .actions {
            margin-top: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/PrescriptionServlet?action=list">返回处方列表</a>
                <a href="${pageContext.request.contextPath}/doctor/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <div class="prescription-header">
                <h2>处方详情</h2>
                <div class="prescription-type <%= "中药处方".equals(prescription.getPrescriptionType()) ? "type-chinese" : 
                                                  "西药处方".equals(prescription.getPrescriptionType()) ? "type-western" : "type-mixed" %>">
                    <%= prescription.getPrescriptionType() %>
                </div>
            </div>
            
            <div class="info-section">
                <h3>基本信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">处方编号</span>
                        <span class="info-value"><%= prescription.getPrescriptionNo() %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">患者姓名</span>
                        <span class="info-value"><%= prescription.getPatientName() != null ? prescription.getPatientName() : "未知" %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">开具医生</span>
                        <span class="info-value"><%= prescription.getDoctorName() != null ? prescription.getDoctorName() : "未知" %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">开具日期</span>
                        <span class="info-value"><%= dateFormat.format(prescription.getPrescriptionDate()) %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">诊断</span>
                        <span class="info-value"><%= prescription.getDiagnosis() != null ? prescription.getDiagnosis() : "无" %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">状态</span>
                        <span class="status <%= "已开具".equals(prescription.getStatus()) ? "status-issued" : 
                                              "已发药".equals(prescription.getStatus()) ? "status-dispensed" : "status-cancelled" %>">
                            <%= prescription.getStatus() %>
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="info-section">
                <h3>药品明细</h3>
                <% if (prescription.getPrescriptionDetails() == null || prescription.getPrescriptionDetails().isEmpty()) { %>
                    <p>暂无药品明细</p>
                <% } else { %>
                    <table class="medicine-table">
                        <thead>
                            <tr>
                                <th>药品名称</th>
                                <th>类型</th>
                                <th>规格</th>
                                <th>剂量</th>
                                <th>用法</th>
                                <th>频次</th>
                                <th>天数</th>
                                <th>数量</th>
                                <th>单价</th>
                                <th>小计</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% for (PrescriptionDetail detail : prescription.getPrescriptionDetails()) { %>
                                <tr>
                                    <td><%= detail.getMedicineName() %></td>
                                    <td><%= detail.getMedicineType() %></td>
                                    <td><%= detail.getSpecification() != null ? detail.getSpecification() : "-" %></td>
                                    <td><%= detail.getDosage() + " " + detail.getDosageUnit() %></td>
                                    <td><%= detail.getUsageMethod() != null ? detail.getUsageMethod() : "-" %></td>
                                    <td><%= detail.getFrequency() != null ? detail.getFrequency() : "-" %></td>
                                    <td><%= detail.getDays() != null ? detail.getDays() + "天" : "-" %></td>
                                    <td><%= detail.getQuantity() %></td>
                                    <td>￥<%= detail.getUnitPrice() %></td>
                                    <td>￥<%= detail.getTotalPrice() %></td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                <% } %>
                
                <% if ("中药处方".equals(prescription.getPrescriptionType()) && prescription.getChinesePrescriptionAttr() != null) { %>
                    <div class="chinese-attrs">
                        <h4>中药煎煮方法</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">煎煮方法</span>
                                <span class="info-value"><%= prescription.getChinesePrescriptionAttr().getDecoctionMethod() != null ? 
                                                            prescription.getChinesePrescriptionAttr().getDecoctionMethod() : "常规煎煮" %></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">煎煮次数</span>
                                <span class="info-value"><%= prescription.getChinesePrescriptionAttr().getDecoctionTimes() != null ? 
                                                            prescription.getChinesePrescriptionAttr().getDecoctionTimes() + "次" : "2次" %></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">服用方法</span>
                                <span class="info-value"><%= prescription.getChinesePrescriptionAttr().getUsageMethod() != null ? 
                                                            prescription.getChinesePrescriptionAttr().getUsageMethod() : "温服" %></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">每次用量</span>
                                <span class="info-value"><%= prescription.getChinesePrescriptionAttr().getDosagePerTime() != null ? 
                                                            prescription.getChinesePrescriptionAttr().getDosagePerTime() : "150ml" %></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">服用频次</span>
                                <span class="info-value"><%= prescription.getChinesePrescriptionAttr().getFrequency() != null ? 
                                                            prescription.getChinesePrescriptionAttr().getFrequency() : "一日2次" %></span>
                            </div>
                        </div>
                    </div>
                <% } %>
            </div>
            
            <div class="actions">
                <a href="${pageContext.request.contextPath}/PrescriptionServlet?action=list" class="btn btn-secondary">返回列表</a>
                <% if ("已开具".equals(prescription.getStatus())) { %>
                    <a href="${pageContext.request.contextPath}/PrescriptionServlet?action=updateStatus&id=<%= prescription.getId() %>&status=已发药" 
                       class="btn btn-primary" onclick="return confirm('确认标记为已发药？')">标记发药</a>
                <% } %>
            </div>
        </div>
    </div>
</body>
</html>
