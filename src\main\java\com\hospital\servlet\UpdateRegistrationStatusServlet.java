package com.hospital.servlet;

import com.hospital.entity.Doctor;
import com.hospital.entity.Registration;
import com.hospital.service.RegistrationService;
import com.hospital.ioc.BeanFactory;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * 更新挂号状态控制器
 */
@WebServlet("/UpdateRegistrationStatusServlet")
public class UpdateRegistrationStatusServlet extends HttpServlet {
    
    private RegistrationService registrationService;
    
    @Override
    public void init() throws ServletException {
        BeanFactory beanFactory = BeanFactory.getInstance();
        registrationService = beanFactory.getApplicationContext().getBean(RegistrationService.class);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        
        HttpSession session = request.getSession();
        Doctor doctor = (Doctor) session.getAttribute("doctor");
        
        if (doctor == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        String idStr = request.getParameter("id");
        String status = request.getParameter("status");
        
        if (idStr == null || status == null) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "缺少必要参数");
            return;
        }
        
        try {
            Integer id = Integer.parseInt(idStr);
            
            // 验证挂号记录是否属于当前医生
            Registration registration = registrationService.findById(id);
            if (registration == null || !registration.getDoctorId().equals(doctor.getId())) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "无权限操作此挂号记录");
                return;
            }
            
            // 更新状态
            boolean success = registrationService.updateStatus(id, status);
            
            if (success) {
                // 重定向回挂号管理页面，使用数字代码而不是中文
                response.sendRedirect(request.getContextPath() + "/doctor/registrations.jsp?success=3");
            } else {
                response.sendRedirect(request.getContextPath() + "/doctor/registrations.jsp?error=" + URLEncoder.encode("状态更新失败", "UTF-8"));
            }
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "挂号ID格式错误");
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect(request.getContextPath() + "/doctor/registrations.jsp?error=" + URLEncoder.encode("系统错误", "UTF-8"));
        }
    }
}
