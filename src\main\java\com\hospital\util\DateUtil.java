package com.hospital.util;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
日期工具类
提供日期和时间的格式化、解析等常用操作的工具方法。
该类封装了SimpleDateFormat的使用，提供了统一的日期处理接口，
主要功能：
1. 日期格式化：将Date对象转换为指定格式的字符串
2. 日期解析：将字符串解析为Date对象
3. 常用格式：提供系统中常用的日期时间格式常量
4. 异常处理：安全的日期解析，避免解析异常
支持的格式：
日期格式：yyyy-MM-dd
时间格式：HH:mm:ss
日期时间格式：yyyy-MM-dd HH:mm:ss
 */
public class DateUtil {

    /**
     标准日期格式常量
     格式：yyyy-MM-dd，如：2024-01-01
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     标准时间格式常量
     格式：HH:mm:ss，如：14:30:00
     */
    public static final String TIME_FORMAT = "HH:mm:ss";

    /**
     标准日期时间格式常量
     格式：yyyy-MM-dd HH:mm:ss，如：2024-01-01 14:30:00
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     格式化日期
     将Date对象按照指定的格式转换为字符串。
     */
    public static String format(Date date, String pattern) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    /**
     格式化日期（默认格式）
     使用标准日期格式（yyyy-MM-dd）格式化日期。
     */
    public static String formatDate(Date date) {
        return format(date, DATE_FORMAT);
    }

    /**
     格式化时间
     使用标准时间格式（HH:mm:ss）格式化时间部分。
     */
    public static String formatTime(Date date) {
        return format(date, TIME_FORMAT);
    }

    /**
     格式化日期时间
     使用标准日期时间格式（yyyy-MM-dd HH:mm:ss）格式化完整的日期时间。
     */
    public static String formatDateTime(Date date) {
        return format(date, DATETIME_FORMAT);
    }
    
    /**
     解析日期字符串
     将指定格式的日期字符串解析为Date对象。
     */
    public static Date parse(String dateStr, String pattern) {
        // 检查输入参数的有效性
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            return sdf.parse(dateStr);
        } catch (Exception e) {
            // 解析失败时返回null，避免抛出异常影响业务流程
            return null;
        }
    }

    /**
     解析日期字符串（默认格式）
     使用标准日期格式（yyyy-MM-dd）解析日期字符串。
     */
    public static Date parseDate(String dateStr) {
        return parse(dateStr, DATE_FORMAT);
    }

    /**
     解析日期时间字符串
     使用标准日期时间格式（yyyy-MM-dd HH:mm:ss）解析日期时间字符串。
     */
    public static Date parseDateTime(String dateTimeStr) {
        return parse(dateTimeStr, DATETIME_FORMAT);
    }
}
