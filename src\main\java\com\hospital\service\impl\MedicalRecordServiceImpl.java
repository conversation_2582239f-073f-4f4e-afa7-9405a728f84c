package com.hospital.service.impl;

import com.hospital.dao.MedicalRecordDao;
import com.hospital.entity.MedicalRecord;
import com.hospital.service.MedicalRecordService;
import com.hospital.service.RegistrationService;
import com.hospital.util.TransactionManager;

import java.util.Date;
import java.util.List;

/**
 就诊记录服务实现类
 */
public class MedicalRecordServiceImpl implements MedicalRecordService {

    private MedicalRecordDao medicalRecordDao;
    private RegistrationService registrationService;

    public void setMedicalRecordDao(MedicalRecordDao medicalRecordDao) {
        this.medicalRecordDao = medicalRecordDao;
    }

    public void setRegistrationService(RegistrationService registrationService) {
        this.registrationService = registrationService;
    }
    
    @Override
    public boolean addMedicalRecord(MedicalRecord medicalRecord) {
        // 设置就诊时间
        if (medicalRecord.getVisitDate() == null) {
            medicalRecord.setVisitDate(new Date());
        }
        
        boolean success = medicalRecordDao.insert(medicalRecord) > 0;
        
        // 如果添加成功，更新对应挂号记录的状态为"已就诊"
        if (success && medicalRecord.getRegId() != null) {
            registrationService.updateStatus(medicalRecord.getRegId(), "已就诊");
        }
        
        return success;
    }
    
    @Override
    public boolean updateMedicalRecord(MedicalRecord medicalRecord) {
        return medicalRecordDao.update(medicalRecord) > 0;
    }
    
    @Override
    public MedicalRecord findById(Integer id) {
        return medicalRecordDao.findById(id);
    }
    
    @Override
    public MedicalRecord findByRegId(Integer regId) {
        return medicalRecordDao.findByRegId(regId);
    }
    
    @Override
    public List<MedicalRecord> findByPatientId(Integer patientId) {
        return medicalRecordDao.findByPatientId(patientId);
    }
    
    @Override
    public List<MedicalRecord> findByDoctorId(Integer doctorId) {
        return medicalRecordDao.findByDoctorId(doctorId);
    }
    
    @Override
    public List<MedicalRecord> findAllWithDetails() {
        return medicalRecordDao.findAllWithDetails();
    }
    
    @Override
    public List<MedicalRecord> findByPatientIdWithDetails(Integer patientId) {
        return medicalRecordDao.findByPatientIdWithDetails(patientId);
    }
    
    @Override
    public List<MedicalRecord> findByDoctorIdWithDetails(Integer doctorId) {
        return medicalRecordDao.findByDoctorIdWithDetails(doctorId);
    }
    
    @Override
    public MedicalRecord findByRegIdWithDetails(Integer regId) {
        return medicalRecordDao.findByRegIdWithDetails(regId);
    }
    
    @Override
    public boolean deleteMedicalRecord(Integer id) {
        return medicalRecordDao.deleteById(id) > 0;
    }
    
    @Override
    public List<MedicalRecord> findByPage(int page, int size) {
        int offset = (page - 1) * size;
        return medicalRecordDao.findByPage(offset, size);
    }
    
    @Override
    public int count() {
        return medicalRecordDao.count();
    }
    
    @Override
    public int countByDoctorAndDateRange(Integer doctorId, Date startDate, Date endDate) {
        return medicalRecordDao.countByDoctorAndDateRange(doctorId, startDate, endDate);
    }
    
    @Override
    public int countByPatientId(Integer patientId) {
        return medicalRecordDao.countByPatientId(patientId);
    }
}
