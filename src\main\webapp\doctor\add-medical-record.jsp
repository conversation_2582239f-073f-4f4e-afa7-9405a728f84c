<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.hospital.entity.Doctor" %>
<%@ page import="com.hospital.entity.Registration" %>
<%@ page import="com.hospital.service.RegistrationService" %>
<%@ page import="com.hospital.util.JspServiceUtil" %>
<%
    Doctor doctor = (Doctor) session.getAttribute("doctor");
    if (doctor == null) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
    
    String regIdStr = request.getParameter("regId");
    if (regIdStr == null) {
        response.sendRedirect(request.getContextPath() + "/doctor/registrations.jsp");
        return;
    }
    
    // 获取挂号信息
    RegistrationService registrationService = JspServiceUtil.getRegistrationService(application);
    Registration registration = registrationService.findById(Integer.parseInt(regIdStr));
    
    if (registration == null || !registration.getDoctorId().equals(doctor.getId())) {
        response.sendRedirect(request.getContextPath() + "/doctor/registrations.jsp");
        return;
    }
    
    // 获取挂号详情
    registration = registrationService.findByRegNo(registration.getRegNo());
    if (registration.getPatientName() == null) {
        // 如果没有详情，重新查询带详情的记录
        java.util.List<Registration> regList = registrationService.findByDoctorIdWithDetails(doctor.getId());
        for (Registration reg : regList) {
            if (reg.getId().equals(registration.getId())) {
                registration = reg;
                break;
            }
        }
    }
%>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>录入就诊记录 - 医院挂号就诊管理系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #324263 0%, #b0e4ed 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1000px;
            margin: 30px auto;
            padding: 0 20px;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card h2 {
            color: #333;
            margin: 0 0 20px 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .patient-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }
        
        .patient-info h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .info-label {
            font-weight: 500;
            color: #666;
        }
        
        .info-value {
            color: #333;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .required {
            color: red;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🏥 医院管理系统</div>
            <div class="nav-links">
                <a href="${pageContext.request.contextPath}/doctor/registrations.jsp">返回挂号管理</a>
                <a href="${pageContext.request.contextPath}/doctor/index.jsp">返回首页</a>
                <a href="${pageContext.request.contextPath}/LogoutServlet">退出登录</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="card">
            <h2>录入就诊记录</h2>
            
            <div class="patient-info">
                <h3>患者信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">患者姓名：</span>
                        <span class="info-value"><%= registration.getPatientName() != null ? registration.getPatientName() : "未知" %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">挂号单号：</span>
                        <span class="info-value"><%= registration.getRegNo() %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">挂号日期：</span>
                        <span class="info-value"><%= new java.text.SimpleDateFormat("yyyy-MM-dd").format(registration.getRegDate()) %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">挂号时间：</span>
                        <span class="info-value"><%= new java.text.SimpleDateFormat("HH:mm").format(registration.getRegTime()) %></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">主要症状：</span>
                        <span class="info-value"><%= registration.getSymptoms() != null ? registration.getSymptoms() : "无" %></span>
                    </div>
                </div>
            </div>
            
            <% if (request.getAttribute("error") != null) { %>
                <div class="alert alert-error">
                    <%= request.getAttribute("error") %>
                </div>
            <% } %>
            
            <form action="${pageContext.request.contextPath}/MedicalRecordServlet" method="post">
                <input type="hidden" name="action" value="add">
                <input type="hidden" name="regId" value="<%= registration.getId() %>">
                
                <div class="form-group">
                    <label for="chiefComplaint">主诉 <span class="required">*</span></label>
                    <textarea id="chiefComplaint" name="chiefComplaint" required 
                              placeholder="请输入患者的主要症状和不适..."><%= registration.getSymptoms() != null ? registration.getSymptoms() : "" %></textarea>
                </div>
                
                <div class="form-group">
                    <label for="presentIllness">现病史</label>
                    <textarea id="presentIllness" name="presentIllness" 
                              placeholder="请详细描述患者的现病史..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="physicalExam">体格检查</label>
                    <textarea id="physicalExam" name="physicalExam" 
                              placeholder="请记录体格检查结果..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="diagnosis">诊断 <span class="required">*</span></label>
                    <textarea id="diagnosis" name="diagnosis" required 
                              placeholder="请输入诊断结果..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="treatmentPlan">治疗方案</label>
                    <textarea id="treatmentPlan" name="treatmentPlan" 
                              placeholder="请输入治疗方案..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="prescription">处方</label>
                    <textarea id="prescription" name="prescription" 
                              placeholder="请输入处方药物及用法用量..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="advice">医嘱</label>
                    <textarea id="advice" name="advice" 
                              placeholder="请输入医嘱和注意事项..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="nextVisitDate">下次复诊日期</label>
                    <input type="date" id="nextVisitDate" name="nextVisitDate" 
                           min="<%= new java.text.SimpleDateFormat("yyyy-MM-dd").format(new java.util.Date()) %>">
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary">保存就诊记录</button>
                    <a href="${pageContext.request.contextPath}/doctor/registrations.jsp" class="btn btn-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
