package com.hospital.servlet;

import com.hospital.entity.*;
import com.hospital.service.PrescriptionService;
import com.hospital.util.JspServiceUtil;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 处方管理Servlet
 * 处理处方相关的请求，包括开具处方、查看处方、更新处方状态等
 */
@WebServlet("/PrescriptionServlet")
public class PrescriptionServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        String action = request.getParameter("action");
        
        try {
            switch (action) {
                case "add":
                    addPrescription(request, response);
                    break;
                case "view":
                    viewPrescription(request, response);
                    break;
                case "list":
                    listPrescriptions(request, response);
                    break;
                case "updateStatus":
                    updatePrescriptionStatus(request, response);
                    break;
                default:
                    response.sendRedirect(request.getContextPath() + "/doctor/index.jsp");
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "操作失败：" + e.getMessage());
            request.getRequestDispatcher("/doctor/prescription.jsp").forward(request, response);
        }
    }
    
    /**
     * 添加处方
     */
    private void addPrescription(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession();
        Doctor doctor = (Doctor) session.getAttribute("doctor");
        
        if (doctor == null) {
            response.sendRedirect(request.getContextPath() + "/doctor/login.jsp");
            return;
        }
        
        try {
            // 获取处方基本信息
            Integer medicalRecordId = Integer.parseInt(request.getParameter("medicalRecordId"));
            Integer patientId = Integer.parseInt(request.getParameter("patientId"));
            Integer doctorId = Integer.parseInt(request.getParameter("doctorId"));
            String prescriptionType = request.getParameter("prescriptionType");
            String diagnosis = request.getParameter("diagnosis");
            
            // 创建处方对象
            PrescriptionService prescriptionService = JspServiceUtil.getPrescriptionService(getServletContext());
            String prescriptionNo = prescriptionService.generatePrescriptionNo();
            
            Prescription prescription = new Prescription(prescriptionNo, medicalRecordId, patientId, 
                                                       doctorId, prescriptionType, diagnosis);
            
            // 获取药品信息
            String[] medicineIds = request.getParameterValues("medicineIds");
            String[] dosages = request.getParameterValues("dosages");
            String[] frequencies = request.getParameterValues("frequencies");
            String[] days = request.getParameterValues("days");
            String[] quantities = request.getParameterValues("quantities");
            String[] usageMethods = request.getParameterValues("usageMethods");
            String[] medicineTypes = request.getParameterValues("medicineTypes");
            
            if (medicineIds == null || medicineIds.length == 0) {
                request.setAttribute("error", "请至少选择一种药品！");
                request.getRequestDispatcher("/doctor/prescription.jsp?medicalRecordId=" + medicalRecordId).forward(request, response);
                return;
            }
            
            // 创建处方明细列表
            List<PrescriptionDetail> details = new ArrayList<>();
            for (int i = 0; i < medicineIds.length; i++) {
                PrescriptionDetail detail = new PrescriptionDetail();
                detail.setMedicineId(Integer.parseInt(medicineIds[i]));
                detail.setMedicineType(medicineTypes[i]);
                detail.setDosage(new BigDecimal(dosages[i]));
                detail.setDosageUnit("g"); // 默认单位
                detail.setFrequency(frequencies[i]);
                detail.setUsageMethod(usageMethods[i]);
                detail.setDays(Integer.parseInt(days[i]));
                detail.setQuantity(new BigDecimal(quantities[i]));
                
                // 这里需要根据药品ID获取药品信息来设置价格等
                // 为了简化，暂时设置默认值
                detail.setUnitPrice(new BigDecimal("10.00"));
                detail.setTotalPrice(detail.getQuantity().multiply(detail.getUnitPrice()));
                
                details.add(detail);
            }
            
            boolean success = false;
            
            // 根据处方类型处理
            if ("中药处方".equals(prescriptionType)) {
                // 处理中药处方特殊属性
                ChinesePrescriptionAttr attr = new ChinesePrescriptionAttr();
                attr.setDecoctionMethod(request.getParameter("decoctionMethod"));
                attr.setDecoctionTimes(Integer.parseInt(request.getParameter("decoctionTimes")));
                attr.setUsageMethod(request.getParameter("chineseUsageMethod"));
                attr.setDosagePerTime(request.getParameter("dosagePerTime"));
                attr.setFrequency(request.getParameter("chineseFrequency"));
                
                success = prescriptionService.addChinesePrescription(prescription, details, attr);
            } else {
                success = prescriptionService.addPrescription(prescription, details);
            }
            
            if (success) {
                response.sendRedirect(request.getContextPath() + "/doctor/medical-records.jsp?success=prescription_added");
            } else {
                request.setAttribute("error", "处方开具失败，请重试！");
                request.getRequestDispatcher("/doctor/prescription.jsp?medicalRecordId=" + medicalRecordId).forward(request, response);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "处方开具失败：" + e.getMessage());
            request.getRequestDispatcher("/doctor/prescription.jsp").forward(request, response);
        }
    }
    
    /**
     * 查看处方详情
     */
    private void viewPrescription(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String prescriptionId = request.getParameter("id");
        if (prescriptionId == null || prescriptionId.isEmpty()) {
            response.sendRedirect(request.getContextPath() + "/doctor/index.jsp");
            return;
        }
        
        try {
            PrescriptionService prescriptionService = JspServiceUtil.getPrescriptionService(getServletContext());
            Prescription prescription = prescriptionService.findById(Integer.parseInt(prescriptionId));
            
            if (prescription != null) {
                // 获取处方明细
                List<PrescriptionDetail> details = prescriptionService.findDetailsByPrescriptionId(prescription.getId());
                prescription.setPrescriptionDetails(details);
                
                // 如果是中药处方，获取中药特殊属性
                if ("中药处方".equals(prescription.getPrescriptionType())) {
                    ChinesePrescriptionAttr attr = prescriptionService.findChineseAttrByPrescriptionId(prescription.getId());
                    prescription.setChinesePrescriptionAttr(attr);
                }
                
                request.setAttribute("prescription", prescription);
                request.getRequestDispatcher("/doctor/view-prescription.jsp").forward(request, response);
            } else {
                response.sendRedirect(request.getContextPath() + "/doctor/index.jsp");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect(request.getContextPath() + "/doctor/index.jsp");
        }
    }
    
    /**
     * 查看处方列表
     */
    private void listPrescriptions(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession();
        Doctor doctor = (Doctor) session.getAttribute("doctor");
        
        if (doctor == null) {
            response.sendRedirect(request.getContextPath() + "/doctor/login.jsp");
            return;
        }
        
        try {
            PrescriptionService prescriptionService = JspServiceUtil.getPrescriptionService(getServletContext());
            List<Prescription> prescriptions = prescriptionService.findByDoctorIdWithDetails(doctor.getId());
            
            request.setAttribute("prescriptions", prescriptions);
            request.getRequestDispatcher("/doctor/prescription-list.jsp").forward(request, response);
            
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect(request.getContextPath() + "/doctor/index.jsp");
        }
    }
    
    /**
     * 更新处方状态
     */
    private void updatePrescriptionStatus(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String prescriptionId = request.getParameter("id");
        String status = request.getParameter("status");
        
        if (prescriptionId == null || status == null) {
            response.sendRedirect(request.getContextPath() + "/doctor/index.jsp");
            return;
        }
        
        try {
            PrescriptionService prescriptionService = JspServiceUtil.getPrescriptionService(getServletContext());
            boolean success = prescriptionService.updateStatus(Integer.parseInt(prescriptionId), status);
            
            if (success) {
                response.sendRedirect(request.getContextPath() + "/PrescriptionServlet?action=list&success=status_updated");
            } else {
                response.sendRedirect(request.getContextPath() + "/PrescriptionServlet?action=list&error=status_update_failed");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect(request.getContextPath() + "/PrescriptionServlet?action=list&error=status_update_failed");
        }
    }
}
